const GPlayerImp = require('../classes/GPlayerImp');

// ==================== 配置区域 ====================
// 功能开关配置
const CONFIG = {
    // 是否启用时间段概率 (false = 始终使用默认配置)
    enableTimeSlots: false,

    // 是否启用角色专属配置 (true = 优先使用角色专属配置)
    enablePlayerSpecific: true,

    // 调试模式 (显示详细日志)
    debugMode: true
};

// 时间段配置
const timeSlots = {
    // 时间段名称: { start: 开始小时, end: 结束小时, description: 描述 }
    morning: { start: 6, end: 12, description: "早晨时段 (6:00-12:00)" },
    afternoon: { start: 12, end: 18, description: "下午时段 (12:00-18:00)" },
    evening: { start: 18, end: 24, description: "晚上时段 (18:00-24:00)" },
    night: { start: 0, end: 6, description: "深夜时段 (0:00-6:00)" }
};

// 角色专属配置表 (根据角色ID)
const playerSpecificRates = {
    // 角色ID: { reinforce: 炼器概率, property: 灌注概率 }
    "2705": {
        reinforce: {
            // 炼器概率 - 超高成功率
            0:  [1.0, 0.0, 0.0, 0.00001, 0.0],
            1:  [1.0, 0.0, 0.0, 0.00001, 0.0],
            2:  [1.0, 0.0, 0.0, 0.00001, 0.0],
            3:  [1.0, 0.0, 0.0, 0.00001, 0.0],
            4:  [1.0, 0.0, 0.0, 0.00001, 0.0],
            5:  [1.0, 0.0, 0.0, 0.00001, 0.0],
            6:  [1.0, 0.0, 0.0, 0.00001, 0.0],
            7: [-0.050000001, 0.0, 0.0, 1.051, 0.0],
            8:  [1.0, 0.0, 0.0, 0.00001, 0.0],
            9:  [1.0, 0.0, 0.0, 0.00001, 0.0],
            10: [1.0, 0.0, 0.0, 0.00001, 0.0],
            11: [1.0, 0.0, 0.0, 0.00001, 0.0],
            12: [1.0, 0.0, 0.0, 0.00001, 0.0],
            13: [1.0, 0.0, 0.0, 0.00001, 0.0],
            14: [1.0, 0.0, 0.0, 0.00001, 0.0],
            15: [1.0, 0.0, 0.0, 0.00001, 0.0],
            16: [1.0, 0.0, 0.0, 0.00001, 0.0],
            17: [1.0, 0.0, 0.0, 0.00001, 0.0],
            18: [1.0, 0.0, 0.0, 0.00001, 0.0],
            19: [1.0, 0.0, 0.0, 0.00001, 0.0],
            20: [1.0, 0.0, 0.0, 0.00001, 0.0],
        },
        property: {
            // 灌注概率 - 超高成功率
            0: [0.95, 0.0, 0.06, 0.0, 0.0],    // 95%成功 + 6%清除 = 1.01
            1: [0.90, 0.0, 0.11, 0.0, 0.0],    // 90%成功 + 11%清除 = 1.01
            2: [0.85, 0.0, 0.16, 0.0, 0.0],    // 85%成功 + 16%清除 = 1.01
            3: [0.80, 0.0, 0.21, 0.0, 0.0],    // 80%成功 + 21%清除 = 1.01
            4: [0.75, 0.0, 0.26, 0.0, 0.0],    // 75%成功 + 26%清除 = 1.01
            5: [0.70, 0.0, 0.31, 0.0, 0.0],    // 70%成功 + 31%清除 = 1.01
            6: [0.65, 0.0, 0.36, 0.0, 0.0],    // 65%成功 + 36%清除 = 1.01
            7: [0.60, 0.0, 0.41, 0.0, 0.0],    // 60%成功 + 41%清除 = 1.01
            8: [0.55, 0.0, 0.46, 0.0, 0.0],    // 55%成功 + 46%清除 = 1.01
            9: [0.50, 0.0, 0.51, 0.0, 0.0]     // 50%成功 + 51%清除 = 1.01
        }
    }
    // 可以添加更多角色ID...
};
// ==================== 配置区域结束 ====================

// 炼器概率表 - 按时间段配置
const reinforceRates = {
    // 默认概率表 (当没有匹配的时间段时使用)
    default: {
        // level: [成功率, 降级率, 清除属性率, 爆装率, 不降级率]
        0:  [0.85000002, 0.0, 0.0, 0.15099999, 0.0],
        1:  [0.60000002, 0.0, 0.0, 0.40099999, 0.0],
        2:  [0.55000001, 0.0, 0.0, 0.45100001, 0.0],
        3:  [0.5, 0.0, 0.0, 0.50099999, 0.0],
        4:  [0.5, 0.0, 0.0, 0.50099999, 0.0],
        5:  [0.40000001, 0.0, 0.0, 0.60100001, 0.0],
        6:  [0.34999999, 0.0, 0.0, 0.65100002, 0.0],
        7:  [0.25, 0.0, 0.0, 0.75099999, 0.0],
        8:  [0.2, 0.0, 0.0, 0.801, 0.0],
        9:  [0.15000001, 0.0, 0.0, 0.85100001, 0.0],
        10: [0.12, 0.0, 0.0, 0.88099998, 0.0],
        11: [0.1, 0.0, 0.0, 0.90100002, 0.0],
        12: [0.1, 0.0, 0.0, 0.90100002, 0.0],
        13: [0.050000001, 0.0, 0.0, 0.95099998, 0.0],
        14: [0.050000001, 0.0, 0.0, 0.95099998, 0.0],
        15: [0.0, 0.0, 0.0, 1.001, 0.0],
        16: [-0.050000001, 0.0, 0.0, 1.051, 0.0],
        17: [-0.1, 0.0, 0.0, 1.101, 0.0],
        18: [-0.15000001, 0.0, 0.0, 1.151, 0.0],
        19: [-0.2, 0.0, 0.0, 1.201, 0.0]
        // 修正概率 - 前4个概率总和必须等于1.01
        // 0:  [0.85, 0.0, 0.0, 0.16, 0.0],      // 85%成功 + 16%爆装 = 1.01
        // 1:  [0.60, 0.0, 0.0, 0.41, 0.0],      // 60%成功 + 41%爆装 = 1.01
        // 2:  [0.55, 0.0, 0.0, 0.46, 0.0],      // 55%成功 + 46%爆装 = 1.01
        // 3:  [0.50, 0.0, 0.0, 0.51, 0.0],      // 50%成功 + 51%爆装 = 1.01
        // 4:  [0.50, 0.0, 0.0, 0.51, 0.0],      // 50%成功 + 51%爆装 = 1.01
        // 5:  [0.40, 0.0, 0.0, 0.61, 0.0],      // 40%成功 + 61%爆装 = 1.01
        // 6:  [0.35, 0.0, 0.0, 0.66, 0.0],      // 35%成功 + 66%爆装 = 1.01
        // 7:  [0.25, 0.0, 0.0, 0.76, 0.0],      // 25%成功 + 76%爆装 = 1.01
        // 8:  [0.20, 0.0, 0.0, 0.81, 0.0],      // 20%成功 + 81%爆装 = 1.01
        // 9:  [0.15, 0.0, 0.0, 0.86, 0.0],      // 15%成功 + 86%爆装 = 1.01
        // 10: [0.12, 0.0, 0.0, 0.89, 0.0],      // 12%成功 + 89%爆装 = 1.01
        // 11: [0.10, 0.0, 0.0, 0.91, 0.0],      // 10%成功 + 91%爆装 = 1.01
        // 12: [0.10, 0.0, 0.0, 0.91, 0.0],      // 10%成功 + 91%爆装 = 1.01
        // 13: [0.05, 0.0, 0.0, 0.96, 0.0],      // 5%成功 + 96%爆装 = 1.01
        // 14: [0.05, 0.0, 0.0, 0.96, 0.0],      // 5%成功 + 96%爆装 = 1.01
        // 15: [0.00, 0.0, 0.0, 1.01, 0.0],      // 0%成功 + 101%爆装 = 1.01
        // 16: [0.00, 0.0, 0.0, 1.01, 0.0],      // 0%成功 + 101%爆装 = 1.01
        // 17: [0.00, 0.0, 0.0, 1.01, 0.0],      // 0%成功 + 101%爆装 = 1.01
        // 18: [0.00, 0.0, 0.0, 1.01, 0.0],      // 0%成功 + 101%爆装 = 1.01
        // 19: [0.00, 0.0, 0.0, 1.01, 0.0]       // 0%成功 + 101%爆装 = 1.01
        // 0:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 1:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 2:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 3:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 4:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 5:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 6:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 7:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 8:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 9:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 10: [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 11: [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 12: [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 13: [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 14: [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 15: [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 16: [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 17: [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 18: [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 19: [1.0, 0.0, 0.0, 0.00001, 0.0],
        // 20: [1.0, 0.0, 0.0, 0.00001, 0.0],
    },

    // 早晨时段 - 高成功率
    morning: {
        0:  [0.95000002, 0.0, 0.0, 0.05099999, 0.0],
        1:  [0.80000002, 0.0, 0.0, 0.20099999, 0.0],
        2:  [0.75000001, 0.0, 0.0, 0.25100001, 0.0],
        3:  [0.70, 0.0, 0.0, 0.30099999, 0.0],
        4:  [0.65, 0.0, 0.0, 0.35099999, 0.0],
        5:  [0.60000001, 0.0, 0.0, 0.40100001, 0.0],
        6:  [0.55999999, 0.0, 0.0, 0.44100002, 0.0],
        7:  [0.50, 0.0, 0.0, 0.50099999, 0.0],
        8:  [0.45, 0.0, 0.0, 0.551, 0.0],
        9:  [0.40000001, 0.0, 0.0, 0.60100001, 0.0],
        10: [0.35, 0.0, 0.0, 0.65099998, 0.0],
        11: [0.30, 0.0, 0.0, 0.70100002, 0.0],
        12: [0.25, 0.0, 0.0, 0.75100002, 0.0],
        13: [0.20000001, 0.0, 0.0, 0.80099998, 0.0],
        14: [0.15000001, 0.0, 0.0, 0.85099998, 0.0],
        15: [0.10, 0.0, 0.0, 0.901, 0.0],
        16: [0.05000001, 0.0, 0.0, 0.951, 0.0],
        17: [0.0, 0.0, 0.0, 1.001, 0.0],
        18: [0.0, 0.0, 0.0, 1.001, 0.0],
        19: [0.0, 0.0, 0.0, 1.001, 0.0]
    },

    // 下午时段 - 中等成功率
    afternoon: {
        0:  [0.90000002, 0.0, 0.0, 0.10099999, 0.0],
        1:  [0.70000002, 0.0, 0.0, 0.30099999, 0.0],
        2:  [0.65000001, 0.0, 0.0, 0.35100001, 0.0],
        3:  [0.60, 0.0, 0.0, 0.40099999, 0.0],
        4:  [0.55, 0.0, 0.0, 0.45099999, 0.0],
        5:  [0.50000001, 0.0, 0.0, 0.50100001, 0.0],
        6:  [0.44999999, 0.0, 0.0, 0.55100002, 0.0],
        7:  [0.35, 0.0, 0.0, 0.65099999, 0.0],
        8:  [0.30, 0.0, 0.0, 0.701, 0.0],
        9:  [0.25000001, 0.0, 0.0, 0.75100001, 0.0],
        10: [0.22, 0.0, 0.0, 0.78099998, 0.0],
        11: [0.20, 0.0, 0.0, 0.80100002, 0.0],
        12: [0.15, 0.0, 0.0, 0.85100002, 0.0],
        13: [0.10000001, 0.0, 0.0, 0.90099998, 0.0],
        14: [0.08000001, 0.0, 0.0, 0.92099998, 0.0],
        15: [0.05, 0.0, 0.0, 0.951, 0.0],
        16: [0.0, 0.0, 0.0, 1.001, 0.0],
        17: [-0.05, 0.0, 0.0, 1.051, 0.0],
        18: [-0.10000001, 0.0, 0.0, 1.101, 0.0],
        19: [-0.15, 0.0, 0.0, 1.151, 0.0]
    },

    // 晚上时段 - 低成功率
    evening: {
        0:  [0.75000002, 0.0, 0.0, 0.25099999, 0.0],
        1:  [0.50000002, 0.0, 0.0, 0.50099999, 0.0],
        2:  [0.45000001, 0.0, 0.0, 0.55100001, 0.0],
        3:  [0.40, 0.0, 0.0, 0.60099999, 0.0],
        4:  [0.35, 0.0, 0.0, 0.65099999, 0.0],
        5:  [0.30000001, 0.0, 0.0, 0.70100001, 0.0],
        6:  [0.24999999, 0.0, 0.0, 0.75100002, 0.0],
        7:  [0.15, 0.0, 0.0, 0.85099999, 0.0],
        8:  [0.10, 0.0, 0.0, 0.901, 0.0],
        9:  [0.05000001, 0.0, 0.0, 0.95100001, 0.0],
        10: [0.02, 0.0, 0.0, 0.98099998, 0.0],
        11: [0.0, 0.0, 0.0, 1.00100002, 0.0],
        12: [0.0, 0.0, 0.0, 1.00100002, 0.0],
        13: [-0.050000001, 0.0, 0.0, 1.05099998, 0.0],
        14: [-0.10000001, 0.0, 0.0, 1.10099998, 0.0],
        15: [-0.15, 0.0, 0.0, 1.151, 0.0],
        16: [-0.20000001, 0.0, 0.0, 1.201, 0.0],
        17: [-0.25, 0.0, 0.0, 1.251, 0.0],
        18: [-0.30000001, 0.0, 0.0, 1.301, 0.0],
        19: [-0.35, 0.0, 0.0, 1.351, 0.0]
    },

    // 深夜时段 - 极低成功率
    night: {
        // 0:  [0.70000002, 0.0, 0.0, 0.30099999, 0.0],
        // 1:  [0.00000002, 0.0, 0.0, 0.99999998, 0.0],  // 几乎100%爆装率
        // 2:  [0.35000001, 0.0, 0.0, 0.65100001, 0.0],
        // 3:  [0.30, 0.0, 0.0, 0.70099999, 0.0],
        // 4:  [0.25, 0.0, 0.0, 0.75099999, 0.0],
        // 5:  [0.20000001, 0.0, 0.0, 0.80100001, 0.0],
        // 6:  [0.14999999, 0.0, 0.0, 0.85100002, 0.0],
        // 7:  [0.05, 0.0, 0.0, 0.95099999, 0.0],
        // 8:  [0.0, 0.0, 0.0, 1.001, 0.0],
        // 9:  [-0.05000001, 0.0, 0.0, 1.05100001, 0.0],
        // 10: [-0.08, 0.0, 0.0, 1.08099998, 0.0],
        // 11: [-0.10, 0.0, 0.0, 1.10100002, 0.0],
        // 12: [-0.15, 0.0, 0.0, 1.15100002, 0.0],
        // 13: [-0.20000001, 0.0, 0.0, 1.20099998, 0.0],
        // 14: [-0.25000001, 0.0, 0.0, 1.25099998, 0.0],
        // 15: [-0.30, 0.0, 0.0, 1.301, 0.0],
        // 16: [-0.35000001, 0.0, 0.0, 1.351, 0.0],
        // 17: [-0.40, 0.0, 0.0, 1.401, 0.0],
        // 18: [-0.45000001, 0.0, 0.0, 1.451, 0.0],
        // 19: [-0.50, 0.0, 0.0, 1.501, 0.0]
        // 从C++代码转换的原版概率
        // 0:  [0.85, 0.0, 0.0, 0.15001, 0.0],
        // 1:  [0.6, 0.0, 0.0, 0.40001, 0.0],
        // 2:  [0.55, 0.0, 0.0, 0.45001, 0.0],
        // 3:  [0.5, 0.0, 0.0, 0.50001, 0.0],
        // 4:  [0.5, 0.0, 0.0, 0.50001, 0.0],
        // 5:  [0.4, 0.0, 0.0, 0.60001, 0.0],
        // 6:  [0.35, 0.0, 0.0, 0.65001, 0.0],
        // 7:  [0.25, 0.0, 0.0, 0.75001, 0.0],
        // 8:  [0.2, 0.0, 0.0, 0.80001, 0.0],
        // 9:  [0.15, 0.0, 0.0, 0.85001, 0.0],
        // 10: [0.12, 0.0, 0.0, 0.88001, 0.0],
        // 11: [0.1, 0.0, 0.0, 0.90001, 0.0],
        // 12: [0.1, 0.0, 0.0, 0.90001, 0.0],
        // 13: [0.05, 0.0, 0.0, 0.95001, 0.0],
        // 14: [0.05, 0.0, 0.0, 0.95001, 0.0],
        // 15: [0.0, 0.0, 0.0, 1.00001, 0.0],
        // 16: [-0.05, 0.0, 0.0, 1.05001, 0.0],
        // 17: [-0.14, 0.0, 0.0, 1.14001, 0.0],
        // 18: [-0.25, 0.0, 0.0, 1.25001, 0.0],
        // 19: [-0.35, 0.0, 0.0, 1.35001, 0.0]

        0:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        1:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        2:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        3:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        4:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        5:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        6:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        7:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        8:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        9:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        10: [1.0, 0.0, 0.0, 0.00001, 0.0],
        11: [1.0, 0.0, 0.0, 0.00001, 0.0],
        12: [1.0, 0.0, 0.0, 0.00001, 0.0],
        13: [1.0, 0.0, 0.0, 0.00001, 0.0],
        14: [1.0, 0.0, 0.0, 0.00001, 0.0],
        15: [1.0, 0.0, 0.0, 0.00001, 0.0],
        16: [1.0, 0.0, 0.0, 0.00001, 0.0],
        17: [1.0, 0.0, 0.0, 0.00001, 0.0],
        18: [1.0, 0.0, 0.0, 0.00001, 0.0],
        19: [1.0, 0.0, 0.0, 0.00001, 0.0]
    }
};

// 灌注概率表 - 按时间段配置
const propertyRates = {
    // 默认概率表 (修正概率总和为1.01)
    default: {
        // level: [成功率, 降级率, 清除属性率, 爆装率, 不降级率] - 前4个总和=1.01
        0: [0.80, 0.0, 0.21, 0.0, 0.0],      // 80%成功 + 21%清除 = 1.01
        1: [0.70, 0.0, 0.31, 0.0, 0.0],      // 70%成功 + 31%清除 = 1.01
        2: [0.60, 0.0, 0.41, 0.0, 0.0],      // 60%成功 + 41%清除 = 1.01
        3: [0.50, 0.0, 0.51, 0.0, 0.0],      // 50%成功 + 51%清除 = 1.01
        4: [0.40, 0.0, 0.61, 0.0, 0.0],      // 40%成功 + 61%清除 = 1.01
        5: [0.35, 0.0, 0.66, 0.0, 0.0],      // 35%成功 + 66%清除 = 1.01
        6: [0.35, 0.0, 0.66, 0.0, 0.0],      // 35%成功 + 66%清除 = 1.01
        7: [0.30, 0.0, 0.71, 0.0, 0.0],      // 30%成功 + 71%清除 = 1.01
        8: [0.30, 0.0, 0.71, 0.0, 0.0],      // 30%成功 + 71%清除 = 1.01
        9: [0.25, 0.0, 0.76, 0.0, 0.0]       // 25%成功 + 76%清除 = 1.01
    },

    // 早晨时段 - 高成功率
    morning: {
        0: [0.95000001, 0.0, 0.05100001, 0.0, 0.0],
        1: [0.89999999, 0.0, 0.101, 0.0, 0.0],
        2: [0.85000002, 0.0, 0.15099999, 0.0, 0.0],
        3: [0.80, 0.0, 0.20099999, 0.0, 0.0],
        4: [0.75000001, 0.0, 0.25100001, 0.0, 0.0],
        5: [0.69999999, 0.0, 0.30100002, 0.0, 0.0],
        6: [0.64999999, 0.0, 0.35100002, 0.0, 0.0],
        7: [0.60000001, 0.0, 0.40099998, 0.0, 0.0],
        8: [0.55000001, 0.0, 0.45099998, 0.0, 0.0],
        9: [0.50, 0.0, 0.50099999, 0.0, 0.0]
    },

    // 下午时段 - 中等成功率
    afternoon: {
        0: [0.90000001, 0.0, 0.10100001, 0.0, 0.0],
        1: [0.79999999, 0.0, 0.201, 0.0, 0.0],
        2: [0.70000002, 0.0, 0.30099999, 0.0, 0.0],
        3: [0.60, 0.0, 0.40099999, 0.0, 0.0],
        4: [0.50000001, 0.0, 0.50100001, 0.0, 0.0],
        5: [0.44999999, 0.0, 0.55100002, 0.0, 0.0],
        6: [0.39999999, 0.0, 0.60100002, 0.0, 0.0],
        7: [0.35000001, 0.0, 0.65099998, 0.0, 0.0],
        8: [0.30000001, 0.0, 0.70099998, 0.0, 0.0],
        9: [0.25, 0.0, 0.75099999, 0.0, 0.0]
    },

    // 晚上时段 - 低成功率
    evening: {
        0: [0.70000001, 0.0, 0.30100001, 0.0, 0.0],
        1: [0.59999999, 0.0, 0.401, 0.0, 0.0],
        2: [0.50000002, 0.0, 0.50099999, 0.0, 0.0],
        3: [0.40, 0.0, 0.60099999, 0.0, 0.0],
        4: [0.30000001, 0.0, 0.70100001, 0.0, 0.0],
        5: [0.24999999, 0.0, 0.75100002, 0.0, 0.0],
        6: [0.19999999, 0.0, 0.80100002, 0.0, 0.0],
        7: [0.15000001, 0.0, 0.85099998, 0.0, 0.0],
        8: [0.10000001, 0.0, 0.90099998, 0.0, 0.0],
        9: [0.05, 0.0, 0.95099999, 0.0, 0.0]
    },

    // 深夜时段 - 极低成功率
    night: {
        0: [0.60000001, 0.0, 0.40100001, 0.0, 0.0],
        1: [0.00000009, 0.0, 0.501, 0.0, 0.0],
        2: [0.40000002, 0.0, 0.60099999, 0.0, 0.0],
        3: [0.30, 0.0, 0.70099999, 0.0, 0.0],
        4: [0.20000001, 0.0, 0.80100001, 0.0, 0.0],
        5: [0.14999999, 0.0, 0.85100002, 0.0, 0.0],
        6: [0.09999999, 0.0, 0.90100002, 0.0, 0.0],
        7: [0.05000001, 0.0, 0.95099998, 0.0, 0.0],
        8: [0.0, 0.0, 1.00099998, 0.0, 0.0],
        9: [0.0, 0.0, 1.00099999, 0.0, 0.0]
    }
};



// 修改强化概率的函数
function modifyReinforceRate(level, rates) {
    const reinforce_table = ptr('0x091E8A00');
    const entry = reinforce_table.add((level - 1) * 24);

    entry.add(4).writeFloat(rates[0]);   // 成功率
    entry.add(8).writeFloat(rates[1]);   // 降级率
    entry.add(12).writeFloat(rates[2]);  // 清除属性率
    entry.add(16).writeFloat(rates[3]);  // 爆装率
    entry.add(20).writeFloat(rates[4]);  // 不降级率

    console.log(`炼器等级${level+1}的强化概率: 成功率=${(rates[0]*100).toFixed(0)}%, 清除率=${(rates[2]*100).toFixed(0)}%, 爆装率=${(rates[3]*100).toFixed(0)}%`);
}

// 从 gactive_imp 指针获取角色ID (直接内存读取)
function getPlayerIdFromGActiveImp(gactiveImpPtr) {
    try {
        if (!gactiveImpPtr || gactiveImpPtr.isNull()) {
            if (CONFIG.debugMode) {
                console.log(`[调试] gactive_imp 指针为空`);
            }
            return null;
        }

        if (CONFIG.debugMode) {
            console.log(`[调试] gactive_imp 指针: ${gactiveImpPtr}`);
        }

        // 根据 gplayer_imp 的结构，尝试不同的偏移来找到角色ID
        const offsets = [
            0x40,   // 直接偏移
            0x44,   // 可能的其他偏移
            0x48,   //
            0x4C,   //
            0x50,   //
            0x54,   //
            0x58,   //
            0x5C,   //
            0x60,   //
            0x64,   //
            0x68,   //
            0x6C,   //
            0x70,   //
            0x74,   //
            0x78,   //
            0x7C,   //
            0x80,   //
        ];

        for (let i = 0; i < offsets.length; i++) {
            try {
                const offset = offsets[i];
                const playerId = gactiveImpPtr.add(offset).readInt();

                if (CONFIG.debugMode) {
                    console.log(`[调试] 偏移 0x${offset.toString(16)}: ${playerId}`);
                }

                // 检查是否是合理的角色ID (通常是正整数，不会太大)
                if (playerId && playerId > 0 && playerId < 1000000) {
                    console.log(`[成功] 在偏移 0x${offset.toString(16)} 找到角色ID: ${playerId}`);
                    return playerId.toString();
                }
            } catch (e) {
                if (CONFIG.debugMode) {
                    console.log(`[调试] 偏移 0x${offsets[i].toString(16)} 读取失败: ${e}`);
                }
            }
        }

        if (CONFIG.debugMode) {
            console.log(`[调试] 所有偏移都未找到有效角色ID`);
        }
        return null;

    } catch (e) {
        if (CONFIG.debugMode) {
            console.error(`[调试] 从gactive_imp获取角色ID失败:`, e);
        }
        return null;
    }
}

// 修改灌注概率的函数
function modifyPropertyRate(level, rates) {
    const property_table = ptr('0x091E8EE0');
    const entry = property_table.add((level - 1) * 24);

    entry.add(4).writeFloat(rates[0]);   // 成功率
    entry.add(8).writeFloat(rates[1]);   // 降级率
    entry.add(12).writeFloat(rates[2]);  // 清除属性率
    entry.add(16).writeFloat(rates[3]);  // 爆装率
    entry.add(20).writeFloat(rates[4]);  // 不降级率

    console.log(`灌注等级${level+1}的概率: 成功率=${(rates[0]*100).toFixed(0)}%, 清除率=${(rates[2]*100).toFixed(0)}%`);
}


// 手动设置时间段 (用于测试或特殊需求)
let manualTimeSlot = null;

// 获取当前角色ID (直接从内存读取)
function getCurrentPlayerId() {
    try {
        // 方法1: 尝试从已知的内存地址读取当前角色ID
        // 这个地址需要根据具体游戏调整
        try {
            // 假设有一个全局的当前玩家指针
            const currentPlayerPtr = ptr('0x????????').readPointer(); // 需要找到正确的地址
            if (currentPlayerPtr && !currentPlayerPtr.isNull()) {
                const playerId = currentPlayerPtr.add(0x40).readInt();
                if (CONFIG.debugMode) {
                    console.log(`[调试] 从内存获取角色ID: ${playerId}`);
                }
                if (playerId && playerId > 0) {
                    return playerId.toString();
                }
            }
        } catch (e1) {
            if (CONFIG.debugMode) {
                console.log(`[调试] 方法1失败: ${e1}`);
            }
        }

        // 方法2: 暂时返回null，让Hook函数来处理
        if (CONFIG.debugMode) {
            console.log(`[调试] getCurrentPlayerId 返回null，等待Hook获取`);
        }
        return null;

    } catch (e) {
        if (CONFIG.debugMode) {
            console.error(`[调试] 获取角色ID失败:`, e);
        }
        return null;
    }
}

// 选择合适的概率配置 (通用版本)
function selectRatesConfig(type) {
    try {
        // 1. 优先检查角色专属配置
        if (CONFIG.enablePlayerSpecific) {
            const playerId = getCurrentPlayerId();
            if (playerId && playerSpecificRates[playerId]) {
                const playerConfig = playerSpecificRates[playerId];
                console.log(`[配置] 使用角色专属配置 (ID: ${playerId})`);
                return type === 'reinforce' ? playerConfig.reinforce : playerConfig.property;
            }
        }

        // 2. 检查时间段配置
        if (CONFIG.enableTimeSlots) {
            const currentTimeSlot = getCurrentTimeSlot();
            const timeSlotRates = type === 'reinforce' ? reinforceRates[currentTimeSlot] : propertyRates[currentTimeSlot];
            if (timeSlotRates) {
                const timeSlotDesc = getTimeSlotDescription(currentTimeSlot);
                console.log(`[配置] 使用时间段配置: ${timeSlotDesc}`);
                return timeSlotRates;
            }
        }

        // 3. 使用默认配置
        console.log(`[配置] 使用默认配置`);
        return type === 'reinforce' ? reinforceRates.default : propertyRates.default;

    } catch (e) {
        console.error(`[配置] 选择配置失败，使用默认配置:`, e);
        return type === 'reinforce' ? reinforceRates.default : propertyRates.default;
    }
}

// 为特定角色选择概率配置 (Hook专用版本)
function selectRatesConfigForPlayer(type, playerId) {
    try {
        // 1. 优先检查角色专属配置
        if (CONFIG.enablePlayerSpecific && playerId && playerSpecificRates[playerId]) {
            const playerConfig = playerSpecificRates[playerId];
            if (CONFIG.debugMode) {
                console.log(`[配置] 使用角色专属配置 (ID: ${playerId})`);
            }
            return type === 'reinforce' ? playerConfig.reinforce : playerConfig.property;
        }

        // 2. 检查时间段配置
        if (CONFIG.enableTimeSlots) {
            const currentTimeSlot = getCurrentTimeSlot();
            const timeSlotRates = type === 'reinforce' ? reinforceRates[currentTimeSlot] : propertyRates[currentTimeSlot];
            if (timeSlotRates) {
                if (CONFIG.debugMode) {
                    const timeSlotDesc = getTimeSlotDescription(currentTimeSlot);
                    console.log(`[配置] 使用时间段配置: ${timeSlotDesc}`);
                }
                return timeSlotRates;
            }
        }

        // 3. 使用默认配置
        if (CONFIG.debugMode) {
            console.log(`[配置] 使用默认配置`);
        }
        return type === 'reinforce' ? reinforceRates.default : propertyRates.default;

    } catch (e) {
        if (CONFIG.debugMode) {
            console.error(`[配置] 选择配置失败，使用默认配置:`, e);
        }
        return type === 'reinforce' ? reinforceRates.default : propertyRates.default;
    }
}

// 获取时间段描述
function getTimeSlotDescription(timeSlot) {
    const descriptions = {
        morning: "早晨时段 (6:00-12:00)",
        afternoon: "下午时段 (12:00-18:00)",
        evening: "晚上时段 (18:00-24:00)",
        night: "深夜时段 (0:00-6:00)",
        default: "默认时段"
    };
    return descriptions[timeSlot] || descriptions.default;
}

// 测试角色ID获取
function testPlayerIdRetrieval() {
    console.log("\n=== 测试角色ID获取 ===");

    try {
        const playerId = getCurrentPlayerId();
        console.log(`通过getCurrentPlayerId获取: ${playerId}`);

        // 检查是否有专属配置
        if (playerId && playerSpecificRates[playerId]) {
            console.log(`找到角色 ${playerId} 的专属配置`);
            console.log(`炼器配置等级数: ${Object.keys(playerSpecificRates[playerId].reinforce).length}`);
            console.log(`灌注配置等级数: ${Object.keys(playerSpecificRates[playerId].property).length}`);
        } else {
            console.log(`角色 ${playerId} 没有专属配置`);
        }

        // 测试配置应用
        console.log("测试配置应用...");
        checkAndApplyPlayerConfig();

    } catch (e) {
        console.error("测试角色ID获取失败:", e);
    }

    console.log("========================\n");
}

// Hook ReinforceEquipment 函数来实时获取角色ID并应用配置
function hookReinforceEquipment() {
    try {
        // ReinforceEquipment 函数地址 (从你提供的汇编代码)
        const reinforceEquipmentAddr = ptr('0x0889A33A');

        console.log(`[Hook] Hook ReinforceEquipment 函数: ${reinforceEquipmentAddr}`);

        Interceptor.attach(reinforceEquipmentAddr, {
            onEnter: function(args) {
                try {
                    // 根据函数签名: equip_item *this, item *a2, gactive_imp *a3, ...
                    // args[0] = this (equip_item*)
                    // args[1] = a2 (item*)
                    // args[2] = a3 (gactive_imp*)
                    const gactiveImpPtr = args[2];

                    if (CONFIG.debugMode) {
                        console.log(`[Hook] ReinforceEquipment 被调用，gactive_imp: ${gactiveImpPtr}`);
                    }

                    // 从 gactive_imp 获取角色ID
                    const playerId = getPlayerIdFromGActiveImp(gactiveImpPtr);

                    if (CONFIG.debugMode) {
                        console.log(`[Hook] 获取到角色ID: ${playerId}`);
                    }

                    // 如果有专属配置，立即应用
                    if (playerId && playerSpecificRates[playerId]) {
                        console.log(`[Hook] 为角色 ${playerId} 应用专属强化配置`);

                        // 应用炼器专属配置
                        const reinforceConfig = playerSpecificRates[playerId].reinforce;
                        Object.entries(reinforceConfig).forEach(([level, rates]) => {
                            modifyReinforceRate(parseInt(level), rates);
                        });

                        // 应用灌注专属配置
                        const propertyConfig = playerSpecificRates[playerId].property;
                        Object.entries(propertyConfig).forEach(([level, rates]) => {
                            modifyPropertyRate(parseInt(level), rates);
                        });
                    } else {
                        if (CONFIG.debugMode) {
                            console.log(`[Hook] 角色 ${playerId} 没有专属配置，使用默认配置`);
                        }
                        // 应用默认配置
                        applyReinforceRatesDefault();
                        applyPropertyRatesDefault();
                    }

                } catch (e) {
                    if (CONFIG.debugMode) {
                        console.error("[Hook] ReinforceEquipment Hook 错误:", e);
                    }
                }
            }
        });

        console.log("[Hook] ReinforceEquipment Hook 安装成功");

    } catch (e) {
        console.error("[Hook] 安装 ReinforceEquipment Hook 失败:", e);
        // 回退到默认配置
        applyReinforceRatesDefault();
        applyPropertyRatesDefault();
    }
}

function checkAndApplyPlayerConfig() {
    try {
        const playerId = getCurrentPlayerId();

        if (CONFIG.debugMode) {
            console.log(`[检查] 当前角色ID: ${playerId}`);
        }

        if (playerId && playerSpecificRates[playerId]) {
            console.log(`[配置] 检测到角色 ${playerId}，应用专属配置`);

            // 应用炼器专属配置
            const reinforceConfig = playerSpecificRates[playerId].reinforce;
            Object.entries(reinforceConfig).forEach(([level, rates]) => {
                modifyReinforceRate(parseInt(level), rates);
            });

            // 应用灌注专属配置
            const propertyConfig = playerSpecificRates[playerId].property;
            Object.entries(propertyConfig).forEach(([level, rates]) => {
                modifyPropertyRate(parseInt(level), rates);
            });

            console.log(`[配置] 角色 ${playerId} 专属配置应用完成`);
        }

    } catch (e) {
        if (CONFIG.debugMode) {
            console.error("[检查] 检查角色配置失败:", e);
        }
    }
}

// 应用炼器概率修改 (默认方式)
function applyReinforceRatesDefault() {
    try {
        console.log("[强化概率修改] 开始修改炼器等级概率...");

        // 选择合适的配置
        const ratesConfig = selectRatesConfig('reinforce');

        Object.entries(ratesConfig).forEach(([level, rates]) => {
            modifyReinforceRate(parseInt(level), rates);
        });

        console.log("[强化概率修改] 炼器概率修改完成");
    } catch(e) {
        console.error("[强化概率修改] 修改失败:", e);
    }
}

// 应用灌注概率修改 (默认方式)
function applyPropertyRatesDefault() {
    try {
        console.log("[强化概率修改] 开始修改灌注等级概率...");

        // 选择合适的配置
        const ratesConfig = selectRatesConfig('property');

        Object.entries(ratesConfig).forEach(([level, rates]) => {
            modifyPropertyRate(parseInt(level), rates);
        });

        console.log("[强化概率修改] 灌注概率修改完成");
    } catch(e) {
        console.error("[强化概率修改] 修改失败:", e);
    }
}

// 应用炼器概率修改 (新的入口函数)
function applyReinforceRates() {
    if (CONFIG.enablePlayerSpecific) {
        // 如果启用角色专属配置，使用Hook方式
        hookReinforceEquipment();
    } else {
        // 否则使用默认方式
        applyReinforceRatesDefault();
    }
}

// 应用灌注概率修改
function applyPropertyRates() {
    try {
        console.log("[强化概率修改] 开始修改灌注等级概率...");

        // 选择合适的配置
        const ratesConfig = selectRatesConfig('property');

        Object.entries(ratesConfig).forEach(([level, rates]) => {
            modifyPropertyRate(parseInt(level), rates);
        });

        console.log("[强化概率修改] 灌注概率修改完成");
    } catch(e) {
        console.error("[强化概率修改] 修改失败:", e);
    }
}




// 设置手动时间段
function setManualTimeSlot(timeSlot) {
    if (timeSlot === null || ['morning', 'afternoon', 'evening', 'night', 'default'].includes(timeSlot)) {
        manualTimeSlot = timeSlot;
        console.log(`[强化概率修改] 手动时间段设置为: ${timeSlot ? getTimeSlotDescription(timeSlot) : '自动模式'}`);

        // 立即应用新的概率设置
        applyReinforceRates();
        applyPropertyRates();

        return true;
    } else {
        console.error(`[强化概率修改] 无效的时间段: ${timeSlot}`);
        return false;
    }
}

// 获取当前时间段 (考虑手动设置)
function getCurrentTimeSlot() {
    if (manualTimeSlot !== null) {
        return manualTimeSlot;
    }

    const now = new Date();
    const hour = now.getHours();

    if (hour >= 6 && hour < 12) {
        return 'morning';
    } else if (hour >= 12 && hour < 18) {
        return 'afternoon';
    } else if (hour >= 18 && hour < 24) {
        return 'evening';
    } else {
        return 'night';
    }
}

// 显示当前概率信息
function showCurrentRates() {
    const currentTimeSlot = getCurrentTimeSlot();
    const timeSlotDesc = getTimeSlotDescription(currentTimeSlot);

    console.log(`\n=== 当前强化概率信息 ===`);
    console.log(`时间段: ${timeSlotDesc}`);
    console.log(`手动模式: ${manualTimeSlot !== null ? '是' : '否'}`);

    const reinforceTable = reinforceRates[currentTimeSlot] || reinforceRates.default;
    const propertyTable = propertyRates[currentTimeSlot] || propertyRates.default;

    console.log(`\n炼器概率 (前5级):`);
    for (let i = 0; i < 5; i++) {
        const rates = reinforceTable[i];
        if (rates) {
            console.log(`  等级${i+1}: 成功率=${(rates[0]*100).toFixed(1)}%, 爆装率=${(rates[3]*100).toFixed(1)}%`);
        }
    }

    console.log(`\n灌注概率 (前5级):`);
    for (let i = 0; i < 5; i++) {
        const rates = propertyTable[i];
        if (rates) {
            console.log(`  等级${i+1}: 成功率=${(rates[0]*100).toFixed(1)}%, 清除率=${(rates[2]*100).toFixed(1)}%`);
        }
    }
    console.log(`========================\n`);
}

// 定时检查并更新概率 (每小时检查一次)
let autoUpdateInterval = null;

function startAutoUpdate() {
    if (autoUpdateInterval) {
        clearInterval(autoUpdateInterval);
    }

    // 每小时检查一次时间段变化
    autoUpdateInterval = setInterval(() => {
        if (manualTimeSlot === null) { // 只在自动模式下更新
            const currentTimeSlot = getCurrentTimeSlot();
            console.log(`[强化概率修改] 定时检查 - 当前时间段: ${getTimeSlotDescription(currentTimeSlot)}`);
            applyReinforceRates();
            applyPropertyRates();
        }
    }, 60 * 60 * 1000); // 1小时

    console.log("[强化概率修改] 自动更新已启动 (每小时检查一次)");
}

function stopAutoUpdate() {
    if (autoUpdateInterval) {
        clearInterval(autoUpdateInterval);
        autoUpdateInterval = null;
        console.log("[强化概率修改] 自动更新已停止");
    }
}

// 配置管理函数
function setConfig(key, value) {
    if (CONFIG.hasOwnProperty(key)) {
        CONFIG[key] = value;
        console.log(`[配置] ${key} 设置为: ${value}`);

        // 配置改变后重新应用概率
        applyReinforceRates();
        applyPropertyRates();

        return true;
    } else {
        console.error(`[配置] 无效的配置项: ${key}`);
        return false;
    }
}

function getConfig() {
    return { ...CONFIG };
}

function addPlayerConfig(playerId, reinforceRates, propertyRates) {
    playerSpecificRates[playerId.toString()] = {
        reinforce: reinforceRates,
        property: propertyRates
    };
    console.log(`[配置] 已添加角色专属配置 (ID: ${playerId})`);
}

function removePlayerConfig(playerId) {
    const playerIdStr = playerId.toString();
    if (playerSpecificRates[playerIdStr]) {
        delete playerSpecificRates[playerIdStr];
        console.log(`[配置] 已删除角色专属配置 (ID: ${playerId})`);
        return true;
    } else {
        console.log(`[配置] 未找到角色配置: ID ${playerId}`);
        return false;
    }
}

function listPlayerConfigs() {
    console.log("\n=== 角色专属配置列表 ===");
    const configs = Object.entries(playerSpecificRates);
    if (configs.length === 0) {
        console.log("暂无角色专属配置");
    } else {
        configs.forEach(([playerId]) => {
            console.log(`角色ID: ${playerId}`);
        });
    }
    console.log("========================\n");
}

// 测试系统功能
function testSystem() {
    console.log("\n=== 强化概率系统测试 ===");

    // 测试配置获取
    console.log("当前配置:", getConfig());

    // 测试角色ID获取
    const playerId = getCurrentPlayerId();
    console.log("当前角色ID:", playerId);

    // 测试时间段获取
    const timeSlot = getCurrentTimeSlot();
    console.log("当前时间段:", timeSlot, "-", getTimeSlotDescription(timeSlot));

    // 测试配置选择
    const reinforceConfig = selectRatesConfig('reinforce');
    const propertyConfig = selectRatesConfig('property');

    console.log("选择的炼器配置:", Object.keys(reinforceConfig).length, "个等级");
    console.log("选择的灌注配置:", Object.keys(propertyConfig).length, "个等级");

    // 显示前3级的概率
    console.log("\n前3级炼器概率:");
    for (let i = 0; i < 3; i++) {
        if (reinforceConfig[i]) {
            const rates = reinforceConfig[i];
            const total = rates[0] + rates[1] + rates[2] + rates[3];
            console.log(`  等级${i+1}: 成功=${(rates[0]*100).toFixed(1)}%, 爆装=${(rates[3]*100).toFixed(1)}%, 总和=${total.toFixed(3)}`);
        }
    }

    console.log("\n前3级灌注概率:");
    for (let i = 0; i < 3; i++) {
        if (propertyConfig[i]) {
            const rates = propertyConfig[i];
            const total = rates[0] + rates[1] + rates[2] + rates[3];
            console.log(`  等级${i+1}: 成功=${(rates[0]*100).toFixed(1)}%, 清除=${(rates[2]*100).toFixed(1)}%, 总和=${total.toFixed(3)}`);
        }
    }

    console.log("========================\n");
}

module.exports = {
    applyReinforceRates,
    applyPropertyRates,
    setManualTimeSlot,
    showCurrentRates,
    startAutoUpdate,
    stopAutoUpdate,
    getCurrentTimeSlot,
    getTimeSlotDescription,
    // 新增的配置管理函数
    setConfig,
    getConfig,
    addPlayerConfig,
    removePlayerConfig,
    listPlayerConfigs,
    testSystem,
    testPlayerIdRetrieval
};