// 时间段配置
const timeSlots = {
    // 时间段名称: { start: 开始小时, end: 结束小时, description: 描述 }
    morning: { start: 6, end: 12, description: "早晨时段 (6:00-12:00)" },
    afternoon: { start: 12, end: 18, description: "下午时段 (12:00-18:00)" },
    evening: { start: 18, end: 24, description: "晚上时段 (18:00-24:00)" },
    night: { start: 0, end: 6, description: "深夜时段 (0:00-6:00)" }
};

// 炼器概率表 - 按时间段配置
const reinforceRates = {
    // 默认概率表 (当没有匹配的时间段时使用)
    default: {
        // level: [成功率, 降级率, 清除属性率, 爆装率, 不降级率]
        // 0:  [0.85000002, 0.0, 0.0, 0.15099999, 0.0],
        // 1:  [0.60000002, 0.0, 0.0, 0.40099999, 0.0],
        // 2:  [0.55000001, 0.0, 0.0, 0.45100001, 0.0],
        // 3:  [0.5, 0.0, 0.0, 0.50099999, 0.0],
        // 4:  [0.5, 0.0, 0.0, 0.50099999, 0.0],
        // 5:  [0.40000001, 0.0, 0.0, 0.60100001, 0.0],
        // 6:  [0.34999999, 0.0, 0.0, 0.65100002, 0.0],
        // 7:  [0.25, 0.0, 0.0, 0.75099999, 0.0],
        // 8:  [0.2, 0.0, 0.0, 0.801, 0.0],
        // 9:  [0.15000001, 0.0, 0.0, 0.85100001, 0.0],
        // 10: [0.12, 0.0, 0.0, 0.88099998, 0.0],
        // 11: [0.1, 0.0, 0.0, 0.90100002, 0.0],
        // 12: [0.1, 0.0, 0.0, 0.90100002, 0.0],
        // 13: [0.050000001, 0.0, 0.0, 0.95099998, 0.0],
        // 14: [0.050000001, 0.0, 0.0, 0.95099998, 0.0],
        // 15: [0.0, 0.0, 0.0, 1.001, 0.0],
        // 16: [-0.050000001, 0.0, 0.0, 1.051, 0.0],
        // 17: [-0.1, 0.0, 0.0, 1.101, 0.0],
        // 18: [-0.15000001, 0.0, 0.0, 1.151, 0.0],
        // 19: [-0.2, 0.0, 0.0, 1.201, 0.0]
        0:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        1:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        2:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        3:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        4:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        5:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        6:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        7:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        8:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        9:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        10: [1.0, 0.0, 0.0, 0.00001, 0.0],
        11: [1.0, 0.0, 0.0, 0.00001, 0.0],
        12: [1.0, 0.0, 0.0, 0.00001, 0.0],
        13: [1.0, 0.0, 0.0, 0.00001, 0.0],
        14: [1.0, 0.0, 0.0, 0.00001, 0.0],
        15: [1.0, 0.0, 0.0, 0.00001, 0.0],
        16: [1.0, 0.0, 0.0, 0.00001, 0.0],
        17: [1.0, 0.0, 0.0, 0.00001, 0.0],
        18: [1.0, 0.0, 0.0, 0.00001, 0.0],
        19: [1.0, 0.0, 0.0, 0.00001, 0.0]
    },

    // 早晨时段 - 高成功率
    morning: {
        0:  [0.95000002, 0.0, 0.0, 0.05099999, 0.0],
        1:  [0.80000002, 0.0, 0.0, 0.20099999, 0.0],
        2:  [0.75000001, 0.0, 0.0, 0.25100001, 0.0],
        3:  [0.70, 0.0, 0.0, 0.30099999, 0.0],
        4:  [0.65, 0.0, 0.0, 0.35099999, 0.0],
        5:  [0.60000001, 0.0, 0.0, 0.40100001, 0.0],
        6:  [0.55999999, 0.0, 0.0, 0.44100002, 0.0],
        7:  [0.50, 0.0, 0.0, 0.50099999, 0.0],
        8:  [0.45, 0.0, 0.0, 0.551, 0.0],
        9:  [0.40000001, 0.0, 0.0, 0.60100001, 0.0],
        10: [0.35, 0.0, 0.0, 0.65099998, 0.0],
        11: [0.30, 0.0, 0.0, 0.70100002, 0.0],
        12: [0.25, 0.0, 0.0, 0.75100002, 0.0],
        13: [0.20000001, 0.0, 0.0, 0.80099998, 0.0],
        14: [0.15000001, 0.0, 0.0, 0.85099998, 0.0],
        15: [0.10, 0.0, 0.0, 0.901, 0.0],
        16: [0.05000001, 0.0, 0.0, 0.951, 0.0],
        17: [0.0, 0.0, 0.0, 1.001, 0.0],
        18: [0.0, 0.0, 0.0, 1.001, 0.0],
        19: [0.0, 0.0, 0.0, 1.001, 0.0]
    },

    // 下午时段 - 中等成功率
    afternoon: {
        0:  [0.90000002, 0.0, 0.0, 0.10099999, 0.0],
        1:  [0.70000002, 0.0, 0.0, 0.30099999, 0.0],
        2:  [0.65000001, 0.0, 0.0, 0.35100001, 0.0],
        3:  [0.60, 0.0, 0.0, 0.40099999, 0.0],
        4:  [0.55, 0.0, 0.0, 0.45099999, 0.0],
        5:  [0.50000001, 0.0, 0.0, 0.50100001, 0.0],
        6:  [0.44999999, 0.0, 0.0, 0.55100002, 0.0],
        7:  [0.35, 0.0, 0.0, 0.65099999, 0.0],
        8:  [0.30, 0.0, 0.0, 0.701, 0.0],
        9:  [0.25000001, 0.0, 0.0, 0.75100001, 0.0],
        10: [0.22, 0.0, 0.0, 0.78099998, 0.0],
        11: [0.20, 0.0, 0.0, 0.80100002, 0.0],
        12: [0.15, 0.0, 0.0, 0.85100002, 0.0],
        13: [0.10000001, 0.0, 0.0, 0.90099998, 0.0],
        14: [0.08000001, 0.0, 0.0, 0.92099998, 0.0],
        15: [0.05, 0.0, 0.0, 0.951, 0.0],
        16: [0.0, 0.0, 0.0, 1.001, 0.0],
        17: [-0.05, 0.0, 0.0, 1.051, 0.0],
        18: [-0.10000001, 0.0, 0.0, 1.101, 0.0],
        19: [-0.15, 0.0, 0.0, 1.151, 0.0]
    },

    // 晚上时段 - 低成功率
    evening: {
        0:  [0.75000002, 0.0, 0.0, 0.25099999, 0.0],
        1:  [0.50000002, 0.0, 0.0, 0.50099999, 0.0],
        2:  [0.45000001, 0.0, 0.0, 0.55100001, 0.0],
        3:  [0.40, 0.0, 0.0, 0.60099999, 0.0],
        4:  [0.35, 0.0, 0.0, 0.65099999, 0.0],
        5:  [0.30000001, 0.0, 0.0, 0.70100001, 0.0],
        6:  [0.24999999, 0.0, 0.0, 0.75100002, 0.0],
        7:  [0.15, 0.0, 0.0, 0.85099999, 0.0],
        8:  [0.10, 0.0, 0.0, 0.901, 0.0],
        9:  [0.05000001, 0.0, 0.0, 0.95100001, 0.0],
        10: [0.02, 0.0, 0.0, 0.98099998, 0.0],
        11: [0.0, 0.0, 0.0, 1.00100002, 0.0],
        12: [0.0, 0.0, 0.0, 1.00100002, 0.0],
        13: [-0.050000001, 0.0, 0.0, 1.05099998, 0.0],
        14: [-0.10000001, 0.0, 0.0, 1.10099998, 0.0],
        15: [-0.15, 0.0, 0.0, 1.151, 0.0],
        16: [-0.20000001, 0.0, 0.0, 1.201, 0.0],
        17: [-0.25, 0.0, 0.0, 1.251, 0.0],
        18: [-0.30000001, 0.0, 0.0, 1.301, 0.0],
        19: [-0.35, 0.0, 0.0, 1.351, 0.0]
    },

    // 深夜时段 - 极低成功率
    night: {
        // 0:  [0.70000002, 0.0, 0.0, 0.30099999, 0.0],
        // 1:  [0.00000002, 0.0, 0.0, 0.99999998, 0.0],  // 几乎100%爆装率
        // 2:  [0.35000001, 0.0, 0.0, 0.65100001, 0.0],
        // 3:  [0.30, 0.0, 0.0, 0.70099999, 0.0],
        // 4:  [0.25, 0.0, 0.0, 0.75099999, 0.0],
        // 5:  [0.20000001, 0.0, 0.0, 0.80100001, 0.0],
        // 6:  [0.14999999, 0.0, 0.0, 0.85100002, 0.0],
        // 7:  [0.05, 0.0, 0.0, 0.95099999, 0.0],
        // 8:  [0.0, 0.0, 0.0, 1.001, 0.0],
        // 9:  [-0.05000001, 0.0, 0.0, 1.05100001, 0.0],
        // 10: [-0.08, 0.0, 0.0, 1.08099998, 0.0],
        // 11: [-0.10, 0.0, 0.0, 1.10100002, 0.0],
        // 12: [-0.15, 0.0, 0.0, 1.15100002, 0.0],
        // 13: [-0.20000001, 0.0, 0.0, 1.20099998, 0.0],
        // 14: [-0.25000001, 0.0, 0.0, 1.25099998, 0.0],
        // 15: [-0.30, 0.0, 0.0, 1.301, 0.0],
        // 16: [-0.35000001, 0.0, 0.0, 1.351, 0.0],
        // 17: [-0.40, 0.0, 0.0, 1.401, 0.0],
        // 18: [-0.45000001, 0.0, 0.0, 1.451, 0.0],
        // 19: [-0.50, 0.0, 0.0, 1.501, 0.0]
        // 从C++代码转换的原版概率
        // 0:  [0.85, 0.0, 0.0, 0.15001, 0.0],
        // 1:  [0.6, 0.0, 0.0, 0.40001, 0.0],
        // 2:  [0.55, 0.0, 0.0, 0.45001, 0.0],
        // 3:  [0.5, 0.0, 0.0, 0.50001, 0.0],
        // 4:  [0.5, 0.0, 0.0, 0.50001, 0.0],
        // 5:  [0.4, 0.0, 0.0, 0.60001, 0.0],
        // 6:  [0.35, 0.0, 0.0, 0.65001, 0.0],
        // 7:  [0.25, 0.0, 0.0, 0.75001, 0.0],
        // 8:  [0.2, 0.0, 0.0, 0.80001, 0.0],
        // 9:  [0.15, 0.0, 0.0, 0.85001, 0.0],
        // 10: [0.12, 0.0, 0.0, 0.88001, 0.0],
        // 11: [0.1, 0.0, 0.0, 0.90001, 0.0],
        // 12: [0.1, 0.0, 0.0, 0.90001, 0.0],
        // 13: [0.05, 0.0, 0.0, 0.95001, 0.0],
        // 14: [0.05, 0.0, 0.0, 0.95001, 0.0],
        // 15: [0.0, 0.0, 0.0, 1.00001, 0.0],
        // 16: [-0.05, 0.0, 0.0, 1.05001, 0.0],
        // 17: [-0.14, 0.0, 0.0, 1.14001, 0.0],
        // 18: [-0.25, 0.0, 0.0, 1.25001, 0.0],
        // 19: [-0.35, 0.0, 0.0, 1.35001, 0.0]

        0:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        1:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        2:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        3:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        4:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        5:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        6:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        7:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        8:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        9:  [1.0, 0.0, 0.0, 0.00001, 0.0],
        10: [1.0, 0.0, 0.0, 0.00001, 0.0],
        11: [1.0, 0.0, 0.0, 0.00001, 0.0],
        12: [1.0, 0.0, 0.0, 0.00001, 0.0],
        13: [1.0, 0.0, 0.0, 0.00001, 0.0],
        14: [1.0, 0.0, 0.0, 0.00001, 0.0],
        15: [1.0, 0.0, 0.0, 0.00001, 0.0],
        16: [1.0, 0.0, 0.0, 0.00001, 0.0],
        17: [1.0, 0.0, 0.0, 0.00001, 0.0],
        18: [1.0, 0.0, 0.0, 0.00001, 0.0],
        19: [1.0, 0.0, 0.0, 0.00001, 0.0]
    }
};

// 灌注概率表 - 按时间段配置
const propertyRates = {
    // 默认概率表
    default: {
        // level: [成功率, 降级率, 清除属性率, 爆装率, 不降级率]
        0: [0.80000001, 0.0, 0.20100001, 0.0, 0.0],
        1: [0.69999999, 0.0, 0.301, 0.0, 0.0],
        2: [0.60000002, 0.0, 0.40099999, 0.0, 0.0],
        3: [0.5, 0.0, 0.50099999, 0.0, 0.0],
        4: [0.40000001, 0.0, 0.60100001, 0.0, 0.0],
        5: [0.34999999, 0.0, 0.65100002, 0.0, 0.0],
        6: [0.34999999, 0.0, 0.65100002, 0.0, 0.0],
        7: [0.30000001, 0.0, 0.70099998, 0.0, 0.0],
        8: [0.30000001, 0.0, 0.70099998, 0.0, 0.0],
        9: [0.25, 0.0, 0.75099999, 0.0, 0.0]
    },

    // 早晨时段 - 高成功率
    morning: {
        0: [0.95000001, 0.0, 0.05100001, 0.0, 0.0],
        1: [0.89999999, 0.0, 0.101, 0.0, 0.0],
        2: [0.85000002, 0.0, 0.15099999, 0.0, 0.0],
        3: [0.80, 0.0, 0.20099999, 0.0, 0.0],
        4: [0.75000001, 0.0, 0.25100001, 0.0, 0.0],
        5: [0.69999999, 0.0, 0.30100002, 0.0, 0.0],
        6: [0.64999999, 0.0, 0.35100002, 0.0, 0.0],
        7: [0.60000001, 0.0, 0.40099998, 0.0, 0.0],
        8: [0.55000001, 0.0, 0.45099998, 0.0, 0.0],
        9: [0.50, 0.0, 0.50099999, 0.0, 0.0]
    },

    // 下午时段 - 中等成功率
    afternoon: {
        0: [0.90000001, 0.0, 0.10100001, 0.0, 0.0],
        1: [0.79999999, 0.0, 0.201, 0.0, 0.0],
        2: [0.70000002, 0.0, 0.30099999, 0.0, 0.0],
        3: [0.60, 0.0, 0.40099999, 0.0, 0.0],
        4: [0.50000001, 0.0, 0.50100001, 0.0, 0.0],
        5: [0.44999999, 0.0, 0.55100002, 0.0, 0.0],
        6: [0.39999999, 0.0, 0.60100002, 0.0, 0.0],
        7: [0.35000001, 0.0, 0.65099998, 0.0, 0.0],
        8: [0.30000001, 0.0, 0.70099998, 0.0, 0.0],
        9: [0.25, 0.0, 0.75099999, 0.0, 0.0]
    },

    // 晚上时段 - 低成功率
    evening: {
        0: [0.70000001, 0.0, 0.30100001, 0.0, 0.0],
        1: [0.59999999, 0.0, 0.401, 0.0, 0.0],
        2: [0.50000002, 0.0, 0.50099999, 0.0, 0.0],
        3: [0.40, 0.0, 0.60099999, 0.0, 0.0],
        4: [0.30000001, 0.0, 0.70100001, 0.0, 0.0],
        5: [0.24999999, 0.0, 0.75100002, 0.0, 0.0],
        6: [0.19999999, 0.0, 0.80100002, 0.0, 0.0],
        7: [0.15000001, 0.0, 0.85099998, 0.0, 0.0],
        8: [0.10000001, 0.0, 0.90099998, 0.0, 0.0],
        9: [0.05, 0.0, 0.95099999, 0.0, 0.0]
    },

    // 深夜时段 - 极低成功率
    night: {
        0: [0.60000001, 0.0, 0.40100001, 0.0, 0.0],
        1: [0.00000009, 0.0, 0.501, 0.0, 0.0],
        2: [0.40000002, 0.0, 0.60099999, 0.0, 0.0],
        3: [0.30, 0.0, 0.70099999, 0.0, 0.0],
        4: [0.20000001, 0.0, 0.80100001, 0.0, 0.0],
        5: [0.14999999, 0.0, 0.85100002, 0.0, 0.0],
        6: [0.09999999, 0.0, 0.90100002, 0.0, 0.0],
        7: [0.05000001, 0.0, 0.95099998, 0.0, 0.0],
        8: [0.0, 0.0, 1.00099998, 0.0, 0.0],
        9: [0.0, 0.0, 1.00099999, 0.0, 0.0]
    }
};



// 修改强化概率的函数 (添加调试信息)
function modifyReinforceRate(level, rates) {
    try {
        const reinforce_table = ptr('0x091E8A04');
        const entry = reinforce_table.add((level - 1) * 24);

        console.log(`[调试] 修改炼器等级${level+1}: 地址=${entry}, 偏移=${(level - 1) * 24}`);
        console.log(`[调试] 原始数据: 成功率=${rates[0]}, 爆装率=${rates[3]}`);

        // 先读取原始值进行对比
        const originalSuccess = entry.add(4).readFloat();
        const originalDestroy = entry.add(16).readFloat();
        console.log(`[调试] 修改前: 成功率=${originalSuccess}, 爆装率=${originalDestroy}`);

        entry.add(4).writeFloat(rates[0]);   // 成功率
        entry.add(8).writeFloat(rates[1]);   // 降级率
        entry.add(12).writeFloat(rates[2]);  // 清除属性率
        entry.add(16).writeFloat(rates[3]);  // 爆装率
        entry.add(20).writeFloat(rates[4]);  // 不降级率

        // 验证写入是否成功
        const newSuccess = entry.add(4).readFloat();
        const newDestroy = entry.add(16).readFloat();
        console.log(`[调试] 修改后: 成功率=${newSuccess}, 爆装率=${newDestroy}`);

        console.log(`炼器等级${level+1}的强化概率: 成功率=${(rates[0]*100).toFixed(0)}%, 清除率=${(rates[2]*100).toFixed(0)}%, 爆装率=${(rates[3]*100).toFixed(0)}%`);
    } catch(e) {
        console.error(`[调试] 修改炼器等级${level+1}失败:`, e);
    }
}

// 修改属性石概率的函数
function modifyPropertyRate(level, rates) {
    const property_table = ptr('0x091E8EE0');
    const entry = property_table.add((level - 1) * 24);
    
    entry.add(4).writeFloat(rates[0]);   // 成功率
    entry.add(8).writeFloat(rates[1]);   // 降级率
    entry.add(12).writeFloat(rates[2]);  // 清除属性率
    entry.add(16).writeFloat(rates[3]);  // 爆装率
    entry.add(20).writeFloat(rates[4]);  // 不降级率
    
    console.log(`灌注等级${level+1}的概率: 成功率=${(rates[0]*100).toFixed(0)}%, 清除率=${(rates[2]*100).toFixed(0)}%`);
}


// 手动设置时间段 (用于测试或特殊需求)
let manualTimeSlot = null;

// 获取时间段描述
function getTimeSlotDescription(timeSlot) {
    const descriptions = {
        morning: "早晨时段 (6:00-12:00)",
        afternoon: "下午时段 (12:00-18:00)",
        evening: "晚上时段 (18:00-24:00)",
        night: "深夜时段 (0:00-6:00)",
        default: "默认时段"
    };
    return descriptions[timeSlot] || descriptions.default;
}

// 应用炼器概率修改 (临时使用默认配置进行测试)
function applyReinforceRates() {
    try {
        console.log("[强化概率修改] 使用默认配置进行测试...");
        console.log("[强化概率修改] 开始修改炼器等级概率...");

        // 临时强制使用默认表进行测试
        const ratesTable = reinforceRates.default;

        Object.entries(ratesTable).forEach(([level, rates]) => {
            modifyReinforceRate(parseInt(level), rates);
        });

        console.log("[强化概率修改] 炼器概率修改完成 (使用默认概率)");
    } catch(e) {
        console.error("[强化概率修改] 修改失败:", e);
    }
}

// 应用灌注概率修改 (临时使用默认配置进行测试)
function applyPropertyRates() {
    try {
        console.log("[强化概率修改] 使用默认配置进行测试...");
        console.log("[强化概率修改] 开始修改灌注等级概率...");

        // 临时强制使用默认表进行测试
        const ratesTable = propertyRates.default;

        Object.entries(ratesTable).forEach(([level, rates]) => {
            modifyPropertyRate(parseInt(level), rates);
        });

        console.log("[强化概率修改] 灌注概率修改完成 (使用默认概率)");
    } catch(e) {
        console.error("[强化概率修改] 修改失败:", e);
    }
}




// 设置手动时间段
function setManualTimeSlot(timeSlot) {
    if (timeSlot === null || ['morning', 'afternoon', 'evening', 'night', 'default'].includes(timeSlot)) {
        manualTimeSlot = timeSlot;
        console.log(`[强化概率修改] 手动时间段设置为: ${timeSlot ? getTimeSlotDescription(timeSlot) : '自动模式'}`);

        // 立即应用新的概率设置
        applyReinforceRates();
        applyPropertyRates();

        return true;
    } else {
        console.error(`[强化概率修改] 无效的时间段: ${timeSlot}`);
        return false;
    }
}

// 获取当前时间段 (考虑手动设置)
function getCurrentTimeSlot() {
    if (manualTimeSlot !== null) {
        return manualTimeSlot;
    }

    const now = new Date();
    const hour = now.getHours();

    if (hour >= 6 && hour < 12) {
        return 'morning';
    } else if (hour >= 12 && hour < 18) {
        return 'afternoon';
    } else if (hour >= 18 && hour < 24) {
        return 'evening';
    } else {
        return 'night';
    }
}

// 显示当前概率信息
function showCurrentRates() {
    const currentTimeSlot = getCurrentTimeSlot();
    const timeSlotDesc = getTimeSlotDescription(currentTimeSlot);

    console.log(`\n=== 当前强化概率信息 ===`);
    console.log(`时间段: ${timeSlotDesc}`);
    console.log(`手动模式: ${manualTimeSlot !== null ? '是' : '否'}`);

    const reinforceTable = reinforceRates[currentTimeSlot] || reinforceRates.default;
    const propertyTable = propertyRates[currentTimeSlot] || propertyRates.default;

    console.log(`\n炼器概率 (前5级):`);
    for (let i = 0; i < 5; i++) {
        const rates = reinforceTable[i];
        if (rates) {
            console.log(`  等级${i+1}: 成功率=${(rates[0]*100).toFixed(1)}%, 爆装率=${(rates[3]*100).toFixed(1)}%`);
        }
    }

    console.log(`\n灌注概率 (前5级):`);
    for (let i = 0; i < 5; i++) {
        const rates = propertyTable[i];
        if (rates) {
            console.log(`  等级${i+1}: 成功率=${(rates[0]*100).toFixed(1)}%, 清除率=${(rates[2]*100).toFixed(1)}%`);
        }
    }
    console.log(`========================\n`);
}

// 定时检查并更新概率 (每小时检查一次)
let autoUpdateInterval = null;

function startAutoUpdate() {
    if (autoUpdateInterval) {
        clearInterval(autoUpdateInterval);
    }

    // 每小时检查一次时间段变化
    autoUpdateInterval = setInterval(() => {
        if (manualTimeSlot === null) { // 只在自动模式下更新
            const currentTimeSlot = getCurrentTimeSlot();
            console.log(`[强化概率修改] 定时检查 - 当前时间段: ${getTimeSlotDescription(currentTimeSlot)}`);
            applyReinforceRates();
            applyPropertyRates();
        }
    }, 60 * 60 * 1000); // 1小时

    console.log("[强化概率修改] 自动更新已启动 (每小时检查一次)");
}

function stopAutoUpdate() {
    if (autoUpdateInterval) {
        clearInterval(autoUpdateInterval);
        autoUpdateInterval = null;
        console.log("[强化概率修改] 自动更新已停止");
    }
}

module.exports = {
    applyReinforceRates,
    applyPropertyRates,
    setManualTimeSlot,
    showCurrentRates,
    startAutoUpdate,
    stopAutoUpdate,
    getCurrentTimeSlot,
    getTimeSlotDescription
};