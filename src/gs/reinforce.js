// 炼器概率表
const reinforceRates = {
    // level: [成功率, 降级率, 清除属性率, 爆装率, 不降级率]
    0:  [0.85000002, 0.0, 0.0, 0.15099999, 0.0],
    1:  [0.60000002, 0.0, 0.0, 0.40099999, 0.0],
    2:  [0.55000001, 0.0, 0.0, 0.45100001, 0.0],
    3:  [0.5, 0.0, 0.0, 0.50099999, 0.0],
    4:  [0.5, 0.0, 0.0, 0.50099999, 0.0],
    5:  [0.40000001, 0.0, 0.0, 0.60100001, 0.0],
    6:  [0.34999999, 0.0, 0.0, 0.65100002, 0.0],
    7:  [0.25, 0.0, 0.0, 0.75099999, 0.0],
    8:  [0.2, 0.0, 0.0, 0.801, 0.0],
    9:  [0.15000001, 0.0, 0.0, 0.85100001, 0.0],
    10: [0.12, 0.0, 0.0, 0.88099998, 0.0],
    11: [0.1, 0.0, 0.0, 0.90100002, 0.0],
    12: [0.1, 0.0, 0.0, 0.90100002, 0.0],
    13: [0.050000001, 0.0, 0.0, 0.95099998, 0.0],
    14: [0.050000001, 0.0, 0.0, 0.95099998, 0.0],
    15: [0.0, 0.0, 0.0, 1.001, 0.0],
    16: [-0.050000001, 0.0, 0.0, 1.051, 0.0],
    17: [-0.1, 0.0, 0.0, 1.101, 0.0],
    18: [-0.15000001, 0.0, 0.0, 1.151, 0.0],
    19: [-0.2, 0.0, 0.0, 1.201, 0.0]
};


// 灌注概率表
const propertyRates = {
    // level: [成功率, 降级率, 清除属性率, 爆装率, 不降级率]
    0: [0.80000001, 0.0, 0.20100001, 0.0, 0.0],
    1: [0.69999999, 0.0, 0.301, 0.0, 0.0],
    2: [0.60000002, 0.0, 0.40099999, 0.0, 0.0],
    3: [0.5, 0.0, 0.50099999, 0.0, 0.0],
    4: [0.40000001, 0.0, 0.60100001, 0.0, 0.0],
    5: [0.34999999, 0.0, 0.65100002, 0.0, 0.0],
    6: [0.34999999, 0.0, 0.65100002, 0.0, 0.0],
    7: [0.30000001, 0.0, 0.70099998, 0.0, 0.0],
    8: [0.30000001, 0.0, 0.70099998, 0.0, 0.0],
    9: [0.25, 0.0, 0.75099999, 0.0, 0.0]
};



// 修改强化概率的函数
function modifyReinforceRate(level, rates) {
    const reinforce_table = ptr('0x091E8A04');
    const entry = reinforce_table.add((level - 1) * 24);
    
    entry.add(4).writeFloat(rates[0]);   // 成功率
    entry.add(8).writeFloat(rates[1]);   // 降级率
    entry.add(12).writeFloat(rates[2]);  // 清除属性率
    entry.add(16).writeFloat(rates[3]);  // 爆装率
    entry.add(20).writeFloat(rates[4]);  // 不降级率
    
    console.log(`炼器等级${level+1}的强化概率: 成功率=${(rates[0]*100).toFixed(0)}%, 清除率=${(rates[2]*100).toFixed(0)}%, 爆装率=${(rates[3]*100).toFixed(0)}%`);
}

// 修改属性石概率的函数
function modifyPropertyRate(level, rates) {
    const property_table = ptr('0x091E8EE0');
    const entry = property_table.add((level - 1) * 24);
    
    entry.add(4).writeFloat(rates[0]);   // 成功率
    entry.add(8).writeFloat(rates[1]);   // 降级率
    entry.add(12).writeFloat(rates[2]);  // 清除属性率
    entry.add(16).writeFloat(rates[3]);  // 爆装率
    entry.add(20).writeFloat(rates[4]);  // 不降级率
    
    console.log(`灌注等级${level+1}的概率: 成功率=${(rates[0]*100).toFixed(0)}%, 清除率=${(rates[2]*100).toFixed(0)}%`);
}


// 应用炼器概率修改
function applyReinforceRates() {
    try {
        console.log("[强化概率修改] 开始修改炼器等级概率...");
        Object.entries(reinforceRates).forEach(([level, rates]) => {
            modifyReinforceRate(parseInt(level), rates);
        });
        console.log("[强化概率修改] 炼器概率修改完成");
    } catch(e) {
        console.error("[强化概率修改] 修改失败:", e);
    }
}

// 应用灌注概率修改
function applyPropertyRates() {
    try {
        console.log("[强化概率修改] 开始修改灌注等级概率...");
        Object.entries(propertyRates).forEach(([level, rates]) => {
            modifyPropertyRate(parseInt(level), rates);
        });
        console.log("[强化概率修改] 灌注概率修改完成");
    } catch(e) {
        console.error("[强化概率修改] 修改失败:", e);
    }
}


module.exports = {
    applyReinforceRates,
    applyPropertyRates,
};