const fs = require('fs');
const path = require('path');

// ==================== 版本配置区域 ====================
// 内存偏移配置 (根据不同版本调整)
const VERSION_OFFSETS = {
    // ReinforceEquipment 函数地址
    reinforceEquipmentAddr: "0x0889A33A",
    
    // 概率表地址
    reinforceTableAddr: "0x091E8A00",  // 炼器概率表
    propertyTableAddr: "0x091E8EE0",   // 灌注概率表
    
    // gactive_imp 结构偏移
    gactiveImpParentOffset: 8,         // gactive_imp + 8 -> _parent
    playerIdOffset: 64,                // _parent + 64 -> 角色ID
    
    // 概率表结构
    tableEntrySize: 24,                // 每个等级条目大小
    fieldOffsets: {
        successRate: 4,                // 成功率偏移
        degradeRate: 8,                // 降级率偏移  
        clearRate: 12,                 // 清除率偏移
        destroyRate: 16,               // 爆装率偏移
        noDowngradeRate: 20            // 不降级率偏移
    }
};

// ==================== 配置加载 ====================
let CONFIG = null;
let defaultRates = null;
let playerSpecificRates = null;

// 加载配置文件
function loadConfig() {
    try {
        const configPath = path.join(__dirname, 'reinforce_config.json');
        const configData = fs.readFileSync(configPath, 'utf8');
        const config = JSON.parse(configData);
        
        // 合并版本偏移到配置中
        CONFIG = {
            ...config.switches,
            ...VERSION_OFFSETS
        };
        
        defaultRates = config.defaultRates;
        playerSpecificRates = config.playerSpecificRates;
        
        console.log("[配置] 强化概率配置加载成功");
        return true;
    } catch (e) {
        console.error("[配置] 加载配置文件失败:", e);
        return false;
    }
}

// 重新加载配置文件
function reloadConfig() {
    return loadConfig();
}

// ==================== 核心功能 ====================

// 从 gactive_imp 指针获取角色ID
function getPlayerIdFromGActiveImp(gactiveImpPtr) {
    try {
        if (!gactiveImpPtr || gactiveImpPtr.isNull()) {
            return null;
        }

        // gactive_imp + offset -> _parent (gplayer_imp) + offset -> 角色ID
        const parentPtr = gactiveImpPtr.add(CONFIG.gactiveImpParentOffset).readPointer();
        if (!parentPtr || parentPtr.isNull()) {
            return null;
        }

        const playerId = parentPtr.add(CONFIG.playerIdOffset).readInt();
        
        // 检查是否是合理的角色ID
        if (playerId && playerId > 0 && playerId < 1000000) {
            return playerId.toString();
        }

        return null;
    } catch (e) {
        return null;
    }
}

// 修改强化概率的函数
function modifyReinforceRate(level, rates) {
    const reinforce_table = ptr(CONFIG.reinforceTableAddr);
    const entry = reinforce_table.add((level - 1) * CONFIG.tableEntrySize);

    entry.add(CONFIG.fieldOffsets.successRate).writeFloat(rates[0]);      // 成功率
    entry.add(CONFIG.fieldOffsets.degradeRate).writeFloat(rates[1]);      // 降级率
    entry.add(CONFIG.fieldOffsets.clearRate).writeFloat(rates[2]);        // 清除属性率
    entry.add(CONFIG.fieldOffsets.destroyRate).writeFloat(rates[3]);      // 爆装率
    entry.add(CONFIG.fieldOffsets.noDowngradeRate).writeFloat(rates[4]);  // 不降级率
}

// 修改灌注概率的函数
function modifyPropertyRate(level, rates) {
    const property_table = ptr(CONFIG.propertyTableAddr);
    const entry = property_table.add((level - 1) * CONFIG.tableEntrySize);

    entry.add(CONFIG.fieldOffsets.successRate).writeFloat(rates[0]);      // 成功率
    entry.add(CONFIG.fieldOffsets.degradeRate).writeFloat(rates[1]);      // 降级率
    entry.add(CONFIG.fieldOffsets.clearRate).writeFloat(rates[2]);        // 清除属性率
    entry.add(CONFIG.fieldOffsets.destroyRate).writeFloat(rates[3]);      // 爆装率
    entry.add(CONFIG.fieldOffsets.noDowngradeRate).writeFloat(rates[4]);  // 不降级率
}

// Hook ReinforceEquipment 函数
function hookReinforceEquipment() {
    try {
        const reinforceEquipmentAddr = ptr(CONFIG.reinforceEquipmentAddr);
        
        Interceptor.attach(reinforceEquipmentAddr, {
            onEnter: function(args) {
                try {
                    // args[2] = gactive_imp*
                    const gactiveImpPtr = args[2];
                    const playerId = getPlayerIdFromGActiveImp(gactiveImpPtr);

                    // 如果有专属配置，立即应用
                    if (playerId && playerSpecificRates[playerId]) {
                        console.log(`[强化] 为角色 ${playerId} 应用专属配置`);
                        
                        // 应用炼器专属配置 (配置等级0-19 → 游戏等级1-20)
                        const reinforceConfig = playerSpecificRates[playerId].reinforce;
                        Object.entries(reinforceConfig).forEach(([configLevel, rates]) => {
                            const level = parseInt(configLevel);
                            modifyReinforceRate(level + 1, rates);
                        });
                        
                        // 应用灌注专属配置 (配置等级0-9 → 游戏等级1-10)
                        const propertyConfig = playerSpecificRates[playerId].property;
                        Object.entries(propertyConfig).forEach(([configLevel, rates]) => {
                            const level = parseInt(configLevel);
                            modifyPropertyRate(level + 1, rates);
                        });
                    } else {
                        // 应用默认配置
                        applyDefaultRates();
                    }
                    
                } catch (e) {
                    console.error("[Hook] 错误:", e);
                }
            }
        });
        
    } catch (e) {
        console.error("[Hook] 安装Hook失败:", e);
        applyDefaultRates();
    }
}

// 应用默认配置
function applyDefaultRates() {
    try {
        // 应用默认炼器配置
        Object.entries(defaultRates.reinforce).forEach(([level, rates]) => {
            modifyReinforceRate(parseInt(level), rates);
        });
        
        // 应用默认灌注配置
        Object.entries(defaultRates.property).forEach(([level, rates]) => {
            modifyPropertyRate(parseInt(level), rates);
        });
    } catch(e) {
        console.error("[错误] 应用默认配置失败:", e);
    }
}

// ==================== 主要入口函数 ====================

// 应用炼器概率修改
function applyReinforceRates() {
    // 首先加载配置
    if (!loadConfig()) {
        console.error("[错误] 配置加载失败，无法启动");
        return false;
    }
    
    if (CONFIG.enablePlayerSpecific) {
        // 如果启用角色专属配置，使用Hook方式
        hookReinforceEquipment();
    } else {
        // 否则使用默认方式
        applyDefaultRates();
    }
    
    return true;
}

function applyPropertyRates() {
    // 灌注概率在 applyReinforceRates 中一起处理了
}

// ==================== 配置管理函数 ====================

// 设置配置
function setConfig(key, value) {
    if (!CONFIG) {
        loadConfig();
    }
    
    if (CONFIG.hasOwnProperty(key)) {
        CONFIG[key] = value;
        console.log(`[配置] ${key} 设置为: ${value}`);
        return true;
    } else {
        console.error(`[配置] 无效的配置项: ${key}`);
        return false;
    }
}

// 获取配置
function getConfig() {
    if (!CONFIG) {
        loadConfig();
    }
    return { ...CONFIG };
}

// 添加角色专属配置
function addPlayerConfig(playerId, reinforceRates, propertyRates) {
    if (!playerSpecificRates) {
        loadConfig();
    }
    
    playerSpecificRates[playerId.toString()] = {
        reinforce: reinforceRates,
        property: propertyRates
    };
    console.log(`[配置] 已添加角色专属配置 (ID: ${playerId})`);
}

// 删除角色专属配置
function removePlayerConfig(playerId) {
    if (!playerSpecificRates) {
        loadConfig();
    }
    
    const playerIdStr = playerId.toString();
    if (playerSpecificRates[playerIdStr]) {
        delete playerSpecificRates[playerIdStr];
        console.log(`[配置] 已删除角色专属配置 (ID: ${playerId})`);
        return true;
    } else {
        console.log(`[配置] 未找到角色配置: ID ${playerId}`);
        return false;
    }
}

// 列出角色专属配置
function listPlayerConfigs() {
    if (!playerSpecificRates) {
        loadConfig();
    }
    
    console.log("\n=== 角色专属配置列表 ===");
    const configs = Object.entries(playerSpecificRates);
    if (configs.length === 0) {
        console.log("暂无角色专属配置");
    } else {
        configs.forEach(([playerId]) => {
            console.log(`角色ID: ${playerId}`);
        });
    }
    console.log("========================\n");
}

// 测试系统功能
function testSystem() {
    console.log("\n=== 强化概率系统测试 ===");
    
    // 测试配置加载
    if (loadConfig()) {
        console.log("✅ 配置文件加载成功");
        console.log("当前配置:", getConfig());
        console.log("角色专属配置:");
        listPlayerConfigs();
    } else {
        console.log("❌ 配置文件加载失败");
    }
    
    console.log("========================\n");
}

module.exports = {
    applyReinforceRates,
    applyPropertyRates,
    setConfig,
    getConfig,
    addPlayerConfig,
    removePlayerConfig,
    listPlayerConfigs,
    testSystem,
    reloadConfig
};
