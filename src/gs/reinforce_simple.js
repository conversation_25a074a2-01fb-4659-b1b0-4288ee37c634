const GPlayerImp = require('../classes/GPlayerImp');

// ==================== 配置区域 ====================
// 功能开关配置
const CONFIG = {
    // 是否启用角色专属配置 (true = 优先使用角色专属配置)
    enablePlayerSpecific: true,

    // 调试模式 (显示详细日志)
    debugMode: false,
};

// 角色专属配置表 (根据角色ID)
const playerSpecificRates = {
    // 角色ID: { reinforce: 炼器概率, property: 灌注概率 }
    "2705": {
        reinforce: {
            // 炼器概率 - 超高成功率，概率总和=1.01
            0: [0.98, 0.0, 0.0, 0.03, 0.0],    // 98%成功 + 3%爆装 = 1.01
            1: [-0.050000001, 0.0, 0.0, 1.051, 0.0],
            2: [0.96, 0.0, 0.0, 0.05, 0.0],    // 96%成功 + 5%爆装 = 1.01
            3: [0.95, 0.0, 0.0, 0.06, 0.0],    // 95%成功 + 6%爆装 = 1.01
            4: [0.94, 0.0, 0.0, 0.07, 0.0],    // 94%成功 + 7%爆装 = 1.01
            5: [0.93, 0.0, 0.0, 0.08, 0.0],    // 93%成功 + 8%爆装 = 1.01
            6: [0.92, 0.0, 0.0, 0.09, 0.0],    // 92%成功 + 9%爆装 = 1.01
            7: [0.91, 0.0, 0.0, 0.10, 0.0],    // 91%成功 + 10%爆装 = 1.01
            8: [0.90, 0.0, 0.0, 0.11, 0.0],    // 90%成功 + 11%爆装 = 1.01
            9: [0.89, 0.0, 0.0, 0.12, 0.0],    // 89%成功 + 12%爆装 = 1.01
            10: [0.88, 0.0, 0.0, 0.13, 0.0],    // 88%成功 + 13%爆装 = 1.01
            11: [0.87, 0.0, 0.0, 0.14, 0.0],    // 87%成功 + 14%爆装 = 1.01
            12: [0.86, 0.0, 0.0, 0.15, 0.0],    // 86%成功 + 15%爆装 = 1.01
            13: [0.85, 0.0, 0.0, 0.16, 0.0],    // 85%成功 + 16%爆装 = 1.01
            14: [0.84, 0.0, 0.0, 0.17, 0.0],    // 84%成功 + 17%爆装 = 1.01
            15: [0.83, 0.0, 0.0, 0.18, 0.0],    // 83%成功 + 18%爆装 = 1.01
            16: [0.82, 0.0, 0.0, 0.19, 0.0],    // 82%成功 + 19%爆装 = 1.01
            17: [0.81, 0.0, 0.0, 0.20, 0.0],    // 81%成功 + 20%爆装 = 1.01
            18: [0.80, 0.0, 0.0, 0.21, 0.0],    // 80%成功 + 21%爆装 = 1.01
            19: [0.79, 0.0, 0.0, 0.22, 0.0],    // 79%成功 + 22%爆装 = 1.01
        },
        property: {
            // 灌注概率 - 超高成功率，概率总和=1.01
            1: [0.99, 0.0, 0.02, 0.0, 0.0],     // 99%成功 + 2%清除 = 1.01
            2: [0.98, 0.0, 0.03, 0.0, 0.0],     // 98%成功 + 3%清除 = 1.01
            3: [0.97, 0.0, 0.04, 0.0, 0.0],     // 97%成功 + 4%清除 = 1.01
            4: [0.96, 0.0, 0.05, 0.0, 0.0],     // 96%成功 + 5%清除 = 1.01
            5: [0.95, 0.0, 0.06, 0.0, 0.0],     // 95%成功 + 6%清除 = 1.01
            6: [0.94, 0.0, 0.07, 0.0, 0.0],     // 94%成功 + 7%清除 = 1.01
            7: [0.93, 0.0, 0.08, 0.0, 0.0],     // 93%成功 + 8%清除 = 1.01
            8: [0.92, 0.0, 0.09, 0.0, 0.0],     // 92%成功 + 9%清除 = 1.01
            9: [0.91, 0.0, 0.10, 0.0, 0.0],     // 91%成功 + 10%清除 = 1.01
            10: [0.90, 0.0, 0.11, 0.0, 0.0]      // 90%成功 + 11%清除 = 1.01
        }
    },

    // 为角色ID 2720 添加专属配置 (超高成功率)
    "2720": {
        reinforce: {
            // 炼器概率 - 超高成功率，概率总和=1.01
            0: [0.98, 0.0, 0.0, 0.03, 0.0],    // 98%成功 + 3%爆装 = 1.01
            1: [0.98, 0.0, 0.0, 0.03, 0.0],
            2: [0.96, 0.0, 0.0, 0.05, 0.0],    // 96%成功 + 5%爆装 = 1.01
            3: [-0.050000001, 0.0, 0.0, 1.051, 0.0],    // 95%成功 + 6%爆装 = 1.01
            4: [0.94, 0.0, 0.0, 0.07, 0.0],    // 94%成功 + 7%爆装 = 1.01
            5: [0.93, 0.0, 0.0, 0.08, 0.0],    // 93%成功 + 8%爆装 = 1.01
            6: [0.92, 0.0, 0.0, 0.09, 0.0],    // 92%成功 + 9%爆装 = 1.01
            7: [0.91, 0.0, 0.0, 0.10, 0.0],    // 91%成功 + 10%爆装 = 1.01
            8: [0.90, 0.0, 0.0, 0.11, 0.0],    // 90%成功 + 11%爆装 = 1.01
            9: [0.89, 0.0, 0.0, 0.12, 0.0],    // 89%成功 + 12%爆装 = 1.01
            10: [0.88, 0.0, 0.0, 0.13, 0.0],    // 88%成功 + 13%爆装 = 1.01
            11: [0.87, 0.0, 0.0, 0.14, 0.0],    // 87%成功 + 14%爆装 = 1.01
            12: [0.86, 0.0, 0.0, 0.15, 0.0],    // 86%成功 + 15%爆装 = 1.01
            13: [0.85, 0.0, 0.0, 0.16, 0.0],    // 85%成功 + 16%爆装 = 1.01
            14: [0.84, 0.0, 0.0, 0.17, 0.0],    // 84%成功 + 17%爆装 = 1.01
            15: [0.83, 0.0, 0.0, 0.18, 0.0],    // 83%成功 + 18%爆装 = 1.01
            16: [0.82, 0.0, 0.0, 0.19, 0.0],    // 82%成功 + 19%爆装 = 1.01
            17: [0.81, 0.0, 0.0, 0.20, 0.0],    // 81%成功 + 20%爆装 = 1.01
            18: [0.80, 0.0, 0.0, 0.21, 0.0],    // 80%成功 + 21%爆装 = 1.01
            19: [0.79, 0.0, 0.0, 0.22, 0.0],    // 79%成功 + 22%爆装 = 1.01
        },
        property: {
            // 灌注概率 - 超高成功率，概率总和=1.01
            1: [0.99, 0.0, 0.02, 0.0, 0.0],     // 99%成功 + 2%清除 = 1.01
            2: [0.98, 0.0, 0.03, 0.0, 0.0],     // 98%成功 + 3%清除 = 1.01
            3: [0.97, 0.0, 0.04, 0.0, 0.0],     // 97%成功 + 4%清除 = 1.01
            4: [0.96, 0.0, 0.05, 0.0, 0.0],     // 96%成功 + 5%清除 = 1.01
            5: [0.95, 0.0, 0.06, 0.0, 0.0],     // 95%成功 + 6%清除 = 1.01
            6: [0.94, 0.0, 0.07, 0.0, 0.0],     // 94%成功 + 7%清除 = 1.01
            7: [0.93, 0.0, 0.08, 0.0, 0.0],     // 93%成功 + 8%清除 = 1.01
            8: [0.92, 0.0, 0.09, 0.0, 0.0],     // 92%成功 + 9%清除 = 1.01
            9: [0.91, 0.0, 0.10, 0.0, 0.0],     // 91%成功 + 10%清除 = 1.01
            10: [0.90, 0.0, 0.11, 0.0, 0.0]      // 90%成功 + 11%清除 = 1.01
        }
    }
    // 可以添加更多角色ID...
};

// 默认炼器概率配置表
const defaultReinforceRates = {
    // level: [成功率, 降级率, 清除属性率, 爆装率, 不降级率] - 前4个总和=1.01
    1: [0.85, 0.0, 0.0, 0.16, 0.0],      // 85%成功 + 16%爆装 = 1.01
    2: [0.60, 0.0, 0.0, 0.41, 0.0],      // 60%成功 + 41%爆装 = 1.01  
    3: [0.55, 0.0, 0.0, 0.46, 0.0],      // 55%成功 + 46%爆装 = 1.01
    4: [0.50, 0.0, 0.0, 0.51, 0.0],      // 50%成功 + 51%爆装 = 1.01
    5: [0.50, 0.0, 0.0, 0.51, 0.0],      // 50%成功 + 51%爆装 = 1.01
    6: [0.40, 0.0, 0.0, 0.61, 0.0],      // 40%成功 + 61%爆装 = 1.01
    7: [0.35, 0.0, 0.0, 0.66, 0.0],      // 35%成功 + 66%爆装 = 1.01
    8: [0.25, 0.0, 0.0, 0.76, 0.0],      // 25%成功 + 76%爆装 = 1.01
    9: [0.20, 0.0, 0.0, 0.81, 0.0],      // 20%成功 + 81%爆装 = 1.01
    10: [0.15, 0.0, 0.0, 0.86, 0.0],      // 15%成功 + 86%爆装 = 1.01
    11: [0.12, 0.0, 0.0, 0.89, 0.0],     // 12%成功 + 89%爆装 = 1.01
    12: [0.10, 0.0, 0.0, 0.91, 0.0],     // 10%成功 + 91%爆装 = 1.01
    13: [0.10, 0.0, 0.0, 0.91, 0.0],     // 10%成功 + 91%爆装 = 1.01
    14: [0.05, 0.0, 0.0, 0.96, 0.0],     // 5%成功 + 96%爆装 = 1.01
    15: [0.05, 0.0, 0.0, 0.96, 0.0],     // 5%成功 + 96%爆装 = 1.01
    16: [0.00, 0.0, 0.0, 1.01, 0.0],     // 0%成功 + 101%爆装 = 1.01
    17: [0.00, 0.0, 0.0, 1.01, 0.0],     // 0%成功 + 101%爆装 = 1.01
    18: [0.00, 0.0, 0.0, 1.01, 0.0],     // 0%成功 + 101%爆装 = 1.01
    19: [0.00, 0.0, 0.0, 1.01, 0.0],     // 0%成功 + 101%爆装 = 1.01
    20: [0.00, 0.0, 0.0, 1.01, 0.0]      // 0%成功 + 101%爆装 = 1.01
};

// 默认灌注概率配置表
const defaultPropertyRates = {
    // level: [成功率, 降级率, 清除属性率, 爆装率, 不降级率] - 前4个总和=1.01
    1: [0.80, 0.0, 0.21, 0.0, 0.0],      // 80%成功 + 21%清除 = 1.01
    2: [0.70, 0.0, 0.31, 0.0, 0.0],      // 70%成功 + 31%清除 = 1.01
    3: [0.60, 0.0, 0.41, 0.0, 0.0],      // 60%成功 + 41%清除 = 1.01
    4: [0.50, 0.0, 0.51, 0.0, 0.0],      // 50%成功 + 51%清除 = 1.01
    5: [0.40, 0.0, 0.61, 0.0, 0.0],      // 40%成功 + 61%清除 = 1.01
    6: [0.35, 0.0, 0.66, 0.0, 0.0],      // 35%成功 + 66%清除 = 1.01
    7: [0.35, 0.0, 0.66, 0.0, 0.0],      // 35%成功 + 66%清除 = 1.01
    8: [0.30, 0.0, 0.71, 0.0, 0.0],      // 30%成功 + 71%清除 = 1.01
    9: [0.30, 0.0, 0.71, 0.0, 0.0],      // 30%成功 + 71%清除 = 1.01
    10: [0.25, 0.0, 0.76, 0.0, 0.0]       // 25%成功 + 76%清除 = 1.01
};
// ==================== 配置区域结束 ====================

// 从 gactive_imp 指针获取角色ID
function getPlayerIdFromGActiveImp(gactiveImpPtr) {
    try {
        if (!gactiveImpPtr || gactiveImpPtr.isNull()) {
            return null;
        }

        // gactive_imp + 8 -> _parent (gplayer_imp) + 40 -> 角色ID
        const parentPtr = gactiveImpPtr.add(0x8).readPointer();
        if (!parentPtr || parentPtr.isNull()) {
            return null;
        }

        const playerId = parentPtr.add(0x40).readInt();

        // 检查是否是合理的角色ID
        if (playerId && playerId > 0 && playerId < 1000000) {
            return playerId.toString();
        }

        return null;
    } catch (e) {
        return null;
    }
}

// 修改强化概率的函数
function modifyReinforceRate(level, rates) {
    const reinforce_table = ptr('0x091E8A00');
    const entry = reinforce_table.add((level - 1) * 24);

    entry.add(4).writeFloat(rates[0]);   // 成功率
    entry.add(8).writeFloat(rates[1]);   // 降级率
    entry.add(12).writeFloat(rates[2]);  // 清除属性率
    entry.add(16).writeFloat(rates[3]);  // 爆装率
    entry.add(20).writeFloat(rates[4]);  // 不降级率
}

// 修改灌注概率的函数
function modifyPropertyRate(level, rates) {
    const property_table = ptr('0x091E8EE0');
    const entry = property_table.add((level - 1) * 24);

    entry.add(4).writeFloat(rates[0]);   // 成功率
    entry.add(8).writeFloat(rates[1]);   // 降级率
    entry.add(12).writeFloat(rates[2]);  // 清除属性率
    entry.add(16).writeFloat(rates[3]);  // 爆装率
    entry.add(20).writeFloat(rates[4]);  // 不降级率
}

// Hook ReinforceEquipment 函数来实时获取角色ID并应用配置
function hookReinforceEquipment() {
    try {
        // ReinforceEquipment 函数地址 (从你提供的汇编代码)
        const reinforceEquipmentAddr = ptr('0x0889A33A');

        Interceptor.attach(reinforceEquipmentAddr, {
            onEnter: function (args) {
                try {
                    // 根据函数签名: equip_item *this, item *a2, gactive_imp *a3, ...
                    // args[0] = this (equip_item*)
                    // args[1] = a2 (item*)
                    // args[2] = a3 (gactive_imp*)
                    const gactiveImpPtr = args[2];

                    // 从 gactive_imp 获取角色ID
                    const playerId = getPlayerIdFromGActiveImp(gactiveImpPtr);

                    // 如果有专属配置，立即应用
                    if (playerId && playerSpecificRates[playerId]) {
                        console.log(`[强化] 为角色 ${playerId} 应用专属配置`);

                        // 应用炼器专属配置
                        const reinforceConfig = playerSpecificRates[playerId].reinforce;
                        Object.entries(reinforceConfig).forEach(([configLevel, rates]) => {
                            const level = parseInt(configLevel);
                            modifyReinforceRate(level + 1, rates);
                        });

                        // 应用灌注专属配置
                        const propertyConfig = playerSpecificRates[playerId].property;
                        Object.entries(propertyConfig).forEach(([configLevel, rates]) => {
                            const level = parseInt(configLevel);
                            modifyPropertyRate(level + 1, rates);
                        });
                    } else {
                        // 应用默认配置
                        applyDefaultRates();
                    }

                } catch (e) {
                    console.error("[Hook] 错误:", e);
                }
            }
        });

        console.log("[Hook] ReinforceEquipment Hook 安装成功");

    } catch (e) {
        console.error("[Hook] 安装 ReinforceEquipment Hook 失败:", e);
        // 回退到默认配置
        applyDefaultRates();
    }
}

// 应用默认配置
function applyDefaultRates() {
    try {
        console.log("[强化概率修改] 应用默认配置...");

        // 应用默认炼器配置
        Object.entries(defaultReinforceRates).forEach(([level, rates]) => {
            modifyReinforceRate(parseInt(level), rates);
        });

        // 应用默认灌注配置
        Object.entries(defaultPropertyRates).forEach(([level, rates]) => {
            modifyPropertyRate(parseInt(level), rates);
        });

        console.log("[强化概率修改] 默认配置应用完成");
    } catch (e) {
        console.error("[强化概率修改] 应用默认配置失败:", e);
    }
}

// 主要入口函数
function applyReinforceRates() {
    if (CONFIG.enablePlayerSpecific) {
        // 如果启用角色专属配置，使用Hook方式
        hookReinforceEquipment();
    } else {
        // 否则使用默认方式
        applyDefaultRates();
    }
}

function applyPropertyRates() {
    // 灌注概率在 applyReinforceRates 中一起处理了
    console.log("[强化概率修改] 灌注概率已在炼器Hook中处理");
}

// 配置管理函数
function setConfig(key, value) {
    if (CONFIG.hasOwnProperty(key)) {
        CONFIG[key] = value;
        console.log(`[配置] ${key} 设置为: ${value}`);
        return true;
    } else {
        console.error(`[配置] 无效的配置项: ${key}`);
        return false;
    }
}

function getConfig() {
    return { ...CONFIG };
}

function addPlayerConfig(playerId, reinforceRates, propertyRates) {
    playerSpecificRates[playerId.toString()] = {
        reinforce: reinforceRates,
        property: propertyRates
    };
    console.log(`[配置] 已添加角色专属配置 (ID: ${playerId})`);
}

function removePlayerConfig(playerId) {
    const playerIdStr = playerId.toString();
    if (playerSpecificRates[playerIdStr]) {
        delete playerSpecificRates[playerIdStr];
        console.log(`[配置] 已删除角色专属配置 (ID: ${playerId})`);
        return true;
    } else {
        console.log(`[配置] 未找到角色配置: ID ${playerId}`);
        return false;
    }
}

function listPlayerConfigs() {
    console.log("\n=== 角色专属配置列表 ===");
    const configs = Object.entries(playerSpecificRates);
    if (configs.length === 0) {
        console.log("暂无角色专属配置");
    } else {
        configs.forEach(([playerId]) => {
            console.log(`角色ID: ${playerId}`);
        });
    }
    console.log("========================\n");
}

// 测试系统功能
function testSystem() {
    console.log("\n=== 强化概率系统测试 ===");

    // 测试配置获取
    console.log("当前配置:", getConfig());

    // 测试角色专属配置
    console.log("角色专属配置:");
    listPlayerConfigs();

    console.log("========================\n");
}

module.exports = {
    applyReinforceRates,
    applyPropertyRates,
    setConfig,
    getConfig,
    addPlayerConfig,
    removePlayerConfig,
    listPlayerConfigs,
    testSystem
};
