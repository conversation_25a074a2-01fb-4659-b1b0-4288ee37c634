const { applyKillMonitor, applyRuneScoreHook, applyCombatStateHook, applyOtherHooks, applyChatHook } = require('./utils');
const { applyProgressBarHooks } = require('./duration');
const { applyReinforceRates, applyPropertyRates, startAutoUpdate, showCurrentRates } = require('./reinforce');
const { mountDelayHook } = require('./mountDelay');
const { applyUpgradeRates } = require('./AstrologyUpgradeRate');
const skillNoLimit = require('./skillNoLimit');


// 初始化所有gs相关的hook
function initGSHooks() {
    try {
        // 初始化击杀监控
        // applyKillMonitor();

        // 初始化物品使用监控
        // initItemUsageMonitor();

        // 初始化物品获取监控
        // initItemGainMonitor();

        console.log('[System] GS hooks initialized');
    } catch (e) {
        console.error('[Init] Error:', e);
    }
}

function init() {
    try {
        // 初始化符文合成修改
        // initFuwen();

        // 初始化物品拾取监控
        // initItemPickupMonitor();

        // 初始化物品获取监控
        // initItemGainMonitor();

        // 初始化在线奖励系统
        // onlineRewards.init();
        // console.log('[GS] 在线奖励系统已启动');

        // 再次确保装备效果监控系统已启动
        // try {
        //     if (!equipmentEffectMonitor.isInitialized) {
        //         equipmentEffectMonitor.init();
        //         console.log('[GS] 装备效果监控系统已启动');
        //     }
        // } catch (e) {
        //     console.error('[GS] 装备效果监控系统启动失败:', e);
        // }

    } catch (e) {
        console.error('[GS] Init error:', e);
        return false;
    }
}

// 初始化所有监控器
function initAllMonitors() {
    // try {
    // ... existing code ...

    // 初始化物品获取监控
    // if (initItemGainMonitor()) {
    //     console.log('[Monitor] 物品获取监控初始化成功');
    // } else {
    //     console.error('[Monitor] 物品获取监控初始化失败');
    // }

    // ... existing code ...
    // } catch (e) {
    //     console.error('[Monitor] 初始化监控器失败:', e);
    // }
}

module.exports = {
    initGSHooks,
    init,
};

rpc.exports = {
    init(stage) {
        // try {
        //     console.log("[GS] 开始安装功能...");

        //     // 初始化击杀监控
        //     try {
        //         applyKillMonitor();
        //         console.log('[GS] 击杀监控系统已启动');
        //     } catch (e) {
        //         console.error('[GS] 击杀监控系统安装失败:', e);
        //     }

        //     // 初始化物品使用监控
        //     initItemUsageMonitor();

        //     // 初始化物品拾取监控
        //     initItemPickupMonitor();

        //     // 初始化物品获取监控
        //     try {
        //         initItemGainMonitor();
        //         console.log('[GS] 物品获取监控已启动');
        //     } catch (e) {
        //         console.error('[GS] 物品获取监控启动失败:', e);
        //     }

        //     // 初始化符文合成修改
        //     try {
        //         initFuwen();
        //         console.log('[GS] 符文合成限制移除成功');
        //     } catch (e) {
        //         console.error('[GS] 符文合成限制移除失败:', e);
        //     }

        //     // 初始化装备效果监控系统
        //     try {
        //         equipmentEffectMonitor.init();
        //         console.log('[GS] 装备效果监控系统已启动');
        //     } catch (e) {
        //         console.error('[GS] 装备效果监控系统启动失败:', e);
        //     }


        try {
            applyChatHook();
            console.log('[GS] 聊天监控系统已启动');
        } catch (e) {
            console.error('[GS] 聊天监控系统启动失败:', e);
        }
        applyProgressBarHooks();
        // applyRuneScoreHook();
        applyChatHook();
        applyCombatStateHook();
        applyOtherHooks();
        applyReinforceRates();
        applyPropertyRates();
        // applyUpgradeRates();
        // hookEquipmentRange();
        mountDelayHook.install();

        // 启动强化概率自动更新
        startAutoUpdate();

        // 显示当前概率信息
        setTimeout(() => {
            showCurrentRates();
        }, 1000);

        // 初始化在线奖励系统
        // try {
        //     onlineRewards.init();
        //     console.log('[GS] 在线奖励系统已启动');
        // } catch (e) {
        //     console.error('[GS] 在线奖励系统启动失败:', e);
        // }
        // 初始化技能无职业限制功能
        try {
            skillNoLimit.init();
            console.log('[GS] 技能职业限制已移除');
        } catch (e) {
            console.error('[GS] 技能职业限制移除失败:', e);
        }

        console.log('[GS] 所有功能已成功安装');
        return true;
        // } catch (e) {
        //     console.error('[GS] Init error:', e);
        //     return false;
        // }
    }
};
