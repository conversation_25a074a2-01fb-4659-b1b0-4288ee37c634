# 强化概率管理系统

## 概述
这个系统提供了灵活的强化概率管理功能，支持：
- 默认概率配置
- 时间段概率配置
- 角色专属概率配置
- 手动概率控制

## 配置开关

在文件顶部的配置区域可以控制各种功能：

```javascript
const CONFIG = {
    enableTimeSlots: false,        // 是否启用时间段概率
    enablePlayerSpecific: true,    // 是否启用角色专属配置
    debugMode: false              // 调试模式
};
```

## 时间段配置

### 自动时间段
系统会根据当前时间自动选择对应的概率表：

- **早晨时段 (6:00-12:00)**: 高成功率，适合进行重要的强化
- **下午时段 (12:00-18:00)**: 中等成功率，平衡的强化时机
- **晚上时段 (18:00-24:00)**: 低成功率，需要谨慎强化
- **深夜时段 (0:00-6:00)**: 极低成功率，高风险高刺激

### 概率特点

#### 炼器概率
- **早晨**: 成功率最高，爆装率最低
- **下午**: 成功率中等，适合日常强化
- **晚上**: 成功率较低，爆装风险增加
- **深夜**: 成功率极低，爆装风险极高

#### 灌注概率
- **早晨**: 成功率最高，清除率最低
- **下午**: 成功率中等，平衡的选择
- **晚上**: 成功率较低，清除风险增加
- **深夜**: 成功率极低，清除风险极高

## 使用方法

### 1. 配置管理

```javascript
const reinforce = require('./src/gs/reinforce');

// 启用时间段概率
reinforce.setConfig('enableTimeSlots', true);

// 启用角色专属配置
reinforce.setConfig('enablePlayerSpecific', true);

// 启用调试模式
reinforce.setConfig('debugMode', true);

// 查看当前配置
reinforce.getConfig();
```

### 2. 角色专属配置

```javascript
// 添加角色专属配置
const playerId = "12345";
const playerName = "测试角色";
const reinforceRates = {
    0: [0.95, 0.0, 0.0, 0.06, 0.0],  // 95%成功率
    1: [0.90, 0.0, 0.0, 0.11, 0.0],  // 90%成功率
    // ... 更多等级
};
const propertyRates = {
    0: [0.95, 0.0, 0.06, 0.0, 0.0],  // 95%成功率
    1: [0.90, 0.0, 0.11, 0.0, 0.0],  // 90%成功率
    // ... 更多等级
};

reinforce.addPlayerConfig(playerId, playerName, reinforceRates, propertyRates);

// 查看所有角色配置
reinforce.listPlayerConfigs();

// 删除角色配置
reinforce.removePlayerConfig(playerId);
```

### 3. 时间段控制

```javascript
// 手动设置时间段
reinforce.setManualTimeSlot('morning');    // 早晨时段
reinforce.setManualTimeSlot('afternoon');  // 下午时段
reinforce.setManualTimeSlot('evening');    // 晚上时段
reinforce.setManualTimeSlot('night');      // 深夜时段
reinforce.setManualTimeSlot('default');    // 默认概率
reinforce.setManualTimeSlot(null);         // 恢复自动模式

// 查看当前概率信息
reinforce.showCurrentRates();
```

## 功能特性

1. **多层级配置**: 角色专属 > 时间段 > 默认配置的优先级
2. **灵活开关**: 可以独立控制各种功能的启用状态
3. **角色专属**: 支持为特定角色ID设置专属概率
4. **时间段控制**: 根据时间自动或手动切换概率
5. **实时生效**: 配置修改后立即应用到游戏中
6. **调试支持**: 详细的日志记录，方便调试和监控

## 配置优先级

系统按以下优先级选择概率配置：

1. **角色专属配置** (最高优先级)
   - 如果启用且当前角色有专属配置，使用角色专属配置

2. **时间段配置** (中等优先级)
   - 如果启用时间段功能，根据当前时间选择对应配置

3. **默认配置** (最低优先级)
   - 作为兜底配置，始终可用

## 注意事项

1. 手动设置时间段后，系统不会自动切换，需要手动恢复自动模式
2. 概率修改会立即生效，无需重启游戏
3. 系统启动时会显示当前的概率信息
4. 建议在早晨时段进行重要装备的强化

## 技术实现

- 使用 JavaScript Date 对象获取当前时间
- 通过 Frida 的内存修改功能直接修改游戏内的概率表
- 使用定时器实现自动更新功能
- 支持热重载，修改代码后会自动应用新的配置

## 自定义配置

如果需要自定义概率，可以修改 `src/gs/reinforce.js` 文件中的 `reinforceRates` 和 `propertyRates` 对象。每个时间段都有独立的概率配置，可以根据需要调整。

概率数组格式：`[成功率, 降级率, 清除属性率, 爆装率, 不降级率]`

例如：
- `[0.95, 0.0, 0.0, 0.05, 0.0]` 表示 95% 成功率，5% 爆装率
- `[0.50, 0.0, 0.50, 0.0, 0.0]` 表示 50% 成功率，50% 清除属性率
