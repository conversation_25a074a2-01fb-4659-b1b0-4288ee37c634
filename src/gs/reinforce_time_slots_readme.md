# 时间段强化概率系统

## 概述
这个系统允许根据不同的时间段自动调整强化和灌注的成功概率，让游戏体验更加丰富和策略性。

## 时间段配置

### 自动时间段
系统会根据当前时间自动选择对应的概率表：

- **早晨时段 (6:00-12:00)**: 高成功率，适合进行重要的强化
- **下午时段 (12:00-18:00)**: 中等成功率，平衡的强化时机
- **晚上时段 (18:00-24:00)**: 低成功率，需要谨慎强化
- **深夜时段 (0:00-6:00)**: 极低成功率，高风险高刺激

### 概率特点

#### 炼器概率
- **早晨**: 成功率最高，爆装率最低
- **下午**: 成功率中等，适合日常强化
- **晚上**: 成功率较低，爆装风险增加
- **深夜**: 成功率极低，爆装风险极高

#### 灌注概率
- **早晨**: 成功率最高，清除率最低
- **下午**: 成功率中等，平衡的选择
- **晚上**: 成功率较低，清除风险增加
- **深夜**: 成功率极低，清除风险极高

## 使用方法

### 自动模式（默认）
系统启动后会自动根据当前时间选择对应的概率表，并每小时检查一次时间变化。

### 手动模式
可以通过以下方式手动设置时间段：

```javascript
// 在游戏控制台中执行
const reinforce = require('./src/gs/reinforce');

// 设置为早晨时段（高成功率）
reinforce.setManualTimeSlot('morning');

// 设置为下午时段（中等成功率）
reinforce.setManualTimeSlot('afternoon');

// 设置为晚上时段（低成功率）
reinforce.setManualTimeSlot('evening');

// 设置为深夜时段（极低成功率）
reinforce.setManualTimeSlot('night');

// 设置为默认概率
reinforce.setManualTimeSlot('default');

// 恢复自动模式
reinforce.setManualTimeSlot(null);

// 查看当前概率信息
reinforce.showCurrentRates();
```

## 功能特性

1. **自动时间检测**: 系统会自动检测当前时间并应用对应的概率表
2. **手动控制**: 支持手动设置时间段，方便测试和特殊需求
3. **定时更新**: 每小时自动检查时间变化并更新概率
4. **概率显示**: 可以查看当前使用的概率信息
5. **日志记录**: 详细的日志记录，方便调试和监控

## 注意事项

1. 手动设置时间段后，系统不会自动切换，需要手动恢复自动模式
2. 概率修改会立即生效，无需重启游戏
3. 系统启动时会显示当前的概率信息
4. 建议在早晨时段进行重要装备的强化

## 技术实现

- 使用 JavaScript Date 对象获取当前时间
- 通过 Frida 的内存修改功能直接修改游戏内的概率表
- 使用定时器实现自动更新功能
- 支持热重载，修改代码后会自动应用新的配置

## 自定义配置

如果需要自定义概率，可以修改 `src/gs/reinforce.js` 文件中的 `reinforceRates` 和 `propertyRates` 对象。每个时间段都有独立的概率配置，可以根据需要调整。

概率数组格式：`[成功率, 降级率, 清除属性率, 爆装率, 不降级率]`

例如：
- `[0.95, 0.0, 0.0, 0.05, 0.0]` 表示 95% 成功率，5% 爆装率
- `[0.50, 0.0, 0.50, 0.0, 0.0]` 表示 50% 成功率，50% 清除属性率
