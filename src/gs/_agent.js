(function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({1:[function(require,module,exports){
'use strict'

exports.byteLength = byteLength
exports.toByteArray = toByteArray
exports.fromByteArray = fromByteArray

var lookup = []
var revLookup = []
var Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array

var code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
for (var i = 0, len = code.length; i < len; ++i) {
  lookup[i] = code[i]
  revLookup[code.charCodeAt(i)] = i
}

// Support decoding URL-safe base64 strings, as Node.js does.
// See: https://en.wikipedia.org/wiki/Base64#URL_applications
revLookup['-'.charCodeAt(0)] = 62
revLookup['_'.charCodeAt(0)] = 63

function getLens (b64) {
  var len = b64.length

  if (len % 4 > 0) {
    throw new Error('Invalid string. Length must be a multiple of 4')
  }

  // Trim off extra bytes after placeholder bytes are found
  // See: https://github.com/beatgammit/base64-js/issues/42
  var validLen = b64.indexOf('=')
  if (validLen === -1) validLen = len

  var placeHoldersLen = validLen === len
    ? 0
    : 4 - (validLen % 4)

  return [validLen, placeHoldersLen]
}

// base64 is 4/3 + up to two characters of the original data
function byteLength (b64) {
  var lens = getLens(b64)
  var validLen = lens[0]
  var placeHoldersLen = lens[1]
  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen
}

function _byteLength (b64, validLen, placeHoldersLen) {
  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen
}

function toByteArray (b64) {
  var tmp
  var lens = getLens(b64)
  var validLen = lens[0]
  var placeHoldersLen = lens[1]

  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen))

  var curByte = 0

  // if there are placeholders, only get up to the last complete 4 chars
  var len = placeHoldersLen > 0
    ? validLen - 4
    : validLen

  var i
  for (i = 0; i < len; i += 4) {
    tmp =
      (revLookup[b64.charCodeAt(i)] << 18) |
      (revLookup[b64.charCodeAt(i + 1)] << 12) |
      (revLookup[b64.charCodeAt(i + 2)] << 6) |
      revLookup[b64.charCodeAt(i + 3)]
    arr[curByte++] = (tmp >> 16) & 0xFF
    arr[curByte++] = (tmp >> 8) & 0xFF
    arr[curByte++] = tmp & 0xFF
  }

  if (placeHoldersLen === 2) {
    tmp =
      (revLookup[b64.charCodeAt(i)] << 2) |
      (revLookup[b64.charCodeAt(i + 1)] >> 4)
    arr[curByte++] = tmp & 0xFF
  }

  if (placeHoldersLen === 1) {
    tmp =
      (revLookup[b64.charCodeAt(i)] << 10) |
      (revLookup[b64.charCodeAt(i + 1)] << 4) |
      (revLookup[b64.charCodeAt(i + 2)] >> 2)
    arr[curByte++] = (tmp >> 8) & 0xFF
    arr[curByte++] = tmp & 0xFF
  }

  return arr
}

function tripletToBase64 (num) {
  return lookup[num >> 18 & 0x3F] +
    lookup[num >> 12 & 0x3F] +
    lookup[num >> 6 & 0x3F] +
    lookup[num & 0x3F]
}

function encodeChunk (uint8, start, end) {
  var tmp
  var output = []
  for (var i = start; i < end; i += 3) {
    tmp =
      ((uint8[i] << 16) & 0xFF0000) +
      ((uint8[i + 1] << 8) & 0xFF00) +
      (uint8[i + 2] & 0xFF)
    output.push(tripletToBase64(tmp))
  }
  return output.join('')
}

function fromByteArray (uint8) {
  var tmp
  var len = uint8.length
  var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes
  var parts = []
  var maxChunkLength = 16383 // must be multiple of 3

  // go through the array every three bytes, we'll deal with trailing stuff later
  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {
    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)))
  }

  // pad the end with zeros, but make sure to not forget the extra bytes
  if (extraBytes === 1) {
    tmp = uint8[len - 1]
    parts.push(
      lookup[tmp >> 2] +
      lookup[(tmp << 4) & 0x3F] +
      '=='
    )
  } else if (extraBytes === 2) {
    tmp = (uint8[len - 2] << 8) + uint8[len - 1]
    parts.push(
      lookup[tmp >> 10] +
      lookup[(tmp >> 4) & 0x3F] +
      lookup[(tmp << 2) & 0x3F] +
      '='
    )
  }

  return parts.join('')
}

},{}],2:[function(require,module,exports){
(function (global){(function (){
/*
 * Short-circuit auto-detection in the buffer module to avoid a Duktape
 * compatibility issue with __proto__.
 */
global.TYPED_ARRAY_SUPPORT = true;

module.exports = require('buffer/');

}).call(this)}).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})

},{"buffer/":3}],3:[function(require,module,exports){
(function (Buffer){(function (){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
/* eslint-disable no-proto */

'use strict'

var base64 = require('base64-js')
var ieee754 = require('ieee754')
var customInspectSymbol =
  (typeof Symbol === 'function' && typeof Symbol['for'] === 'function') // eslint-disable-line dot-notation
    ? Symbol['for']('nodejs.util.inspect.custom') // eslint-disable-line dot-notation
    : null

exports.Buffer = Buffer
exports.SlowBuffer = SlowBuffer
exports.INSPECT_MAX_BYTES = 50

var K_MAX_LENGTH = 0x7fffffff
exports.kMaxLength = K_MAX_LENGTH

/**
 * If `Buffer.TYPED_ARRAY_SUPPORT`:
 *   === true    Use Uint8Array implementation (fastest)
 *   === false   Print warning and recommend using `buffer` v4.x which has an Object
 *               implementation (most compatible, even IE6)
 *
 * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,
 * Opera 11.6+, iOS 4.2+.
 *
 * We report that the browser does not support typed arrays if the are not subclassable
 * using __proto__. Firefox 4-29 lacks support for adding new properties to `Uint8Array`
 * (See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438). IE 10 lacks support
 * for __proto__ and has a buggy typed array implementation.
 */
Buffer.TYPED_ARRAY_SUPPORT = typedArraySupport()

if (!Buffer.TYPED_ARRAY_SUPPORT && typeof console !== 'undefined' &&
    typeof console.error === 'function') {
  console.error(
    'This browser lacks typed array (Uint8Array) support which is required by ' +
    '`buffer` v5.x. Use `buffer` v4.x if you require old browser support.'
  )
}

function typedArraySupport () {
  // Can typed array instances can be augmented?
  try {
    var arr = new Uint8Array(1)
    var proto = { foo: function () { return 42 } }
    Object.setPrototypeOf(proto, Uint8Array.prototype)
    Object.setPrototypeOf(arr, proto)
    return arr.foo() === 42
  } catch (e) {
    return false
  }
}

Object.defineProperty(Buffer.prototype, 'parent', {
  enumerable: true,
  get: function () {
    if (!Buffer.isBuffer(this)) return undefined
    return this.buffer
  }
})

Object.defineProperty(Buffer.prototype, 'offset', {
  enumerable: true,
  get: function () {
    if (!Buffer.isBuffer(this)) return undefined
    return this.byteOffset
  }
})

function createBuffer (length) {
  if (length > K_MAX_LENGTH) {
    throw new RangeError('The value "' + length + '" is invalid for option "size"')
  }
  // Return an augmented `Uint8Array` instance
  var buf = new Uint8Array(length)
  Object.setPrototypeOf(buf, Buffer.prototype)
  return buf
}

/**
 * The Buffer constructor returns instances of `Uint8Array` that have their
 * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of
 * `Uint8Array`, so the returned instances will have all the node `Buffer` methods
 * and the `Uint8Array` methods. Square bracket notation works as expected -- it
 * returns a single octet.
 *
 * The `Uint8Array` prototype remains unmodified.
 */

function Buffer (arg, encodingOrOffset, length) {
  // Common case.
  if (typeof arg === 'number') {
    if (typeof encodingOrOffset === 'string') {
      throw new TypeError(
        'The "string" argument must be of type string. Received type number'
      )
    }
    return allocUnsafe(arg)
  }
  return from(arg, encodingOrOffset, length)
}

Buffer.poolSize = 8192 // not used by this implementation

function from (value, encodingOrOffset, length) {
  if (typeof value === 'string') {
    return fromString(value, encodingOrOffset)
  }

  if (ArrayBuffer.isView(value)) {
    return fromArrayView(value)
  }

  if (value == null) {
    throw new TypeError(
      'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +
      'or Array-like Object. Received type ' + (typeof value)
    )
  }

  if (isInstance(value, ArrayBuffer) ||
      (value && isInstance(value.buffer, ArrayBuffer))) {
    return fromArrayBuffer(value, encodingOrOffset, length)
  }

  if (typeof SharedArrayBuffer !== 'undefined' &&
      (isInstance(value, SharedArrayBuffer) ||
      (value && isInstance(value.buffer, SharedArrayBuffer)))) {
    return fromArrayBuffer(value, encodingOrOffset, length)
  }

  if (typeof value === 'number') {
    throw new TypeError(
      'The "value" argument must not be of type number. Received type number'
    )
  }

  var valueOf = value.valueOf && value.valueOf()
  if (valueOf != null && valueOf !== value) {
    return Buffer.from(valueOf, encodingOrOffset, length)
  }

  var b = fromObject(value)
  if (b) return b

  if (typeof Symbol !== 'undefined' && Symbol.toPrimitive != null &&
      typeof value[Symbol.toPrimitive] === 'function') {
    return Buffer.from(
      value[Symbol.toPrimitive]('string'), encodingOrOffset, length
    )
  }

  throw new TypeError(
    'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +
    'or Array-like Object. Received type ' + (typeof value)
  )
}

/**
 * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError
 * if value is a number.
 * Buffer.from(str[, encoding])
 * Buffer.from(array)
 * Buffer.from(buffer)
 * Buffer.from(arrayBuffer[, byteOffset[, length]])
 **/
Buffer.from = function (value, encodingOrOffset, length) {
  return from(value, encodingOrOffset, length)
}

// Note: Change prototype *after* Buffer.from is defined to workaround Chrome bug:
// https://github.com/feross/buffer/pull/148
Object.setPrototypeOf(Buffer.prototype, Uint8Array.prototype)
Object.setPrototypeOf(Buffer, Uint8Array)

function assertSize (size) {
  if (typeof size !== 'number') {
    throw new TypeError('"size" argument must be of type number')
  } else if (size < 0) {
    throw new RangeError('The value "' + size + '" is invalid for option "size"')
  }
}

function alloc (size, fill, encoding) {
  assertSize(size)
  if (size <= 0) {
    return createBuffer(size)
  }
  if (fill !== undefined) {
    // Only pay attention to encoding if it's a string. This
    // prevents accidentally sending in a number that would
    // be interpreted as a start offset.
    return typeof encoding === 'string'
      ? createBuffer(size).fill(fill, encoding)
      : createBuffer(size).fill(fill)
  }
  return createBuffer(size)
}

/**
 * Creates a new filled Buffer instance.
 * alloc(size[, fill[, encoding]])
 **/
Buffer.alloc = function (size, fill, encoding) {
  return alloc(size, fill, encoding)
}

function allocUnsafe (size) {
  assertSize(size)
  return createBuffer(size < 0 ? 0 : checked(size) | 0)
}

/**
 * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.
 * */
Buffer.allocUnsafe = function (size) {
  return allocUnsafe(size)
}
/**
 * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.
 */
Buffer.allocUnsafeSlow = function (size) {
  return allocUnsafe(size)
}

function fromString (string, encoding) {
  if (typeof encoding !== 'string' || encoding === '') {
    encoding = 'utf8'
  }

  if (!Buffer.isEncoding(encoding)) {
    throw new TypeError('Unknown encoding: ' + encoding)
  }

  var length = byteLength(string, encoding) | 0
  var buf = createBuffer(length)

  var actual = buf.write(string, encoding)

  if (actual !== length) {
    // Writing a hex string, for example, that contains invalid characters will
    // cause everything after the first invalid character to be ignored. (e.g.
    // 'abxxcd' will be treated as 'ab')
    buf = buf.slice(0, actual)
  }

  return buf
}

function fromArrayLike (array) {
  var length = array.length < 0 ? 0 : checked(array.length) | 0
  var buf = createBuffer(length)
  for (var i = 0; i < length; i += 1) {
    buf[i] = array[i] & 255
  }
  return buf
}

function fromArrayView (arrayView) {
  if (isInstance(arrayView, Uint8Array)) {
    var copy = new Uint8Array(arrayView)
    return fromArrayBuffer(copy.buffer, copy.byteOffset, copy.byteLength)
  }
  return fromArrayLike(arrayView)
}

function fromArrayBuffer (array, byteOffset, length) {
  if (byteOffset < 0 || array.byteLength < byteOffset) {
    throw new RangeError('"offset" is outside of buffer bounds')
  }

  if (array.byteLength < byteOffset + (length || 0)) {
    throw new RangeError('"length" is outside of buffer bounds')
  }

  var buf
  if (byteOffset === undefined && length === undefined) {
    buf = new Uint8Array(array)
  } else if (length === undefined) {
    buf = new Uint8Array(array, byteOffset)
  } else {
    buf = new Uint8Array(array, byteOffset, length)
  }

  // Return an augmented `Uint8Array` instance
  Object.setPrototypeOf(buf, Buffer.prototype)

  return buf
}

function fromObject (obj) {
  if (Buffer.isBuffer(obj)) {
    var len = checked(obj.length) | 0
    var buf = createBuffer(len)

    if (buf.length === 0) {
      return buf
    }

    obj.copy(buf, 0, 0, len)
    return buf
  }

  if (obj.length !== undefined) {
    if (typeof obj.length !== 'number' || numberIsNaN(obj.length)) {
      return createBuffer(0)
    }
    return fromArrayLike(obj)
  }

  if (obj.type === 'Buffer' && Array.isArray(obj.data)) {
    return fromArrayLike(obj.data)
  }
}

function checked (length) {
  // Note: cannot use `length < K_MAX_LENGTH` here because that fails when
  // length is NaN (which is otherwise coerced to zero.)
  if (length >= K_MAX_LENGTH) {
    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +
                         'size: 0x' + K_MAX_LENGTH.toString(16) + ' bytes')
  }
  return length | 0
}

function SlowBuffer (length) {
  if (+length != length) { // eslint-disable-line eqeqeq
    length = 0
  }
  return Buffer.alloc(+length)
}

Buffer.isBuffer = function isBuffer (b) {
  return b != null && b._isBuffer === true &&
    b !== Buffer.prototype // so Buffer.isBuffer(Buffer.prototype) will be false
}

Buffer.compare = function compare (a, b) {
  if (isInstance(a, Uint8Array)) a = Buffer.from(a, a.offset, a.byteLength)
  if (isInstance(b, Uint8Array)) b = Buffer.from(b, b.offset, b.byteLength)
  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {
    throw new TypeError(
      'The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array'
    )
  }

  if (a === b) return 0

  var x = a.length
  var y = b.length

  for (var i = 0, len = Math.min(x, y); i < len; ++i) {
    if (a[i] !== b[i]) {
      x = a[i]
      y = b[i]
      break
    }
  }

  if (x < y) return -1
  if (y < x) return 1
  return 0
}

Buffer.isEncoding = function isEncoding (encoding) {
  switch (String(encoding).toLowerCase()) {
    case 'hex':
    case 'utf8':
    case 'utf-8':
    case 'ascii':
    case 'latin1':
    case 'binary':
    case 'base64':
    case 'ucs2':
    case 'ucs-2':
    case 'utf16le':
    case 'utf-16le':
      return true
    default:
      return false
  }
}

Buffer.concat = function concat (list, length) {
  if (!Array.isArray(list)) {
    throw new TypeError('"list" argument must be an Array of Buffers')
  }

  if (list.length === 0) {
    return Buffer.alloc(0)
  }

  var i
  if (length === undefined) {
    length = 0
    for (i = 0; i < list.length; ++i) {
      length += list[i].length
    }
  }

  var buffer = Buffer.allocUnsafe(length)
  var pos = 0
  for (i = 0; i < list.length; ++i) {
    var buf = list[i]
    if (isInstance(buf, Uint8Array)) {
      if (pos + buf.length > buffer.length) {
        Buffer.from(buf).copy(buffer, pos)
      } else {
        Uint8Array.prototype.set.call(
          buffer,
          buf,
          pos
        )
      }
    } else if (!Buffer.isBuffer(buf)) {
      throw new TypeError('"list" argument must be an Array of Buffers')
    } else {
      buf.copy(buffer, pos)
    }
    pos += buf.length
  }
  return buffer
}

function byteLength (string, encoding) {
  if (Buffer.isBuffer(string)) {
    return string.length
  }
  if (ArrayBuffer.isView(string) || isInstance(string, ArrayBuffer)) {
    return string.byteLength
  }
  if (typeof string !== 'string') {
    throw new TypeError(
      'The "string" argument must be one of type string, Buffer, or ArrayBuffer. ' +
      'Received type ' + typeof string
    )
  }

  var len = string.length
  var mustMatch = (arguments.length > 2 && arguments[2] === true)
  if (!mustMatch && len === 0) return 0

  // Use a for loop to avoid recursion
  var loweredCase = false
  for (;;) {
    switch (encoding) {
      case 'ascii':
      case 'latin1':
      case 'binary':
        return len
      case 'utf8':
      case 'utf-8':
        return utf8ToBytes(string).length
      case 'ucs2':
      case 'ucs-2':
      case 'utf16le':
      case 'utf-16le':
        return len * 2
      case 'hex':
        return len >>> 1
      case 'base64':
        return base64ToBytes(string).length
      default:
        if (loweredCase) {
          return mustMatch ? -1 : utf8ToBytes(string).length // assume utf8
        }
        encoding = ('' + encoding).toLowerCase()
        loweredCase = true
    }
  }
}
Buffer.byteLength = byteLength

function slowToString (encoding, start, end) {
  var loweredCase = false

  // No need to verify that "this.length <= MAX_UINT32" since it's a read-only
  // property of a typed array.

  // This behaves neither like String nor Uint8Array in that we set start/end
  // to their upper/lower bounds if the value passed is out of range.
  // undefined is handled specially as per ECMA-262 6th Edition,
  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.
  if (start === undefined || start < 0) {
    start = 0
  }
  // Return early if start > this.length. Done here to prevent potential uint32
  // coercion fail below.
  if (start > this.length) {
    return ''
  }

  if (end === undefined || end > this.length) {
    end = this.length
  }

  if (end <= 0) {
    return ''
  }

  // Force coercion to uint32. This will also coerce falsey/NaN values to 0.
  end >>>= 0
  start >>>= 0

  if (end <= start) {
    return ''
  }

  if (!encoding) encoding = 'utf8'

  while (true) {
    switch (encoding) {
      case 'hex':
        return hexSlice(this, start, end)

      case 'utf8':
      case 'utf-8':
        return utf8Slice(this, start, end)

      case 'ascii':
        return asciiSlice(this, start, end)

      case 'latin1':
      case 'binary':
        return latin1Slice(this, start, end)

      case 'base64':
        return base64Slice(this, start, end)

      case 'ucs2':
      case 'ucs-2':
      case 'utf16le':
      case 'utf-16le':
        return utf16leSlice(this, start, end)

      default:
        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)
        encoding = (encoding + '').toLowerCase()
        loweredCase = true
    }
  }
}

// This property is used by `Buffer.isBuffer` (and the `is-buffer` npm package)
// to detect a Buffer instance. It's not possible to use `instanceof Buffer`
// reliably in a browserify context because there could be multiple different
// copies of the 'buffer' package in use. This method works even for Buffer
// instances that were created from another copy of the `buffer` package.
// See: https://github.com/feross/buffer/issues/154
Buffer.prototype._isBuffer = true

function swap (b, n, m) {
  var i = b[n]
  b[n] = b[m]
  b[m] = i
}

Buffer.prototype.swap16 = function swap16 () {
  var len = this.length
  if (len % 2 !== 0) {
    throw new RangeError('Buffer size must be a multiple of 16-bits')
  }
  for (var i = 0; i < len; i += 2) {
    swap(this, i, i + 1)
  }
  return this
}

Buffer.prototype.swap32 = function swap32 () {
  var len = this.length
  if (len % 4 !== 0) {
    throw new RangeError('Buffer size must be a multiple of 32-bits')
  }
  for (var i = 0; i < len; i += 4) {
    swap(this, i, i + 3)
    swap(this, i + 1, i + 2)
  }
  return this
}

Buffer.prototype.swap64 = function swap64 () {
  var len = this.length
  if (len % 8 !== 0) {
    throw new RangeError('Buffer size must be a multiple of 64-bits')
  }
  for (var i = 0; i < len; i += 8) {
    swap(this, i, i + 7)
    swap(this, i + 1, i + 6)
    swap(this, i + 2, i + 5)
    swap(this, i + 3, i + 4)
  }
  return this
}

Buffer.prototype.toString = function toString () {
  var length = this.length
  if (length === 0) return ''
  if (arguments.length === 0) return utf8Slice(this, 0, length)
  return slowToString.apply(this, arguments)
}

Buffer.prototype.toLocaleString = Buffer.prototype.toString

Buffer.prototype.equals = function equals (b) {
  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer')
  if (this === b) return true
  return Buffer.compare(this, b) === 0
}

Buffer.prototype.inspect = function inspect () {
  var str = ''
  var max = exports.INSPECT_MAX_BYTES
  str = this.toString('hex', 0, max).replace(/(.{2})/g, '$1 ').trim()
  if (this.length > max) str += ' ... '
  return '<Buffer ' + str + '>'
}
if (customInspectSymbol) {
  Buffer.prototype[customInspectSymbol] = Buffer.prototype.inspect
}

Buffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {
  if (isInstance(target, Uint8Array)) {
    target = Buffer.from(target, target.offset, target.byteLength)
  }
  if (!Buffer.isBuffer(target)) {
    throw new TypeError(
      'The "target" argument must be one of type Buffer or Uint8Array. ' +
      'Received type ' + (typeof target)
    )
  }

  if (start === undefined) {
    start = 0
  }
  if (end === undefined) {
    end = target ? target.length : 0
  }
  if (thisStart === undefined) {
    thisStart = 0
  }
  if (thisEnd === undefined) {
    thisEnd = this.length
  }

  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {
    throw new RangeError('out of range index')
  }

  if (thisStart >= thisEnd && start >= end) {
    return 0
  }
  if (thisStart >= thisEnd) {
    return -1
  }
  if (start >= end) {
    return 1
  }

  start >>>= 0
  end >>>= 0
  thisStart >>>= 0
  thisEnd >>>= 0

  if (this === target) return 0

  var x = thisEnd - thisStart
  var y = end - start
  var len = Math.min(x, y)

  var thisCopy = this.slice(thisStart, thisEnd)
  var targetCopy = target.slice(start, end)

  for (var i = 0; i < len; ++i) {
    if (thisCopy[i] !== targetCopy[i]) {
      x = thisCopy[i]
      y = targetCopy[i]
      break
    }
  }

  if (x < y) return -1
  if (y < x) return 1
  return 0
}

// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,
// OR the last index of `val` in `buffer` at offset <= `byteOffset`.
//
// Arguments:
// - buffer - a Buffer to search
// - val - a string, Buffer, or number
// - byteOffset - an index into `buffer`; will be clamped to an int32
// - encoding - an optional encoding, relevant is val is a string
// - dir - true for indexOf, false for lastIndexOf
function bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {
  // Empty buffer means no match
  if (buffer.length === 0) return -1

  // Normalize byteOffset
  if (typeof byteOffset === 'string') {
    encoding = byteOffset
    byteOffset = 0
  } else if (byteOffset > 0x7fffffff) {
    byteOffset = 0x7fffffff
  } else if (byteOffset < -0x80000000) {
    byteOffset = -0x80000000
  }
  byteOffset = +byteOffset // Coerce to Number.
  if (numberIsNaN(byteOffset)) {
    // byteOffset: it it's undefined, null, NaN, "foo", etc, search whole buffer
    byteOffset = dir ? 0 : (buffer.length - 1)
  }

  // Normalize byteOffset: negative offsets start from the end of the buffer
  if (byteOffset < 0) byteOffset = buffer.length + byteOffset
  if (byteOffset >= buffer.length) {
    if (dir) return -1
    else byteOffset = buffer.length - 1
  } else if (byteOffset < 0) {
    if (dir) byteOffset = 0
    else return -1
  }

  // Normalize val
  if (typeof val === 'string') {
    val = Buffer.from(val, encoding)
  }

  // Finally, search either indexOf (if dir is true) or lastIndexOf
  if (Buffer.isBuffer(val)) {
    // Special case: looking for empty string/buffer always fails
    if (val.length === 0) {
      return -1
    }
    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)
  } else if (typeof val === 'number') {
    val = val & 0xFF // Search for a byte value [0-255]
    if (typeof Uint8Array.prototype.indexOf === 'function') {
      if (dir) {
        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset)
      } else {
        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)
      }
    }
    return arrayIndexOf(buffer, [val], byteOffset, encoding, dir)
  }

  throw new TypeError('val must be string, number or Buffer')
}

function arrayIndexOf (arr, val, byteOffset, encoding, dir) {
  var indexSize = 1
  var arrLength = arr.length
  var valLength = val.length

  if (encoding !== undefined) {
    encoding = String(encoding).toLowerCase()
    if (encoding === 'ucs2' || encoding === 'ucs-2' ||
        encoding === 'utf16le' || encoding === 'utf-16le') {
      if (arr.length < 2 || val.length < 2) {
        return -1
      }
      indexSize = 2
      arrLength /= 2
      valLength /= 2
      byteOffset /= 2
    }
  }

  function read (buf, i) {
    if (indexSize === 1) {
      return buf[i]
    } else {
      return buf.readUInt16BE(i * indexSize)
    }
  }

  var i
  if (dir) {
    var foundIndex = -1
    for (i = byteOffset; i < arrLength; i++) {
      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {
        if (foundIndex === -1) foundIndex = i
        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize
      } else {
        if (foundIndex !== -1) i -= i - foundIndex
        foundIndex = -1
      }
    }
  } else {
    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength
    for (i = byteOffset; i >= 0; i--) {
      var found = true
      for (var j = 0; j < valLength; j++) {
        if (read(arr, i + j) !== read(val, j)) {
          found = false
          break
        }
      }
      if (found) return i
    }
  }

  return -1
}

Buffer.prototype.includes = function includes (val, byteOffset, encoding) {
  return this.indexOf(val, byteOffset, encoding) !== -1
}

Buffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {
  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)
}

Buffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {
  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)
}

function hexWrite (buf, string, offset, length) {
  offset = Number(offset) || 0
  var remaining = buf.length - offset
  if (!length) {
    length = remaining
  } else {
    length = Number(length)
    if (length > remaining) {
      length = remaining
    }
  }

  var strLen = string.length

  if (length > strLen / 2) {
    length = strLen / 2
  }
  for (var i = 0; i < length; ++i) {
    var parsed = parseInt(string.substr(i * 2, 2), 16)
    if (numberIsNaN(parsed)) return i
    buf[offset + i] = parsed
  }
  return i
}

function utf8Write (buf, string, offset, length) {
  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)
}

function asciiWrite (buf, string, offset, length) {
  return blitBuffer(asciiToBytes(string), buf, offset, length)
}

function base64Write (buf, string, offset, length) {
  return blitBuffer(base64ToBytes(string), buf, offset, length)
}

function ucs2Write (buf, string, offset, length) {
  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)
}

Buffer.prototype.write = function write (string, offset, length, encoding) {
  // Buffer#write(string)
  if (offset === undefined) {
    encoding = 'utf8'
    length = this.length
    offset = 0
  // Buffer#write(string, encoding)
  } else if (length === undefined && typeof offset === 'string') {
    encoding = offset
    length = this.length
    offset = 0
  // Buffer#write(string, offset[, length][, encoding])
  } else if (isFinite(offset)) {
    offset = offset >>> 0
    if (isFinite(length)) {
      length = length >>> 0
      if (encoding === undefined) encoding = 'utf8'
    } else {
      encoding = length
      length = undefined
    }
  } else {
    throw new Error(
      'Buffer.write(string, encoding, offset[, length]) is no longer supported'
    )
  }

  var remaining = this.length - offset
  if (length === undefined || length > remaining) length = remaining

  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {
    throw new RangeError('Attempt to write outside buffer bounds')
  }

  if (!encoding) encoding = 'utf8'

  var loweredCase = false
  for (;;) {
    switch (encoding) {
      case 'hex':
        return hexWrite(this, string, offset, length)

      case 'utf8':
      case 'utf-8':
        return utf8Write(this, string, offset, length)

      case 'ascii':
      case 'latin1':
      case 'binary':
        return asciiWrite(this, string, offset, length)

      case 'base64':
        // Warning: maxLength not taken into account in base64Write
        return base64Write(this, string, offset, length)

      case 'ucs2':
      case 'ucs-2':
      case 'utf16le':
      case 'utf-16le':
        return ucs2Write(this, string, offset, length)

      default:
        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)
        encoding = ('' + encoding).toLowerCase()
        loweredCase = true
    }
  }
}

Buffer.prototype.toJSON = function toJSON () {
  return {
    type: 'Buffer',
    data: Array.prototype.slice.call(this._arr || this, 0)
  }
}

function base64Slice (buf, start, end) {
  if (start === 0 && end === buf.length) {
    return base64.fromByteArray(buf)
  } else {
    return base64.fromByteArray(buf.slice(start, end))
  }
}

function utf8Slice (buf, start, end) {
  end = Math.min(buf.length, end)
  var res = []

  var i = start
  while (i < end) {
    var firstByte = buf[i]
    var codePoint = null
    var bytesPerSequence = (firstByte > 0xEF)
      ? 4
      : (firstByte > 0xDF)
          ? 3
          : (firstByte > 0xBF)
              ? 2
              : 1

    if (i + bytesPerSequence <= end) {
      var secondByte, thirdByte, fourthByte, tempCodePoint

      switch (bytesPerSequence) {
        case 1:
          if (firstByte < 0x80) {
            codePoint = firstByte
          }
          break
        case 2:
          secondByte = buf[i + 1]
          if ((secondByte & 0xC0) === 0x80) {
            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F)
            if (tempCodePoint > 0x7F) {
              codePoint = tempCodePoint
            }
          }
          break
        case 3:
          secondByte = buf[i + 1]
          thirdByte = buf[i + 2]
          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {
            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F)
            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {
              codePoint = tempCodePoint
            }
          }
          break
        case 4:
          secondByte = buf[i + 1]
          thirdByte = buf[i + 2]
          fourthByte = buf[i + 3]
          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {
            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F)
            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {
              codePoint = tempCodePoint
            }
          }
      }
    }

    if (codePoint === null) {
      // we did not generate a valid codePoint so insert a
      // replacement char (U+FFFD) and advance only 1 byte
      codePoint = 0xFFFD
      bytesPerSequence = 1
    } else if (codePoint > 0xFFFF) {
      // encode to utf16 (surrogate pair dance)
      codePoint -= 0x10000
      res.push(codePoint >>> 10 & 0x3FF | 0xD800)
      codePoint = 0xDC00 | codePoint & 0x3FF
    }

    res.push(codePoint)
    i += bytesPerSequence
  }

  return decodeCodePointsArray(res)
}

// Based on http://stackoverflow.com/a/22747272/680742, the browser with
// the lowest limit is Chrome, with 0x10000 args.
// We go 1 magnitude less, for safety
var MAX_ARGUMENTS_LENGTH = 0x1000

function decodeCodePointsArray (codePoints) {
  var len = codePoints.length
  if (len <= MAX_ARGUMENTS_LENGTH) {
    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()
  }

  // Decode in chunks to avoid "call stack size exceeded".
  var res = ''
  var i = 0
  while (i < len) {
    res += String.fromCharCode.apply(
      String,
      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)
    )
  }
  return res
}

function asciiSlice (buf, start, end) {
  var ret = ''
  end = Math.min(buf.length, end)

  for (var i = start; i < end; ++i) {
    ret += String.fromCharCode(buf[i] & 0x7F)
  }
  return ret
}

function latin1Slice (buf, start, end) {
  var ret = ''
  end = Math.min(buf.length, end)

  for (var i = start; i < end; ++i) {
    ret += String.fromCharCode(buf[i])
  }
  return ret
}

function hexSlice (buf, start, end) {
  var len = buf.length

  if (!start || start < 0) start = 0
  if (!end || end < 0 || end > len) end = len

  var out = ''
  for (var i = start; i < end; ++i) {
    out += hexSliceLookupTable[buf[i]]
  }
  return out
}

function utf16leSlice (buf, start, end) {
  var bytes = buf.slice(start, end)
  var res = ''
  // If bytes.length is odd, the last 8 bits must be ignored (same as node.js)
  for (var i = 0; i < bytes.length - 1; i += 2) {
    res += String.fromCharCode(bytes[i] + (bytes[i + 1] * 256))
  }
  return res
}

Buffer.prototype.slice = function slice (start, end) {
  var len = this.length
  start = ~~start
  end = end === undefined ? len : ~~end

  if (start < 0) {
    start += len
    if (start < 0) start = 0
  } else if (start > len) {
    start = len
  }

  if (end < 0) {
    end += len
    if (end < 0) end = 0
  } else if (end > len) {
    end = len
  }

  if (end < start) end = start

  var newBuf = this.subarray(start, end)
  // Return an augmented `Uint8Array` instance
  Object.setPrototypeOf(newBuf, Buffer.prototype)

  return newBuf
}

/*
 * Need to make sure that buffer isn't trying to write out of bounds.
 */
function checkOffset (offset, ext, length) {
  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')
  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')
}

Buffer.prototype.readUintLE =
Buffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) checkOffset(offset, byteLength, this.length)

  var val = this[offset]
  var mul = 1
  var i = 0
  while (++i < byteLength && (mul *= 0x100)) {
    val += this[offset + i] * mul
  }

  return val
}

Buffer.prototype.readUintBE =
Buffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) {
    checkOffset(offset, byteLength, this.length)
  }

  var val = this[offset + --byteLength]
  var mul = 1
  while (byteLength > 0 && (mul *= 0x100)) {
    val += this[offset + --byteLength] * mul
  }

  return val
}

Buffer.prototype.readUint8 =
Buffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 1, this.length)
  return this[offset]
}

Buffer.prototype.readUint16LE =
Buffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 2, this.length)
  return this[offset] | (this[offset + 1] << 8)
}

Buffer.prototype.readUint16BE =
Buffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 2, this.length)
  return (this[offset] << 8) | this[offset + 1]
}

Buffer.prototype.readUint32LE =
Buffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)

  return ((this[offset]) |
      (this[offset + 1] << 8) |
      (this[offset + 2] << 16)) +
      (this[offset + 3] * 0x1000000)
}

Buffer.prototype.readUint32BE =
Buffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)

  return (this[offset] * 0x1000000) +
    ((this[offset + 1] << 16) |
    (this[offset + 2] << 8) |
    this[offset + 3])
}

Buffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) checkOffset(offset, byteLength, this.length)

  var val = this[offset]
  var mul = 1
  var i = 0
  while (++i < byteLength && (mul *= 0x100)) {
    val += this[offset + i] * mul
  }
  mul *= 0x80

  if (val >= mul) val -= Math.pow(2, 8 * byteLength)

  return val
}

Buffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) checkOffset(offset, byteLength, this.length)

  var i = byteLength
  var mul = 1
  var val = this[offset + --i]
  while (i > 0 && (mul *= 0x100)) {
    val += this[offset + --i] * mul
  }
  mul *= 0x80

  if (val >= mul) val -= Math.pow(2, 8 * byteLength)

  return val
}

Buffer.prototype.readInt8 = function readInt8 (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 1, this.length)
  if (!(this[offset] & 0x80)) return (this[offset])
  return ((0xff - this[offset] + 1) * -1)
}

Buffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 2, this.length)
  var val = this[offset] | (this[offset + 1] << 8)
  return (val & 0x8000) ? val | 0xFFFF0000 : val
}

Buffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 2, this.length)
  var val = this[offset + 1] | (this[offset] << 8)
  return (val & 0x8000) ? val | 0xFFFF0000 : val
}

Buffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)

  return (this[offset]) |
    (this[offset + 1] << 8) |
    (this[offset + 2] << 16) |
    (this[offset + 3] << 24)
}

Buffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)

  return (this[offset] << 24) |
    (this[offset + 1] << 16) |
    (this[offset + 2] << 8) |
    (this[offset + 3])
}

Buffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)
  return ieee754.read(this, offset, true, 23, 4)
}

Buffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)
  return ieee754.read(this, offset, false, 23, 4)
}

Buffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 8, this.length)
  return ieee754.read(this, offset, true, 52, 8)
}

Buffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 8, this.length)
  return ieee754.read(this, offset, false, 52, 8)
}

function checkInt (buf, value, offset, ext, max, min) {
  if (!Buffer.isBuffer(buf)) throw new TypeError('"buffer" argument must be a Buffer instance')
  if (value > max || value < min) throw new RangeError('"value" argument is out of bounds')
  if (offset + ext > buf.length) throw new RangeError('Index out of range')
}

Buffer.prototype.writeUintLE =
Buffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) {
    var maxBytes = Math.pow(2, 8 * byteLength) - 1
    checkInt(this, value, offset, byteLength, maxBytes, 0)
  }

  var mul = 1
  var i = 0
  this[offset] = value & 0xFF
  while (++i < byteLength && (mul *= 0x100)) {
    this[offset + i] = (value / mul) & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeUintBE =
Buffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) {
    var maxBytes = Math.pow(2, 8 * byteLength) - 1
    checkInt(this, value, offset, byteLength, maxBytes, 0)
  }

  var i = byteLength - 1
  var mul = 1
  this[offset + i] = value & 0xFF
  while (--i >= 0 && (mul *= 0x100)) {
    this[offset + i] = (value / mul) & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeUint8 =
Buffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0)
  this[offset] = (value & 0xff)
  return offset + 1
}

Buffer.prototype.writeUint16LE =
Buffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)
  this[offset] = (value & 0xff)
  this[offset + 1] = (value >>> 8)
  return offset + 2
}

Buffer.prototype.writeUint16BE =
Buffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)
  this[offset] = (value >>> 8)
  this[offset + 1] = (value & 0xff)
  return offset + 2
}

Buffer.prototype.writeUint32LE =
Buffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)
  this[offset + 3] = (value >>> 24)
  this[offset + 2] = (value >>> 16)
  this[offset + 1] = (value >>> 8)
  this[offset] = (value & 0xff)
  return offset + 4
}

Buffer.prototype.writeUint32BE =
Buffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)
  this[offset] = (value >>> 24)
  this[offset + 1] = (value >>> 16)
  this[offset + 2] = (value >>> 8)
  this[offset + 3] = (value & 0xff)
  return offset + 4
}

Buffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) {
    var limit = Math.pow(2, (8 * byteLength) - 1)

    checkInt(this, value, offset, byteLength, limit - 1, -limit)
  }

  var i = 0
  var mul = 1
  var sub = 0
  this[offset] = value & 0xFF
  while (++i < byteLength && (mul *= 0x100)) {
    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {
      sub = 1
    }
    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) {
    var limit = Math.pow(2, (8 * byteLength) - 1)

    checkInt(this, value, offset, byteLength, limit - 1, -limit)
  }

  var i = byteLength - 1
  var mul = 1
  var sub = 0
  this[offset + i] = value & 0xFF
  while (--i >= 0 && (mul *= 0x100)) {
    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {
      sub = 1
    }
    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80)
  if (value < 0) value = 0xff + value + 1
  this[offset] = (value & 0xff)
  return offset + 1
}

Buffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)
  this[offset] = (value & 0xff)
  this[offset + 1] = (value >>> 8)
  return offset + 2
}

Buffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)
  this[offset] = (value >>> 8)
  this[offset + 1] = (value & 0xff)
  return offset + 2
}

Buffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)
  this[offset] = (value & 0xff)
  this[offset + 1] = (value >>> 8)
  this[offset + 2] = (value >>> 16)
  this[offset + 3] = (value >>> 24)
  return offset + 4
}

Buffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)
  if (value < 0) value = 0xffffffff + value + 1
  this[offset] = (value >>> 24)
  this[offset + 1] = (value >>> 16)
  this[offset + 2] = (value >>> 8)
  this[offset + 3] = (value & 0xff)
  return offset + 4
}

function checkIEEE754 (buf, value, offset, ext, max, min) {
  if (offset + ext > buf.length) throw new RangeError('Index out of range')
  if (offset < 0) throw new RangeError('Index out of range')
}

function writeFloat (buf, value, offset, littleEndian, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) {
    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38)
  }
  ieee754.write(buf, value, offset, littleEndian, 23, 4)
  return offset + 4
}

Buffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {
  return writeFloat(this, value, offset, true, noAssert)
}

Buffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {
  return writeFloat(this, value, offset, false, noAssert)
}

function writeDouble (buf, value, offset, littleEndian, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) {
    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308)
  }
  ieee754.write(buf, value, offset, littleEndian, 52, 8)
  return offset + 8
}

Buffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {
  return writeDouble(this, value, offset, true, noAssert)
}

Buffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {
  return writeDouble(this, value, offset, false, noAssert)
}

// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)
Buffer.prototype.copy = function copy (target, targetStart, start, end) {
  if (!Buffer.isBuffer(target)) throw new TypeError('argument should be a Buffer')
  if (!start) start = 0
  if (!end && end !== 0) end = this.length
  if (targetStart >= target.length) targetStart = target.length
  if (!targetStart) targetStart = 0
  if (end > 0 && end < start) end = start

  // Copy 0 bytes; we're done
  if (end === start) return 0
  if (target.length === 0 || this.length === 0) return 0

  // Fatal error conditions
  if (targetStart < 0) {
    throw new RangeError('targetStart out of bounds')
  }
  if (start < 0 || start >= this.length) throw new RangeError('Index out of range')
  if (end < 0) throw new RangeError('sourceEnd out of bounds')

  // Are we oob?
  if (end > this.length) end = this.length
  if (target.length - targetStart < end - start) {
    end = target.length - targetStart + start
  }

  var len = end - start

  if (this === target && typeof Uint8Array.prototype.copyWithin === 'function') {
    // Use built-in when available, missing from IE11
    this.copyWithin(targetStart, start, end)
  } else {
    Uint8Array.prototype.set.call(
      target,
      this.subarray(start, end),
      targetStart
    )
  }

  return len
}

// Usage:
//    buffer.fill(number[, offset[, end]])
//    buffer.fill(buffer[, offset[, end]])
//    buffer.fill(string[, offset[, end]][, encoding])
Buffer.prototype.fill = function fill (val, start, end, encoding) {
  // Handle string cases:
  if (typeof val === 'string') {
    if (typeof start === 'string') {
      encoding = start
      start = 0
      end = this.length
    } else if (typeof end === 'string') {
      encoding = end
      end = this.length
    }
    if (encoding !== undefined && typeof encoding !== 'string') {
      throw new TypeError('encoding must be a string')
    }
    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {
      throw new TypeError('Unknown encoding: ' + encoding)
    }
    if (val.length === 1) {
      var code = val.charCodeAt(0)
      if ((encoding === 'utf8' && code < 128) ||
          encoding === 'latin1') {
        // Fast path: If `val` fits into a single byte, use that numeric value.
        val = code
      }
    }
  } else if (typeof val === 'number') {
    val = val & 255
  } else if (typeof val === 'boolean') {
    val = Number(val)
  }

  // Invalid ranges are not set to a default, so can range check early.
  if (start < 0 || this.length < start || this.length < end) {
    throw new RangeError('Out of range index')
  }

  if (end <= start) {
    return this
  }

  start = start >>> 0
  end = end === undefined ? this.length : end >>> 0

  if (!val) val = 0

  var i
  if (typeof val === 'number') {
    for (i = start; i < end; ++i) {
      this[i] = val
    }
  } else {
    var bytes = Buffer.isBuffer(val)
      ? val
      : Buffer.from(val, encoding)
    var len = bytes.length
    if (len === 0) {
      throw new TypeError('The value "' + val +
        '" is invalid for argument "value"')
    }
    for (i = 0; i < end - start; ++i) {
      this[i + start] = bytes[i % len]
    }
  }

  return this
}

// HELPER FUNCTIONS
// ================

var INVALID_BASE64_RE = /[^+/0-9A-Za-z-_]/g

function base64clean (str) {
  // Node takes equal signs as end of the Base64 encoding
  str = str.split('=')[0]
  // Node strips out invalid characters like \n and \t from the string, base64-js does not
  str = str.trim().replace(INVALID_BASE64_RE, '')
  // Node converts strings with length < 2 to ''
  if (str.length < 2) return ''
  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not
  while (str.length % 4 !== 0) {
    str = str + '='
  }
  return str
}

function utf8ToBytes (string, units) {
  units = units || Infinity
  var codePoint
  var length = string.length
  var leadSurrogate = null
  var bytes = []

  for (var i = 0; i < length; ++i) {
    codePoint = string.charCodeAt(i)

    // is surrogate component
    if (codePoint > 0xD7FF && codePoint < 0xE000) {
      // last char was a lead
      if (!leadSurrogate) {
        // no lead yet
        if (codePoint > 0xDBFF) {
          // unexpected trail
          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
          continue
        } else if (i + 1 === length) {
          // unpaired lead
          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
          continue
        }

        // valid lead
        leadSurrogate = codePoint

        continue
      }

      // 2 leads in a row
      if (codePoint < 0xDC00) {
        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
        leadSurrogate = codePoint
        continue
      }

      // valid surrogate pair
      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000
    } else if (leadSurrogate) {
      // valid bmp char, but last char was a lead
      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
    }

    leadSurrogate = null

    // encode utf8
    if (codePoint < 0x80) {
      if ((units -= 1) < 0) break
      bytes.push(codePoint)
    } else if (codePoint < 0x800) {
      if ((units -= 2) < 0) break
      bytes.push(
        codePoint >> 0x6 | 0xC0,
        codePoint & 0x3F | 0x80
      )
    } else if (codePoint < 0x10000) {
      if ((units -= 3) < 0) break
      bytes.push(
        codePoint >> 0xC | 0xE0,
        codePoint >> 0x6 & 0x3F | 0x80,
        codePoint & 0x3F | 0x80
      )
    } else if (codePoint < 0x110000) {
      if ((units -= 4) < 0) break
      bytes.push(
        codePoint >> 0x12 | 0xF0,
        codePoint >> 0xC & 0x3F | 0x80,
        codePoint >> 0x6 & 0x3F | 0x80,
        codePoint & 0x3F | 0x80
      )
    } else {
      throw new Error('Invalid code point')
    }
  }

  return bytes
}

function asciiToBytes (str) {
  var byteArray = []
  for (var i = 0; i < str.length; ++i) {
    // Node's code seems to be doing this and not & 0x7F..
    byteArray.push(str.charCodeAt(i) & 0xFF)
  }
  return byteArray
}

function utf16leToBytes (str, units) {
  var c, hi, lo
  var byteArray = []
  for (var i = 0; i < str.length; ++i) {
    if ((units -= 2) < 0) break

    c = str.charCodeAt(i)
    hi = c >> 8
    lo = c % 256
    byteArray.push(lo)
    byteArray.push(hi)
  }

  return byteArray
}

function base64ToBytes (str) {
  return base64.toByteArray(base64clean(str))
}

function blitBuffer (src, dst, offset, length) {
  for (var i = 0; i < length; ++i) {
    if ((i + offset >= dst.length) || (i >= src.length)) break
    dst[i + offset] = src[i]
  }
  return i
}

// ArrayBuffer or Uint8Array objects from other contexts (i.e. iframes) do not pass
// the `instanceof` check but they should be treated as of that type.
// See: https://github.com/feross/buffer/issues/166
function isInstance (obj, type) {
  return obj instanceof type ||
    (obj != null && obj.constructor != null && obj.constructor.name != null &&
      obj.constructor.name === type.name)
}
function numberIsNaN (obj) {
  // For IE11 support
  return obj !== obj // eslint-disable-line no-self-compare
}

// Create lookup table for `toString('hex')`
// See: https://github.com/feross/buffer/issues/219
var hexSliceLookupTable = (function () {
  var alphabet = '0123456789abcdef'
  var table = new Array(256)
  for (var i = 0; i < 16; ++i) {
    var i16 = i * 16
    for (var j = 0; j < 16; ++j) {
      table[i16 + j] = alphabet[i] + alphabet[j]
    }
  }
  return table
})()

}).call(this)}).call(this,require("buffer").Buffer)

},{"base64-js":1,"buffer":2,"ieee754":4}],4:[function(require,module,exports){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
exports.read = function (buffer, offset, isLE, mLen, nBytes) {
  var e, m
  var eLen = (nBytes * 8) - mLen - 1
  var eMax = (1 << eLen) - 1
  var eBias = eMax >> 1
  var nBits = -7
  var i = isLE ? (nBytes - 1) : 0
  var d = isLE ? -1 : 1
  var s = buffer[offset + i]

  i += d

  e = s & ((1 << (-nBits)) - 1)
  s >>= (-nBits)
  nBits += eLen
  for (; nBits > 0; e = (e * 256) + buffer[offset + i], i += d, nBits -= 8) {}

  m = e & ((1 << (-nBits)) - 1)
  e >>= (-nBits)
  nBits += mLen
  for (; nBits > 0; m = (m * 256) + buffer[offset + i], i += d, nBits -= 8) {}

  if (e === 0) {
    e = 1 - eBias
  } else if (e === eMax) {
    return m ? NaN : ((s ? -1 : 1) * Infinity)
  } else {
    m = m + Math.pow(2, mLen)
    e = e - eBias
  }
  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)
}

exports.write = function (buffer, value, offset, isLE, mLen, nBytes) {
  var e, m, c
  var eLen = (nBytes * 8) - mLen - 1
  var eMax = (1 << eLen) - 1
  var eBias = eMax >> 1
  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)
  var i = isLE ? 0 : (nBytes - 1)
  var d = isLE ? 1 : -1
  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0

  value = Math.abs(value)

  if (isNaN(value) || value === Infinity) {
    m = isNaN(value) ? 1 : 0
    e = eMax
  } else {
    e = Math.floor(Math.log(value) / Math.LN2)
    if (value * (c = Math.pow(2, -e)) < 1) {
      e--
      c *= 2
    }
    if (e + eBias >= 1) {
      value += rt / c
    } else {
      value += rt * Math.pow(2, 1 - eBias)
    }
    if (value * c >= 2) {
      e++
      c /= 2
    }

    if (e + eBias >= eMax) {
      m = 0
      e = eMax
    } else if (e + eBias >= 1) {
      m = ((value * c) - 1) * Math.pow(2, mLen)
      e = e + eBias
    } else {
      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)
      e = 0
    }
  }

  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}

  e = (e << mLen) | m
  eLen += mLen
  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}

  buffer[offset + i - d] |= s * 128
}

},{}],5:[function(require,module,exports){
class CPlusClass {
    constructor(pointer) {
        this.pointer = pointer;
        if(this.pointer.isNull()) {
            console.log("Warning: Null pointer in constructor");
        }
    }

    ensurePointer() {
        if (this.pointer.isNull()) {
            console.log("Class type:", this.constructor.name);
            console.log("Pointer value:", this.pointer);
            throw new Error("Invalid pointer");
        }
    }
    
    getPointer() {
        return this.pointer;
    }
}

module.exports = CPlusClass;

},{}],6:[function(require,module,exports){
/**
 * 游戏数据管理器
 */

// 全局变量地址
const GLOBAL_ADDRS = {
    DATAMAN: ptr('0x092671A0')  // gmatrix::GetDataMan的地址
};

// vector结构体偏移
const VECTOR = {
    DATA: 0x0,
    FINISH: 0x4,
    CUR_SIZE: 0xC
};

// 所有数据结构的基本偏移定义
const OFFSETS = {
    MONSTER: {
        type: 'monster',
        ARRAY: 0x150,
        SIZE: 0x3A4,
    },
    EQUIPMENT: {
        type: 'equipment',
        ARRAY: 0x30,
        SIZE: 0x2FC,
    },
    NPC: {
        type: 'npc',
        ARRAY: 0x210,
        SIZE: 0x35C,
    },
    SKILLMATTER: {
        type: 'skillmatter',
        ARRAY: 0x410,
        SIZE: 0x88,
    },
    MOUNT_WING_INVENTORY: {
        type: 'mount_wing_inventory',
        ARRAY: 0x420,
        SIZE: 0x144,
    },
};

class BaseEssence {
    constructor(ptr, offsets) {
        this.ptr = ptr;
        this.offsets = offsets;
    }

    getName() {
        return this.ptr.add(this.offsets.NAME).readUtf16String();
    }
}

// 重构DataMan类
class DataMan {
    constructor() {
        this.OFFSETS = OFFSETS;
        this.VECTOR = VECTOR;
        // 新增数据类型映射
        this.dataTypeConfig = {
            monster: {
                offset: OFFSETS.MONSTER.ARRAY,
                size: OFFSETS.MONSTER.SIZE,
                offsets: {
                    ID: 0x0,
                    ID_TYPE: 0x4,
                    NAME: 0x8,
                    PROP: 0x48,
                    DESC: 0x68,
                    MONSTER_DESC: 0x88,
                    FACTION: 0xA8,
                    ENEMY_FACTION: 0xAC,
                    MONSTER_FACTION: 0xB0,
                    FILE_MODEL: 0xB4,
                    HEAD_ICON: 0xB8,
                    BIG_ICON: 0xBC,
                    SIZE: 0xC0,
                    NAME_COLOR: 0xC4,
                    ID_STRATEGY: 0xC8,
                    SELECT_STRATEGY: 0xCC,
                    LEVEL: 0xD0,
                    SHOW_LEVEL: 0xD4,
                    IS_BOSS: 0xD8,
                    KILLED_EXP: 0xDC,
                    KILLED_DROP: 0xE0,
                    IMMUNE_TYPE: 0xE8,
                    SIGHT_RANGE: 0xEC,
                    ATTACK_RANGE: 0xF0,
                    AGGRESSIVE_MODE: 0xF4,
                    AGGRO_RANGE: 0x100,
                    AGGRO_TIME: 0x104,
                    WALK_SPEED: 0x114,
                    RUN_SPEED: 0x118,
                    EXP: 0x130,
                    MONEY_AVERAGE: 0x134,
                    MONEY_VAR: 0x138,
                    HP: 0x13C,
                }
            },
            equipment: {
                offset: OFFSETS.EQUIPMENT.ARRAY,
                size: OFFSETS.EQUIPMENT.SIZE,
                offsets: {
                    ID: 0x0,
                    NAME: 0xC,
                    TYPE: 0x4C,
                    GENDER:0x168
                }
            },
            npc: {
                offset: OFFSETS.NPC.ARRAY,
                size: OFFSETS.NPC.SIZE,
                offsets: {
                    ID: 0x0,
                    NAME: 0x8,
                    ATTACK_RULE: 0x6C,
                }
            },
            skillmatter: {
                offset: OFFSETS.SKILLMATTER.ARRAY,
                size: OFFSETS.SKILLMATTER.SIZE,
                offsets: {
                    ID: 0x0,
                    NAME: 0x4
                }
            },
            mount_wing_inventory:{
                offset: OFFSETS.MOUNT_WING_INVENTORY.ARRAY,
                size: OFFSETS.MOUNT_WING_INVENTORY.SIZE,
                offsets: {
                    ID: 0x0,
                    NAME: 0x4
                }
            },
        };

    }

    // 获取DataMan实例的基址
    getDatamanBase() {
        const datamanPtr = GLOBAL_ADDRS.DATAMAN.readPointer();
        return datamanPtr;
    }

    getVectorBegin(vectorAddr) {
        return vectorAddr.readPointer();
    }

    getVectorEnd(vectorAddr, elementSize) {
        const dataPtr = vectorAddr.readPointer();
        const curSize = vectorAddr.add(VECTOR.CUR_SIZE).readU32();
        return ptr(parseInt(dataPtr.toString()) + (curSize * elementSize));
    }

    getDataByType(type, id) {
        const config = this.dataTypeConfig[type];
        const datamanBase = this.getDatamanBase();
        const vectorAddr = datamanBase.add(config.offset);
        const begin = this.getVectorBegin(vectorAddr);
        const end = this.getVectorEnd(vectorAddr, config.size);
        let current = parseInt(begin.toString());
        const endAddr = parseInt(end.toString());
        while (current < endAddr) {
            const itemPtr = ptr(current);
            const itemId = itemPtr.add(config.offsets.ID).readU32();
            if (itemId === id) {
                return new BaseEssence(itemPtr, config.offsets);
            }
            current += config.size;
        }
        return null;
    }

    // 通用的名称获取方法
    getDataName(id) {
        // 从OFFSETS对象中获取所有类型
        const types = Object.values(OFFSETS).map(config => config.type);
        for (const type of types) {
            const data = this.getDataByType(type, id);
            if (data?.getName()) return data.getName();
        }
        return null;
    }


    // 通用的获取所有ID方法
    getAllIds(type) {
        const config = this.dataTypeConfig[type];
        if (!config) return [];

        const datamanBase = this.getDatamanBase();
        const vectorAddr = datamanBase.add(config.offset);
        const vectorContent = vectorAddr.readPointer();
        const curSize = vectorAddr.add(VECTOR.CUR_SIZE).readU32();

        const ids = [];
        for (let i = 0; i < curSize; i++) {
            const itemPtr = vectorContent.add(i * config.size);
            const itemId = itemPtr.add(config.offsets.ID).readU32();
            if (itemId > 0) {
                ids.push(itemId);
            }
        }
        return ids;
    }

    // 获取指定攻击规则的NPC列表
    getNpcsByAttackRule(ruleValue) {
        const config = this.dataTypeConfig['npc'];
        if (!config) return [];

        const datamanBase = this.getDatamanBase();
        const vectorAddr = datamanBase.add(config.offset);
        const vectorContent = vectorAddr.readPointer();
        const curSize = vectorAddr.add(VECTOR.CUR_SIZE).readU32();

        const ids = [];
        for (let i = 0; i < curSize; i++) {
            const itemPtr = vectorContent.add(i * config.size);
            const attackRule = itemPtr.add(config.offsets.ATTACK_RULE).readU8();
            if (attackRule === ruleValue) {
                const id = itemPtr.add(config.offsets.ID).readU32();
                ids.push(id);
            }
        }
        return ids;
    }

    getMonsterByHp(hp) {
        const config = this.dataTypeConfig['monster'];
        if (!config) return [];

        const datamanBase = this.getDatamanBase();
        const vectorAddr = datamanBase.add(config.offset);
        const vectorContent = vectorAddr.readPointer();
        const curSize = vectorAddr.add(VECTOR.CUR_SIZE).readU32();

        const ids = [];
        for (let i = 0; i < curSize; i++) {
            const itemPtr = vectorContent.add(i * config.size);
            const hp = itemPtr.add(config.offsets.HP).readU32();
            if (hp === hp) {
                const id = itemPtr.add(config.offsets.ID).readU32();
                ids.push(id);
            }
        }
        return ids;
    }

    getEquipIDByGender(genderValue) {
        const config = this.dataTypeConfig['equipment'];
        if (!config) return [];

        const datamanBase = this.getDatamanBase();
        const vectorAddr = datamanBase.add(config.offset);
        const vectorContent = vectorAddr.readPointer();
        const curSize = vectorAddr.add(VECTOR.CUR_SIZE).readU32();

        const ids = [];
        for (let i = 0; i < curSize; i++) {
            const itemPtr = vectorContent.add(i * config.size);
            const genderRule = itemPtr.add(config.offsets.GENDER).readU8();
            if (genderRule === genderValue) {
                const id = itemPtr.add(config.offsets.ID).readU32();
                ids.push(id);
            }
        }
        return ids;
    }
    
    getEquipByType(typeValue) {
        const config = this.dataTypeConfig['equipment'];
        if (!config) return [];

        const datamanBase = this.getDatamanBase();
        const vectorAddr = datamanBase.add(config.offset);
        const vectorContent = vectorAddr.readPointer();
        const curSize = vectorAddr.add(VECTOR.CUR_SIZE).readU32();

        const ids = [];
        for (let i = 0; i < curSize; i++) {
            const itemPtr = vectorContent.add(i * config.size);
            const typeRule = itemPtr.add(config.offsets.TYPE).readU8();
            if (typeRule === typeValue) {
                const id = itemPtr.add(config.offsets.ID).readU32();
                ids.push(id);
            }
        }
        return ids;
    }

    allmalefashionIds(){
        return this.getEquipIDByGender(0);
    }

    allfemalefashionIds(){
        return this.getEquipIDByGender(1);
    }

      // 获取所有怪物ID
    getAllMonsterIds() {
        return this.getAllIds('monster');
    }

    getAllWardobefashionIds() {
        return this.getAllIds('wardobe_fashion_config');
    }

    /**
     * 获取所有男性时装ID
     * @returns {number[]} 男性时装ID数组
     */
    getEquipMaleFashions() {
        try {
            const config = this.dataTypeConfig['equipment'];
            if (!config) return [];

            const datamanBase = this.getDatamanBase();
            const vectorAddr = datamanBase.add(config.offset);
            const vectorContent = vectorAddr.readPointer();
            const curSize = vectorAddr.add(VECTOR.CUR_SIZE).readU32();

            const maleFashions = [];
            for (let i = 0; i < curSize; i++) {
                const itemPtr = vectorContent.add(i * config.size);
                try {
                    // 同时检查类型(3=时装)和性别(0=男性)
                    const itemType = itemPtr.add(config.offsets.TYPE).readU8();
                    if (itemType === 3) {
                        const genderValue = itemPtr.add(config.offsets.GENDER).readU8();
                        if (genderValue === 0) {
                            const id = itemPtr.add(config.offsets.ID).readU32();
                            if (id > 0) {
                                maleFashions.push(id);
                            }
                        }
                    }
                } catch (e) {
                    // 忽略单个错误，继续处理下一个
                    continue;
                }
            }
            
            console.log(`[dataman] 已加载${maleFashions.length}个男性时装ID`);
            return maleFashions;
        } catch (e) {
            console.error(`[dataman] 获取男性时装失败: ${e.message}`);
            return [];
        }
    }

    /**
     * 获取所有女性时装ID
     * @returns {number[]} 女性时装ID数组
     */
    getEquipFemaleFashions() {
        try {
            const config = this.dataTypeConfig['equipment'];
            if (!config) return [];

            const datamanBase = this.getDatamanBase();
            const vectorAddr = datamanBase.add(config.offset);
            const vectorContent = vectorAddr.readPointer();
            const curSize = vectorAddr.add(VECTOR.CUR_SIZE).readU32();

            const femaleFashions = [];
            for (let i = 0; i < curSize; i++) {
                const itemPtr = vectorContent.add(i * config.size);
                try {
                    // 同时检查类型(3=时装)和性别(1=女性)
                    const itemType = itemPtr.add(config.offsets.TYPE).readU8();
                    if (itemType === 3) {
                        const genderValue = itemPtr.add(config.offsets.GENDER).readU8();
                        if (genderValue === 1) {
                            const id = itemPtr.add(config.offsets.ID).readU32();
                            if (id > 0) {
                                femaleFashions.push(id);
                            }
                        }
                    }
                } catch (e) {
                    // 忽略单个错误，继续处理下一个
                    continue;
                }
            }
            
            console.log(`[dataman] 已加载${femaleFashions.length}个女性时装ID`);
            return femaleFashions;
        } catch (e) {
            console.error(`[dataman] 获取女性时装失败: ${e.message}`);
            return [];
        }
    }
}

// 创建单例实例
const dataman = new DataMan();

// 导出单例实例
module.exports = dataman;
},{}],7:[function(require,module,exports){
(function (global){(function (){
// @ts-nocheck  // 忽略整个文件的类型检查

const CPlusClass = require("../base/CPlusClass");
const PlayerWrapper = require("./PlayerWrapper");
const SkillStubBase = require("./SkillStubBase");

const functionMap = new Map();

const getNativeFunction = (symbolOrAddress, ret, args) => {
  if (functionMap.has(symbolOrAddress)) {
    return functionMap.get(symbolOrAddress);
  }

  let address;
  if (symbolOrAddress.startsWith("0x")) {
    address = ptr(symbolOrAddress);
  } else {
    address = Module.getExportByName(null, symbolOrAddress);
  }

  const f = new NativeFunction(address, ret, args);
  functionMap.set(symbolOrAddress, f);
  return f;
};

class GPlayerImp extends CPlusClass {
  // 添加静态Map来存储玩家实例
  static allPlayerInstances = new Map();
  static currentPlayerInstance = null;

  static fromPlayerWrapper(playerWrapper) {
    const playerPtr = playerWrapper.getPointer();
    const gplayerImpPtr = playerPtr.add(0x4).readPointer();
    if (!gplayerImpPtr.isNull()) {
      return new GPlayerImp(gplayerImpPtr);
    }
    throw new Error("Could not get valid GPlayerImp pointer");
  }

  // 获取当前玩家实例的方法
  static getCurrentPlayer() {
    if (GPlayerImp.currentPlayerInstance) {
      return GPlayerImp.currentPlayerInstance;
    }

    try {
      // 简化方法：创建一个基本的实例用于测试
      // 我们不需要真正的玩家指针，只需要能够调用静态方法
      console.log('[获取玩家] 创建测试实例...');

      // 创建一个最小的实例，只用于调用静态方法
      const testInstance = Object.create(GPlayerImp.prototype);
      testInstance.pointer = ptr(0x1); // 最小的非空指针

      GPlayerImp.currentPlayerInstance = testInstance;
      return GPlayerImp.currentPlayerInstance;

    } catch (error) {
      console.log('[获取玩家] 获取当前玩家失败:', error);
      throw new Error("无法获取当前玩家实例");
    }
  }

  GetMoney() {
    return this.pointer.add(0x938).readInt();
  }

  GetPlayerLevel() {
    return this.pointer.add(0x290).readInt();
  }

  IsPlayerFemale() {
    return getNativeFunction("_ZN11gplayer_imp14IsPlayerFemaleEv", "int", ["pointer"])(this.pointer);
  }

  GetGender() {
    const isFemale = this.IsPlayerFemale();
    console.log(`[GetGender] 原始值: ${isFemale}, 位置: 0x4E`);
    return isFemale ? "女" : "男";
  }

  GetPlayerId() {
    return this.pointer.add(0x8).readPointer().add(0x40).readInt();
  }

  playerEquipItemListIndex() {
    return this.pointer.add(0x774);
  }

  IsCombatState() {
    return !!this.pointer.add(1670).readU8();
  }

  GetInventory() {
    return this.pointer.add(1880);
  }

  FilterMan() {
    return this.pointer.add(312);
  }

  //读取玩家身上所有的buff
  filterIds() {
    const mapPtr = this.FilterMan().add(288).toInt32();
    let begin = getNativeFunction('_ZN5abase15static_multimapIiP6filterNS_10fast_allocILi4ELi128EEEE5beginEv', 'pointer', ['int'])(mapPtr);
    let end = getNativeFunction('_ZN5abase15static_multimapIiP6filterNS_10fast_allocILi4ELi128EEEE3endEv', 'pointer', ['int'])(mapPtr);
    begin = parseInt(begin.toString());
    end = parseInt(end.toString());
    let filterArr = [];
    while (begin < end) {
      filterArr.push(ptr(begin).readInt());
      begin += 8;
    }
    return filterArr;
  }

  hasBuffId(buffId) {
    return this.filterIds().includes(buffId);
  }

  GetPlayerName() {
    const nameLength = this.pointer.add(4 * 993).readU32();
    const namePtr = this.pointer.add(3952);
    const name = namePtr.readUtf16String(nameLength / 2);
    return name || "未知玩家";
  }

  getUserID() {
    return this.pointer.add(1015).readInt();
  }

  GetTag() {
    return getNativeFunction("_ZN11gobject_imp12GetClientTagEv", "int", ["pointer"])(this.pointer);
  }

  GetCurTitle() {
    const getCurTitleFunc = new NativeFunction(ptr("0x0863B9BA"), "int16", [
      "pointer",
    ]);
    const titleId = getCurTitleFunc(this.pointer);
    if (titleId <= 0) {
      return "没有称号";
    }
    return titleId;
  }

  getMapId() {
    return getNativeFunction("_ZN16object_interface6GetTagEv", "int", [
      "pointer",
    ])(this.pointer);
  }

  ClrAllCoolDown() {
    return getNativeFunction("_ZN11gplayer_imp14ClrAllCoolDownEv", "int", ["pointer"])(
      this.pointer
    );
  }

  creatureGenerator(creatureId, duration, count) {
    this.ensurePointer();
    // 创建object_interface实例
    const oi = Memory.alloc(0x08);
    oi.add(0).writePointer(this.pointer); // 直接使用GPlayerImp的指针
    oi.add(4).writeU32(0);
    // 创建初始化minor_param结构
    const mp = Memory.alloc(0x48);
    // 设置参数
    mp.add(0).writeU32(creatureId);
    mp.add(0x08).writeU32(duration);
    mp.add(0x14).writeFloat(1.0);
    mp.add(0x18).writeFloat(1.0);
    mp.add(0x1c).writeFloat(1.0);

    // 调用CreateMinors函数
    const createMinorsFunc = new NativeFunction(
      ptr("0x08765D04"), // 使用与PlayerWrapper相同的地址
      "void",
      ["pointer", "pointer", "float"]
    );
    // 循环创建生物
    for (let i = 0; i < count; i++) {
      createMinorsFunc(oi, mp, 6.0); // 使用相同的浮点数参数
    }
  }

  summonNPC(npcId, duration, count) {
    this.ensurePointer();
    // 创建object_interface实例
    const oi = Memory.alloc(0x08);
    oi.add(0).writePointer(this.pointer);
    oi.add(4).writeU8(0);
    // 创建并初始化minor_param结构
    const mp = Memory.alloc(0x48);
    for (let i = 0; i < 0x48; i++) {
      mp.add(i).writeU8(0);
    }
    // 设置参数
    mp.add(0).writeInt(npcId);
    mp.add(0x08).writeInt(duration);
    // 调用CreateNPC函数
    const createNPCFunc = new NativeFunction(
      ptr("0x087643F0"), // object_interface::CreateNPC
      "void",
      ["pointer", "pointer", "float"]
    );
    // 循环创建指定数量的NPC
    for (let i = 0; i < count; i++) {
      createNPCFunc(oi, mp, 6.0);
    }
  }

  // 生成只有召唤者可以攻击的怪物
  myGenerator(id, count, lifetime, ownerType = 1) {
    id = parseInt(id);
    count = parseInt(count);
    lifetime = parseInt(lifetime);
    ownerType = parseInt(ownerType);
    const tag = this.GetTag();
    const posStruct = Memory.alloc(12);
    const parent = this.pointer.add(8).readPointer(); // _parent
    const playerPos = parent.add(0x30); // 玩家位置偏移
    const x = playerPos.readFloat();
    const y = playerPos.add(4).readFloat();
    const z = playerPos.add(8).readFloat();
    posStruct.writeFloat(x); // x
    posStruct.add(4).writeFloat(y); // y
    posStruct.add(8).writeFloat(z); // z 
    const objectInterface = Memory.alloc(8);
    objectInterface.writePointer(this.pointer);
    objectInterface.add(4).writeU32(0); // 初始化第二个字段为0   
    const createMonsterFunc = getNativeFunction("0x087640E6", "void", [// _ZN16object_interface13CreateMonsterEiiR9A3DVECTORiicf
      "pointer",  // object_interface* this
      "int32",    // monster ID
      "int32",    // map tag
      "pointer",  // A3DVECTOR* position
      "int32",    // lifetime 
      "int32",    // count
      "char",     // ownerType
      "float"     // radius (默认值6.0)
    ]);

    // 调用CreateMonster
    createMonsterFunc(
      objectInterface,  // this
      id,               // monster ID
      tag,              // map tag
      posStruct,        // position
      lifetime,         // lifetime
      count,            // count
      ownerType & 0xff, // ownerType (转换为char)
      6.0               // radius
    );

    return true;
  }

  SpendMoney(delta) {
    const currentMoney = this.GetMoney();
    if (currentMoney < delta) {
      return false;
    }
    // 直接写入新的金钱值
    const newMoney = currentMoney - delta;
    this.pointer.add(0x938).writeU32(newMoney);
    // 通知客户端金钱变化
    const runner = this.pointer.add(0x10).readPointer();
    const vtable = runner.readPointer();
    const spendMoneyFunc = new NativeFunction(
      vtable.add(0x1D4).readPointer(),
      "void",
      ["pointer", "uint32"]
    );
    spendMoneyFunc(runner, delta);
    return true;
  }

  CheckGMPrivilege() {
    return new NativeFunction(ptr('0x085D2ED8'), 'bool', ['pointer'])(this.pointer);
  }

  GetTargetType() {
    const oi = Memory.alloc(0x08);
    oi.add(0).writePointer(this.pointer);
    oi.add(4).writeU8(0);
    const xidPtr = new NativeFunction(ptr("0x087605BA"), "pointer", ["pointer"])(oi);
    return xidPtr.readU32();
  }

  GetTargetTemplateId() {
    const result = this.QueryObject(3, null, 0x48);
    if (!result.success) return null;
    const templateId = result.objectInfo.add(44).readInt();
    return templateId;
  }

  FullHPAndMP() {
    // 直接调用FullHPAndMP函数
    const fullHPAndMPFn = new NativeFunction(ptr('0x085FAA02'), 'void', ['pointer']);
    fullHPAndMPFn(this.pointer);
    console.log('[恢复] 已恢复满血满蓝');
    return true;
  }

  takeoutItem(inv_index, item_id, count) {
    const takeoutfuc = new NativeFunction(ptr('0x085CA6B2'), 'int', ['pointer', 'int', 'int', 'int']);
    return takeoutfuc(this.pointer, inv_index, item_id, count);
  }

  取出物品(inv_index, item_id, count) {
    const takeoutfuc = new NativeFunction(ptr('0x085CA6B2'), 'int', ['pointer', 'int', 'int', 'int']);
    return takeoutfuc(this.pointer, inv_index, item_id, count);
  }

  countitemById(itemId) {
    this.ensurePointer();
    // 获取玩家背包指针 (this + 0x758 是背包的偏移)
    const inventoryPtr = this.pointer.add(0x758);
    const countitembyidfuc = new NativeFunction(ptr('0x08634EF4'), 'int', ['pointer', 'int']);
    return countitembyidfuc(inventoryPtr, itemId);
  }

  统计物品(itemId) {
    this.ensurePointer();
    // 获取玩家背包指针 (this + 0x758 是背包的偏移)
    const inventoryPtr = this.pointer.add(0x758);
    const countitembyidfuc = new NativeFunction(ptr('0x08634EF4'), 'int', ['pointer', 'int']);
    return countitembyidfuc(inventoryPtr, itemId);
  }

  UseItemByID(where, itemId, num, useType = 0) {
    const useItemByIDFunc = new NativeFunction(ptr('0x085CADFE'), 'int32', ['pointer', 'pointer', 'int32', 'int32', 'uint32', 'int32']);
    // 使用相同的玩家指针作为pImp参数
    return useItemByIDFunc(this.pointer, this.pointer, where, itemId, num, useType
    );
  }

  getEquipIdByIndex(index) {
    const itemPtr = new NativeFunction(
      ptr('0x08634D28'),  // item_list::operator[]
      'pointer',
      ['pointer', 'uint32']
    )(this.playerEquipItemListIndex(), index);

    const itemId = itemPtr.readU32();
    // 检查无效ID
    if (itemId === 0 || itemId > 1000000) {
      return '空槽位';  // 返回空槽位值
    }
    return itemId;
  }

  getEquipList(size = 0x1E) {  // 默认大小30个槽位
    let equipList = [];
    const listPtr = this.playerEquipItemListIndex();

    // 获取装备列表大小
    const getSizeFunc = new NativeFunction(
      ptr('0x08633C6E'),  // item_list::Size
      'uint32',
      ['pointer']
    );
    const listSize = getSizeFunc(listPtr);

    // 使用传入的size和实际size中的较小值
    const actualSize = Math.min(size, listSize);

    console.log('========== 装备列表 ==========');
    // 获取每个装备的ID
    for (let i = 0; i < actualSize; i++) {
      const itemId = this.getEquipIdByIndex(i);
      equipList.push(itemId);
      console.log(`[${i}] ${itemId}`);
    }
    console.log('============================');
    return equipList;
  }

  // 查询对象信息 (可以查询玩家、NPC、地面物品)
  queryObject(xid, objectInfo = null) {
    this.ensurePointer();

    // 获取world指针 (通常在gplayer_imp的某个偏移位置)
    const worldPtr = this.pointer.add(0x4).readPointer(); // 假设world在偏移0x4

    // 创建XID结构 (8字节: type + id)
    const xidStruct = Memory.alloc(8);
    xidStruct.writeU32(xid.type || 3); // 默认查询物品
    xidStruct.add(4).writeU32(xid.id);

    // 创建object_info结构 (大小需要根据实际结构确定，这里假设64字节)
    const objInfo = objectInfo || Memory.alloc(64);

    // 调用world::QueryObject
    const queryObjectFunc = new NativeFunction(
      ptr('0x0859A7EE'),
      'int',
      ['pointer', 'pointer', 'pointer', 'bool']
    );

    const result = queryObjectFunc(worldPtr, xidStruct, objInfo, true);

    if (result) {
      // 解析object_info结构，返回有用信息
      return {
        success: true,
        position: {
          x: objInfo.readFloat(),
          y: objInfo.add(4).readFloat(),
          z: objInfo.add(8).readFloat()
        },
        // 可以根据需要添加更多字段
      };
    }

    return { success: false };
  }

  // 查询地面物品
  queryGroundItem(itemId) {
    return this.queryObject({ type: 3, id: itemId });
  }

  // 遍历附近的地面物品
  getNearbyGroundItems(radius = 50.0) {
    this.ensurePointer();
    const groundItems = [];

    try {
      // 获取world指针 (从gobject_imp._plane获取)
      const worldPtr = this.pointer.add(0x4).readPointer(); // _plane在偏移0x4
      if (!worldPtr || worldPtr.isNull()) {
        console.log('无法获取world指针');
        return groundItems;
      }

      // 获取玩家当前位置 (从gobject.pos获取，偏移0x30)
      const parent = this.pointer.add(8).readPointer(); // _parent在偏移0x8
      if (!parent || parent.isNull()) {
        console.log('无法获取parent指针');
        return groundItems;
      }

      const playerPos = parent.add(0x30); // gobject.pos在偏移0x30
      const playerX = playerPos.readFloat();
      const playerY = playerPos.add(4).readFloat();
      const playerZ = playerPos.add(8).readFloat();

      console.log(`玩家位置: (${playerX.toFixed(2)}, ${playerY.toFixed(2)}, ${playerZ.toFixed(2)})`);

      // 获取最大Matter数量
      const getMaxMatterCountFunc = new NativeFunction(
        ptr('0x0859FAA2'),
        'int',
        []
      );

      // 获取Matter by index的函数
      const getMatterByIndexFunc = new NativeFunction(
        ptr('0x085A073E'),
        'pointer',
        ['pointer', 'int']
      );

      let maxMatterCount;
      try {
        maxMatterCount = getMaxMatterCountFunc();
        console.log(`最大Matter数量: ${maxMatterCount}`);
      } catch (error) {
        console.log('无法获取最大Matter数量，使用默认值1000');
        maxMatterCount = 1000;
      }

      // 限制遍历数量，避免性能问题
      maxMatterCount = Math.min(maxMatterCount, 2000);

      for (let i = 0; i < maxMatterCount; i++) {
        try {
          const matterPtr = getMatterByIndexFunc(worldPtr, i);
          if (!matterPtr || matterPtr.isNull()) continue;

          // 检查Matter是否激活 (gobject.b_zombie应该为false)
          const bZombie = matterPtr.add(0xD).readU8(); // b_zombie在偏移0xD
          if (bZombie) continue; // 如果是zombie状态，跳过

          // 获取Matter位置 (gobject.pos在偏移0x30)
          const matterX = matterPtr.add(0x30).readFloat();
          const matterY = matterPtr.add(0x34).readFloat();
          const matterZ = matterPtr.add(0x38).readFloat();

          // 计算距离
          const dx = matterX - playerX;
          const dy = matterY - playerY;
          const dz = matterZ - playerZ;
          const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);

          if (distance <= radius) {
            // 获取Matter的XID (在偏移0x3C)
            const xidType = matterPtr.add(0x3C).readU32();
            const xidId = matterPtr.add(0x40).readU32();

            // 获取tag (在偏移0x24)
            const tag = matterPtr.add(0x24).readU32();

            groundItems.push({
              index: i,
              xid: { type: xidType, id: xidId },
              tag: tag,
              position: { x: matterX, y: matterY, z: matterZ },
              distance: distance
            });
          }
        } catch (error) {
          // 忽略单个Matter的错误，继续遍历
          continue;
        }
      }

      console.log(`找到${groundItems.length}个附近的地面物品`);
      return groundItems;

    } catch (error) {
      console.log('遍历地面物品时出错:', error);
      return groundItems;
    }
  }

  // 调试方法：检查各种指针和结构
  debugWorldStructure() {
    this.ensurePointer();

    try {
      console.log('=== 调试World结构 ===');

      // 检查gobject_imp结构
      console.log('GPlayerImp指针:', this.pointer);
      const planePtr = this.pointer.add(0x4).readPointer();
      console.log('_plane指针:', planePtr);
      const parentPtr = this.pointer.add(0x8).readPointer();
      console.log('_parent指针:', parentPtr);

      if (!planePtr || planePtr.isNull()) {
        console.log('_plane指针为空，尝试其他偏移');
        // 尝试其他可能的偏移
        for (let i = 0; i < 0x20; i += 4) {
          const testPtr = this.pointer.add(i).readPointer();
          if (testPtr && !testPtr.isNull()) {
            console.log(`偏移0x${i.toString(16)}: ${testPtr}`);
          }
        }
        return;
      }

      // 检查world结构
      console.log('=== World结构信息 ===');
      const worldPtr = planePtr;

      // 读取world的基本信息
      const wIndex = worldPtr.add(0x48).readU32();
      const wTag = worldPtr.add(0x4C).readU32();
      const playerCount = worldPtr.add(0xA94).readU32();

      console.log(`World - Index: ${wIndex}, Tag: ${wTag}, 玩家数量: ${playerCount}`);

      // 检查grid结构
      console.log('=== Grid结构信息 ===');
      const gridPtr = worldPtr; // grid在world开始位置

      const pTable = gridPtr.add(0x0).readPointer();
      const sliceCount = gridPtr.add(0x3C).readU32();
      const regRow = gridPtr.add(0x40).readU32();
      const regColumn = gridPtr.add(0x44).readU32();

      console.log(`Grid - pTable: ${pTable}, Slice数量: ${sliceCount}, 行: ${regRow}, 列: ${regColumn}`);

      if (pTable && !pTable.isNull()) {
        console.log('=== 检查前几个Slice ===');
        for (let i = 0; i < Math.min(5, sliceCount); i++) {
          const slicePtr = pTable.add(i * 0x28);
          const flag = slicePtr.add(0x4).readU32();
          const playerList = slicePtr.add(0x8).readPointer();
          const npcList = slicePtr.add(0xC).readPointer();
          const matterList = slicePtr.add(0x10).readPointer();

          console.log(`Slice[${i}] - Flag: ${flag}, Player: ${playerList}, NPC: ${npcList}, Matter: ${matterList}`);
        }
      }

    } catch (error) {
      console.log('调试时出错:', error);
    }
  }

  // 简单的ID2IDX转换（可能这个才是正确的TempID）
  static simpleId2Idx(id) {
    try {
      const id2idxFunc = new NativeFunction(ptr('0x0859A835'), 'int', ['int']);
      return id2idxFunc(id);
    } catch (error) {
      return -1;
    }
  }

  // 参考你的代码实现正确的QueryObject方法
  QueryObject(xidType, xidId, bufferSize = 0x38) {
    try {
      this.ensurePointer();
      const worldPtr = this.pointer.add(0x4).readPointer();
      if (!worldPtr || worldPtr.isNull()) {
        return { success: false };
      }

      // 创建XID结构
      const xidStruct = Memory.alloc(8);
      xidStruct.writeU32(xidType);
      xidStruct.add(4).writeU32(xidId);

      // 创建object_info结构
      const objectInfo = Memory.alloc(bufferSize);

      // 调用world::QueryObject
      const queryObjectFunc = new NativeFunction(
        ptr('0x0859A7EE'),
        'int',
        ['pointer', 'pointer', 'pointer', 'bool']
      );

      const result = queryObjectFunc(worldPtr, xidStruct, objectInfo, true);

      if (result) {
        return {
          success: true,
          objectInfo: objectInfo
        };
      }

      return { success: false };

    } catch (error) {
      return { success: false, error: error };
    }
  }

  // 获取目标的TemplateID（用于NPC/玩家）
  GetTargetTemplateId(xidType, xidId) {
    const result = this.QueryObject(xidType, xidId, 0x38);
    if (!result.success) return null;
    const templateId = result.objectInfo.add(44).readInt(); // 偏移44 = 0x2C
    return templateId;
  }

  // 获取地面物品的完整信息
  getGroundItemInfo(matterPtr) {
    try {
      // 从gmatter获取imp指针 (gobject.imp在偏移0x44)
      const impPtr = matterPtr.add(0x44).readPointer();
      if (!impPtr || impPtr.isNull()) {
        return null;
      }

      // 从gmatter_item_imp获取item_data指针 (存储在偏移0x38)
      const itemDataPtr = impPtr.add(0x38).readPointer();
      if (!itemDataPtr || itemDataPtr.isNull()) {
        return null;
      }

      // 读取完整的item_data结构
      const type = itemDataPtr.add(0x00).readU32();           // 物品ID
      const count = itemDataPtr.add(0x04).readU32();          // 数量
      const pileLimit = itemDataPtr.add(0x08).readU32();      // 堆叠上限
      const procType = itemDataPtr.add(0x0C).readU32();       // 处理类型
      const price = itemDataPtr.add(0x18).readU32();          // 价格
      const expireDate = itemDataPtr.add(0x1C).readU32();     // 过期时间

      return {
        itemId: type,
        count: count,
        pileLimit: pileLimit,
        procType: procType,
        price: price,
        expireDate: expireDate,
        itemDataPtr: itemDataPtr
      };

    } catch (error) {
      return null;
    }
  }

  // 兼容性方法
  getGroundItemId(matterPtr) {
    const info = this.getGroundItemInfo(matterPtr);
    return info ? { itemId: info.itemId, count: info.count, itemDataPtr: info.itemDataPtr } : null;
  }

  // 调试gmatter_item_imp结构
  static debugGmatterItemImp(impPtr) {
    try {
      console.log('=== 调试gmatter_item_imp结构 ===');
      console.log('gmatter_item_imp指针:', impPtr);

      // 搜索可能的item_data指针
      console.log('搜索item_data指针:');
      for (let offset = 0; offset < 0x80; offset += 4) {
        try {
          const ptr = impPtr.add(offset).readPointer();
          if (ptr && !ptr.isNull()) {
            // 检查这个指针是否指向有效的item_data
            try {
              const type = ptr.readU32();
              const count = ptr.add(4).readU32();

              // 简单验证：物品ID应该是合理的数字，数量也应该是合理的
              if (type > 0 && type < 100000 && count > 0 && count < 10000) {
                console.log(`  偏移0x${offset.toString(16)}: ${ptr} -> 可能的item_data (type=${type}, count=${count})`);
              }
            } catch (error) {
              // 忽略读取错误
            }
          }
        } catch (error) {
          // 忽略读取错误
        }
      }

      // 特别检查偏移0x38
      try {
        const itemDataPtr = impPtr.add(0x38).readPointer();
        console.log(`偏移0x38的指针: ${itemDataPtr}`);
        if (itemDataPtr && !itemDataPtr.isNull()) {
          console.log('偏移0x38指向的内容:');
          for (let i = 0; i < 0x28; i += 4) {
            const value = itemDataPtr.add(i).readU32();
            console.log(`  +0x${i.toString(16)}: ${value} (0x${value.toString(16)})`);
          }
        }
      } catch (error) {
        console.log('读取偏移0x38出错:', error);
      }

    } catch (error) {
      console.log('调试gmatter_item_imp出错:', error);
    }
  }

  // 分析地面物品的详细结构
  analyzeGroundItemStructure(matterPtr) {
    try {
      console.log('=== 分析地面物品结构 ===');
      console.log('Matter指针:', matterPtr);

      // gobject基础信息
      const tag = matterPtr.add(0x24).readU32();
      const posX = matterPtr.add(0x30).readFloat();
      const posY = matterPtr.add(0x34).readFloat();
      const posZ = matterPtr.add(0x38).readFloat();
      const xidType = matterPtr.add(0x3C).readU32();
      const xidId = matterPtr.add(0x40).readU32();
      const impPtr = matterPtr.add(0x44).readPointer();

      console.log(`gobject信息: Tag=${tag}, XID=${xidType}:${xidId}, 位置=(${posX.toFixed(2)}, ${posY.toFixed(2)}, ${posZ.toFixed(2)})`);
      console.log(`imp指针: ${impPtr}`);

      // gmatter特有信息
      const matterState = matterPtr.add(0x4E).readU8();
      const battleFlag = matterPtr.add(0x4F).readU8();
      const matterType = matterPtr.add(0x5C).readU32();
      const spawnIndex = matterPtr.add(0x60).readU32();
      const nameId = matterPtr.add(0x64).readU32();

      console.log(`gmatter信息: 类型=${matterType}, 状态=${matterState}, 名称ID=${nameId}, 生成索引=${spawnIndex}`);

      // 尝试分析imp结构
      if (impPtr && !impPtr.isNull()) {
        console.log('=== 分析imp结构 ===');

        // 读取imp的前几个字段
        for (let i = 0; i < 0x20; i += 4) {
          try {
            const value = impPtr.add(i).readU32();
            console.log(`  偏移0x${i.toString(16)}: ${value} (0x${value.toString(16)})`);
          } catch (error) {
            console.log(`  偏移0x${i.toString(16)}: 读取失败`);
          }
        }
      }

      return {
        tag, xidType, xidId, matterType, nameId, spawnIndex,
        position: { x: posX, y: posY, z: posZ }
      };

    } catch (error) {
      console.log('分析地面物品结构出错:', error);
      return null;
    }
  }

  // 分析item_data结构（基于正确的结构定义）
  static analyzeItemData(itemDataPtr) {
    try {
      console.log('=== 分析item_data结构 ===');
      console.log('item_data指针:', itemDataPtr);

      // 根据提供的item_data结构 (sizeof=0x28)
      const type = itemDataPtr.add(0x00).readU32();           // int type
      const count = itemDataPtr.add(0x04).readU32();          // size_t count
      const pileLimit = itemDataPtr.add(0x08).readU32();      // size_t pile_limit
      const procType = itemDataPtr.add(0x0C).readU32();       // int proc_type
      const guid1 = itemDataPtr.add(0x10).readU32();          // guid低位
      const guid2 = itemDataPtr.add(0x14).readU32();          // guid高位
      const price = itemDataPtr.add(0x18).readU32();          // size_t price
      const expireDate = itemDataPtr.add(0x1C).readU32();     // int expire_date
      const contentLength = itemDataPtr.add(0x20).readU32();  // size_t content_length
      const itemContentPtr = itemDataPtr.add(0x24).readPointer(); // char *item_content

      console.log(`  type (物品ID): ${type}`);
      console.log(`  count (数量): ${count}`);
      console.log(`  pile_limit (堆叠上限): ${pileLimit}`);
      console.log(`  proc_type (处理类型): ${procType}`);
      console.log(`  guid: ${guid1}:${guid2}`);
      console.log(`  price (价格): ${price}`);
      console.log(`  expire_date (过期时间): ${expireDate}`);
      console.log(`  content_length (内容长度): ${contentLength}`);
      console.log(`  item_content (内容指针): ${itemContentPtr}`);

      // 如果有内容，尝试读取一些
      if (itemContentPtr && !itemContentPtr.isNull() && contentLength > 0) {
        console.log('  物品内容 (前16字节):');
        for (let i = 0; i < Math.min(contentLength, 16); i += 4) {
          try {
            const value = itemContentPtr.add(i).readU32();
            console.log(`    +0x${i.toString(16)}: 0x${value.toString(16)}`);
          } catch (error) {
            break;
          }
        }
      }

      return {
        type, count, pileLimit, procType,
        guid: { low: guid1, high: guid2 },
        price, expireDate, contentLength, itemContentPtr
      };

    } catch (error) {
      console.log('分析item_data出错:', error);
      return null;
    }
  }

  // 获取所有地面物品的真实ID列表
  getAllGroundItemIds() {
    this.ensurePointer();
    const itemList = [];

    try {
      const worldPtr = this.pointer.add(0x4).readPointer();
      if (!worldPtr || worldPtr.isNull()) {
        return itemList;
      }

      const getMatterByIndexFunc = new NativeFunction(
        ptr('0x085A073E'),
        'pointer',
        ['pointer', 'int']
      );

      console.log('=== 扫描所有地面物品ID ===');

      for (let i = 0; i < 1000; i++) {
        try {
          const matterPtr = getMatterByIndexFunc(worldPtr, i);
          if (matterPtr && !matterPtr.isNull()) {
            const bZombie = matterPtr.add(0xD).readU8();

            if (!bZombie) {
              const itemInfo = this.getGroundItemInfo(matterPtr);
              if (itemInfo) {
                const xidType = matterPtr.add(0x3C).readU32();
                const xidId = matterPtr.add(0x40).readU32();
                const posX = matterPtr.add(0x30).readFloat();
                const posY = matterPtr.add(0x34).readFloat();
                const posZ = matterPtr.add(0x38).readFloat();

                itemList.push({
                  index: i,
                  xid: { type: xidType, id: xidId },
                  itemId: itemInfo.itemId,
                  count: itemInfo.count,
                  pileLimit: itemInfo.pileLimit,
                  price: itemInfo.price,
                  position: { x: posX, y: posY, z: posZ }
                });

                console.log(`  [${i}] 物品ID: ${itemInfo.itemId}, 数量: ${itemInfo.count}/${itemInfo.pileLimit}, 价格: ${itemInfo.price}, XID: ${xidType}:${xidId}`);
                console.log(`       位置: (${posX.toFixed(1)}, ${posY.toFixed(1)}, ${posZ.toFixed(1)})`);
              }
            }
          }
        } catch (error) {
          continue;
        }
      }

      console.log(`总共找到 ${itemList.length} 个地面物品`);
      return itemList;

    } catch (error) {
      console.log('扫描地面物品ID出错:', error);
      return itemList;
    }
  }

  // 通过Hook记录的信息来查找地面物品
  static groundItemsFromHook = [];

  // 物品收集系统
  static itemCollectionEnabled = false;
  static collectedItems = [];
  static collectionStartTime = 0;

  // 一键拾取系统
  static oneClickPickupEnabled = false;
  static pickupRadius = 30;
  static isProcessingBatchPickup = false; // 防止重复触发

  // 直接激活一键拾取Hook系统
  static activateOneClickPickup(radius = 30) {
    try {
      console.log('🎯 激活一键拾取系统...');

      // 先测试基本功能
      GPlayerImp.testBasicFunctions();

      GPlayerImp.oneClickPickupEnabled = true;
      GPlayerImp.pickupRadius = radius;

      // Hook拾取命令处理
      GPlayerImp.hookPickupCommandHandler();

      // Hook物品和金钱的OnPickup函数
      GPlayerImp.hookOnPickupFunctions();

      // Hook挂机拾取来获取地面物品数据
      GPlayerImp.hookGuajiPickup();

      // Hook物品创建来记录地面物品
      GPlayerImp.hookItemCreation();

      // 启动定期扫描 (暂时禁用，避免错误)
      // GPlayerImp.startPeriodicScan();

      console.log(`✅ 一键拾取系统已激活！范围: ${radius}米`);
      console.log('现在拾取任意物品时会自动拾取附近所有物品');

      return true;

    } catch (error) {
      console.log('❌ 激活一键拾取失败:', error);
      return false;
    }
  }

  // Hook拾取命令处理函数
  static hookPickupCommandHandler() {
    try {
      // Hook gplayer_controller::CommandHandler
      const commandHandlerAddr = ptr('0x086AD9F6'); // 正确的地址

      Interceptor.attach(commandHandlerAddr, {
        onEnter: function(args) {
          const thisPtr = args[0];    // gplayer_controller *this
          const cmd = args[1].toInt32(); // int cmd
          const bufPtr = args[2];     // void const* buf
          const size = args[3].toInt32(); // uint size

          try {
            // 检查是否是拾取命令 (C2S::PICKUP = 6)
            if (cmd === 6 && GPlayerImp.oneClickPickupEnabled) {
              console.log(`[拾取命令] 检测到拾取命令，开始批量拾取...`);

              // 读取拾取命令数据
              const pickupType = bufPtr.readU32();     // pm.type
              const pickupMid = bufPtr.add(4).readU32(); // pm.mid

              console.log(`[拾取命令] 目标物品: type=${pickupType}, mid=${pickupMid}`);

              // 异步执行批量拾取
              setTimeout(() => {
                GPlayerImp.performBatchPickupFromCommand(thisPtr, pickupType);
              }, 100);
            }
          } catch (error) {
            console.log(`[拾取命令] Hook错误: ${error}`);
          }
        }
      });

      console.log('✓ Hook 拾取命令处理 成功');

    } catch (error) {
      console.log('✗ Hook 拾取命令处理 失败:', error);
    }
  }

  // 从拾取命令触发的批量拾取
  static performBatchPickupFromCommand(controllerPtr, targetType) {
    try {
      // 防止重复触发
      if (GPlayerImp.isProcessingBatchPickup) {
        console.log('[批量拾取] 正在处理中，跳过');
        return;
      }

      GPlayerImp.isProcessingBatchPickup = true;

      console.log(`[批量拾取] 开始执行，目标类型: ${targetType}`);

      // 先扫描地面物品
      GPlayerImp.scanGroundItems();

      if (GPlayerImp.groundItemsFromHook.length === 0) {
        console.log(`[批量拾取] 没有发现地面物品`);
        GPlayerImp.isProcessingBatchPickup = false;
        return;
      }

      // 暂时跳过位置检查，直接拾取所有记录的物品
      console.log('[批量拾取] 跳过位置检查，直接拾取所有物品');

      console.log(`[批量拾取] 玩家位置: (${playerX.toFixed(1)}, ${playerY.toFixed(1)}, ${playerZ.toFixed(1)})`);

      // 筛选范围内的物品
      const nearbyItems = GPlayerImp.groundItemsFromHook.filter(item => {
        const dx = item.position.x - playerX;
        const dy = item.position.y - playerY;
        const dz = item.position.z - playerZ;
        const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
        return distance <= GPlayerImp.pickupRadius;
      });

      console.log(`[批量拾取] 范围内物品: ${nearbyItems.length}个`);

      if (nearbyItems.length === 0) {
        console.log(`[批量拾取] 范围内没有物品`);
        GPlayerImp.isProcessingBatchPickup = false;
        return;
      }

      // 发送拾取命令给每个物品
      let successCount = 0;

      nearbyItems.forEach((item, index) => {
        try {
          console.log(`[批量拾取] ${index + 1}/${nearbyItems.length}: ID=${item.itemId}, XID=${item.xid.type}:${item.xid.id}`);

          // 构造拾取命令数据
          const pickupCmd = Memory.alloc(8);
          pickupCmd.writeU32(item.itemId);    // type
          pickupCmd.add(4).writeU32(item.xid.id); // mid

          // 使用Pickup<0>函数直接拾取
          const pickupFunc = new NativeFunction(
            ptr('0x0868A5EA'), // gmatter_item_base_imp::Pickup<0>地址
            'void',
            ['double', 'pointer', 'pointer', 'int', 'int', 'int', 'pointer', 'int', 'char', 'int', 'char', 'int']
          );

          // 创建玩家XID结构
          const pickerXidStruct = Memory.alloc(8);
          pickerXidStruct.writeU32(1); // 玩家类型
          pickerXidStruct.add(4).writeU32(0); // 玩家ID (需要获取真实ID)

          // 创建位置向量
          const posStruct = Memory.alloc(12);
          posStruct.writeFloat(item.position.x);
          posStruct.add(4).writeFloat(item.position.y);
          posStruct.add(8).writeFloat(item.position.z);

          // 调用拾取函数
          pickupFunc(
            102.01001,           // double a1 (距离平方)
            item.gmatterImpPtr,  // gobject_imp *a2
            pickerXidStruct,     // XID *a3
            item.count,          // int a4
            item.count,          // int a5
            0,                   // int a6
            posStruct,           // A3DVECTOR *a7
            0,                   // int a8
            0,                   // char a9 (跳过CheckPickup)
            -1,                  // int a10
            1,                   // char a11
            0                    // int a12
          );

          const result = true; // 假设成功

          if (result) {
            successCount++;
            console.log(`  ✓ 成功`);
          } else {
            console.log(`  ✗ 失败`);
          }

          // 添加延迟避免过快
          Thread.sleep(0.1);

        } catch (error) {
          console.log(`  ✗ 错误: ${error}`);
        }
      });

      console.log(`[批量拾取] 完成: 成功${successCount}个`);

      // 重置处理标志
      setTimeout(() => {
        GPlayerImp.isProcessingBatchPickup = false;
      }, 1000);

    } catch (error) {
      console.log(`[批量拾取] 错误: ${error}`);
      GPlayerImp.isProcessingBatchPickup = false;
    }
  }

  // Hook 拾取命令处理函数
  static hookPickupCommand() {
    try {
      // Hook gplayer_controller::CommandHandler 中的 PICKUP case
      // 我们需要找到这个函数的地址，从你提供的反汇编代码来看
      // 这应该是在 gplayer_controller::CommandHandler 中

      // 先尝试Hook QueryObject来发现地面物品
      GPlayerImp.hookQueryObject();

      // 然后Hook拾取消息发送
      GPlayerImp.hookPickupMessage();

      console.log('✓ Hook 拾取命令 成功');

    } catch (error) {
      console.log('✗ Hook 拾取命令 失败:', error);
    }
  }

  // Hook QueryObject函数来发现地面物品
  static hookQueryObject() {
    try {
      // world::QueryObject 函数地址
      const queryObjectAddr = ptr('0x0859A7EE'); // 正确的地址

      Interceptor.attach(queryObjectAddr, {
        onEnter: function(args) {
          const thisPtr = args[0];    // world *this
          const xidPtr = args[1];     // XID const& obj
          const infoPtr = args[2];    // world::object_info & info

          try {
            const xidType = xidPtr.readU32();
            const xidId = xidPtr.add(4).readU32();

            // 只关注物品类型 (GM_TYPE_MATTER = 3)
            if (xidType === 3) {
              console.log(`[QueryObject] 查询物品: XID=${xidType}:${xidId}`);
            }
          } catch (error) {
            // 忽略错误
          }
        },
        onLeave: function(retval) {
          // 如果查询成功，记录物品信息
          if (retval.toInt32() !== 0) {
            // 查询成功，物品存在
          }
        }
      });

      console.log('✓ Hook QueryObject 成功');

    } catch (error) {
      console.log('✗ Hook QueryObject 失败:', error);
    }
  }

  // Hook OnPickup函数来拦截拾取操作
  static hookOnPickupFunctions() {
    try {
      // Hook gmatter_item_imp::OnPickup (普通物品)
      const itemOnPickupAddr = ptr('0x08688D32');
      Interceptor.attach(itemOnPickupAddr, {
        onEnter: function(args) {
          const thisPtr = args[0];    // gmatter_item_imp *this
          const xidPtr = args[1];     // const XID *a2 (拾取者)
          const count = args[2].toInt32(); // int count
          const param4 = args[3];     // bool a4
          const param5 = args[4].toInt32(); // int a5
          const param6 = args[5];     // bool a6

          try {
            // 检查是否启用一键拾取
            if (!GPlayerImp.oneClickPickupEnabled) return;

            // 防止重复触发
            if (GPlayerImp.isProcessingBatchPickup) return;

            // 读取拾取者XID
            const pickerType = xidPtr.readU32();
            const pickerId = xidPtr.add(4).readU32();

            // 只处理玩家拾取 (type = 1)
            if (pickerType !== 1) return;

            console.log(`[物品拾取] 检测到玩家拾取物品: ${pickerType}:${pickerId}, 数量=${count}`);

            // 异步执行批量拾取
            setTimeout(() => {
              GPlayerImp.performBatchPickupFromOnPickup(xidPtr, 'item');
            }, 100);

          } catch (error) {
            console.log(`[物品拾取] Hook错误: ${error}`);
          }
        }
      });

      // Hook gmatter_money_imp::OnPickup (金钱物品)
      const moneyOnPickupAddr = ptr('0x0868B790');
      Interceptor.attach(moneyOnPickupAddr, {
        onEnter: function(args) {
          const thisPtr = args[0];    // gmatter_money_imp *this
          const xidPtr = args[1];     // const XID *a2 (拾取者)
          const amount = args[2].toInt32(); // int amount
          const param4 = args[3];     // bool a4
          const param5 = args[4].toInt32(); // int a5
          const param6 = args[5];     // bool a6

          try {
            // 检查是否启用一键拾取
            if (!GPlayerImp.oneClickPickupEnabled) return;

            // 防止重复触发
            if (GPlayerImp.isProcessingBatchPickup) return;

            // 读取拾取者XID
            const pickerType = xidPtr.readU32();
            const pickerId = xidPtr.add(4).readU32();

            // 只处理玩家拾取 (type = 1)
            if (pickerType !== 1) return;

            console.log(`[金钱拾取] 检测到玩家拾取金钱: ${pickerType}:${pickerId}, 金额=${amount}`);

            // 异步执行批量拾取
            setTimeout(() => {
              GPlayerImp.performBatchPickupFromOnPickup(xidPtr, 'money');
            }, 100);

          } catch (error) {
            console.log(`[金钱拾取] Hook错误: ${error}`);
          }
        }
      });

      console.log('✓ Hook OnPickup函数 成功');

    } catch (error) {
      console.log('✗ Hook OnPickup函数 失败:', error);
    }
  }

  // 从OnPickup触发的批量拾取
  static performBatchPickupFromOnPickup(playerXidPtr, triggerType) {
    try {
      // 防止重复触发
      if (GPlayerImp.isProcessingBatchPickup) {
        console.log('[批量拾取] 正在处理中，跳过');
        return;
      }

      GPlayerImp.isProcessingBatchPickup = true;

      console.log(`[批量拾取] 由${triggerType}拾取触发，开始执行...`);

      // 先扫描地面物品
      GPlayerImp.scanGroundItems();

      if (GPlayerImp.groundItemsFromHook.length === 0) {
        console.log(`[批量拾取] 没有发现地面物品`);
        GPlayerImp.isProcessingBatchPickup = false;
        return;
      }

      // 获取玩家位置
      const gplayerInstance = GPlayerImp.getCurrentPlayer();
      const parent = gplayerInstance.pointer.add(8).readPointer();
      const playerPos = parent.add(0x30);
      const playerX = playerPos.readFloat();
      const playerY = playerPos.add(4).readFloat();
      const playerZ = playerPos.add(8).readFloat();

      console.log(`[批量拾取] 玩家位置: (${playerX.toFixed(1)}, ${playerY.toFixed(1)}, ${playerZ.toFixed(1)})`);

      // 筛选范围内的物品
      const nearbyItems = GPlayerImp.groundItemsFromHook.filter(item => {
        const dx = item.position.x - playerX;
        const dy = item.position.y - playerY;
        const dz = item.position.z - playerZ;
        const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
        return distance <= GPlayerImp.pickupRadius;
      });

      console.log(`[批量拾取] 范围内物品: ${nearbyItems.length}个`);

      if (nearbyItems.length === 0) {
        console.log(`[批量拾取] 范围内没有物品`);
        GPlayerImp.isProcessingBatchPickup = false;
        return;
      }

      // 基于源码的正确拾取方法
      // 我们需要调用 gmatter_item_base_imp::Pickup<0> 模板函数
      const pickupTemplateFunc = new NativeFunction(
        ptr('0x0868A5EA'), // gmatter_item_base_imp::Pickup<0>地址
        'void',
        ['pointer', 'pointer', 'int', 'int', 'int', 'pointer', 'pointer', 'bool']
      );

      let successCount = 0;
      let failCount = 0;

      // 批量拾取所有物品 - 基于源码的正确实现
      nearbyItems.forEach((item, index) => {
        try {
          console.log(`[批量拾取] ${index + 1}/${nearbyItems.length}: ID=${item.itemId}, 数量=${item.count}`);

          if (item.gmatterImpPtr && !item.gmatterImpPtr.isNull()) {
            // 创建玩家XID结构
            const pickerXidStruct = Memory.alloc(8);
            pickerXidStruct.writeU32(playerXid.type);
            pickerXidStruct.add(4).writeU32(playerXid.id);

            // 创建位置向量 (玩家位置，用于距离检查)
            const playerPosStruct = Memory.alloc(12);
            playerPosStruct.writeFloat(item.position.x); // 使用物品位置避免距离检查失败
            playerPosStruct.add(4).writeFloat(item.position.y);
            playerPosStruct.add(8).writeFloat(item.position.z);

            // 创建bb XID (可能是背包相关)
            const bbXidStruct = Memory.alloc(8);
            bbXidStruct.writeU32(0); // 暂时设为0
            bbXidStruct.add(4).writeU32(0);

            // 调用 Pickup<0> 模板函数
            // void Pickup(const XID & who, int random_pickup, int team_id, int team_seq, const A3DVECTOR &pos, const XID & bb, bool is_check)
            pickupTemplateFunc(
              item.gmatterImpPtr,  // this (gmatter_item_base_imp*)
              pickerXidStruct,     // const XID & who (拾取者)
              0,                   // int random_pickup (非随机拾取)
              0,                   // int team_id (队伍ID，0表示无队伍)
              0,                   // int team_seq (队伍序号)
              playerPosStruct,     // const A3DVECTOR &pos (玩家位置)
              bbXidStruct,         // const XID & bb (背包相关)
              false                // bool is_check (跳过CheckPickup验证)
            );

            successCount++;
            console.log(`  ✓ 调用成功`);
          } else {
            failCount++;
            console.log(`  ✗ 无效的gmatterImpPtr`);
          }

          // 添加小延迟避免过快
          Thread.sleep(0.05);

        } catch (error) {
          failCount++;
          console.log(`  ✗ 错误: ${error}`);
        }
      });

      console.log(`[批量拾取] 完成: 成功${successCount}个, 失败${failCount}个`);

      // 清理已处理的物品
      GPlayerImp.groundItemsFromHook = GPlayerImp.groundItemsFromHook.filter(item => {
        return !nearbyItems.some(nearby => nearby.gmatterImpPtr && nearby.gmatterImpPtr.equals(item.gmatterImpPtr));
      });

      // 重置处理标志
      setTimeout(() => {
        GPlayerImp.isProcessingBatchPickup = false;
      }, 1000);

    } catch (error) {
      console.log(`[批量拾取] 错误: ${error}`);
      GPlayerImp.isProcessingBatchPickup = false;
    }
  }

  // Hook挂机宠物拾取函数来获取地面物品数据
  static hookGuajiPickup() {
    try {
      // Hook child_manager::player_pickup_guaji_child
      const guajiPickupAddr = ptr('0x088651A0'); // 从函数列表中的地址

      Interceptor.attach(guajiPickupAddr, {
        onEnter: function(args) {
          const thisPtr = args[0];      // child_manager *this
          const gplayerPtr = args[1];   // gplayer_imp *a2
          const srcPtr = args[2];       // void *src (物品数据)
          const size = args[3].toInt32(); // size_t n

          try {
            console.log(`[挂机拾取] 收到数据: 大小=${size}字节`);

            if (size > 0 && srcPtr && !srcPtr.isNull()) {
              // 尝试解析物品数据
              console.log(`[挂机拾取] 数据内容:`);

              // 显示前64字节的十六进制数据
              const hexData = srcPtr.readByteArray(Math.min(size, 64));
              console.log(`[挂机拾取] 十六进制: ${hexData}`);

              // 尝试按item_data结构解析
              GPlayerImp.parseGuajiItemData(srcPtr, size);
            }
          } catch (error) {
            console.log(`[挂机拾取] Hook错误: ${error}`);
          }
        }
      });

      console.log('✓ Hook 挂机拾取 成功');

    } catch (error) {
      console.log('✗ Hook 挂机拾取 失败:', error);
    }
  }

  // 解析挂机物品数据
  static parseGuajiItemData(dataPtr, size) {
    try {
      console.log(`[解析挂机数据] 开始解析 ${size} 字节数据`);

      let offset = 0;
      let itemCount = 0;

      // 尝试不同的解析方式

      // 方式1: 假设开头是物品数量
      if (size >= 4) {
        const possibleCount = dataPtr.add(offset).readU32();
        console.log(`[解析] 可能的物品数量: ${possibleCount}`);

        if (possibleCount > 0 && possibleCount < 1000) {
          offset += 4;

          // 尝试解析每个物品
          for (let i = 0; i < Math.min(possibleCount, 10) && offset + 32 <= size; i++) {
            try {
              const itemId = dataPtr.add(offset).readU32();
              const count = dataPtr.add(offset + 4).readU32();
              const pileLimit = dataPtr.add(offset + 8).readU32();
              const procType = dataPtr.add(offset + 12).readU32();

              if (itemId > 0 && itemId < 100000 && count > 0 && count < 10000) {
                console.log(`[解析] 物品${i + 1}: ID=${itemId}, 数量=${count}, 堆叠=${pileLimit}`);

                // 记录到地面物品列表
                const itemInfo = {
                  gmatterPtr: null,
                  gmatterImpPtr: null,
                  xid: { type: 3, id: 0 }, // 暂时没有XID信息
                  itemId: itemId,
                  count: count,
                  position: { x: 0, y: 0, z: 0 }, // 暂时没有位置信息
                  timestamp: Date.now(),
                  source: 'guaji'
                };

                GPlayerImp.groundItemsFromHook.push(itemInfo);
                itemCount++;
              }

              offset += 32; // 假设每个item_data是32字节

            } catch (error) {
              console.log(`[解析] 物品${i + 1}解析失败: ${error}`);
              break;
            }
          }
        }
      }

      console.log(`[解析挂机数据] 完成，解析出 ${itemCount} 个物品`);

    } catch (error) {
      console.log(`[解析挂机数据] 错误: ${error}`);
    }
  }

  // Hook物品创建
  static hookItemCreation() {
    try {
      // Hook SetData来记录地面物品
      const setDataAddr = ptr('0x08688AFC');
      Interceptor.attach(setDataAddr, {
        onEnter: function(args) {
          const thisPtr = args[0];      // gmatter_item_imp *this
          const itemDataPtr = args[1];  // item_data const&

          try {
            const type = itemDataPtr.add(0x00).readU32();
            const count = itemDataPtr.add(0x04).readU32();

            const gmatterPtr = thisPtr.add(0x8).readPointer(); // _parent
            if (gmatterPtr && !gmatterPtr.isNull()) {
              const posX = gmatterPtr.add(0x30).readFloat();
              const posY = gmatterPtr.add(0x34).readFloat();
              const posZ = gmatterPtr.add(0x38).readFloat();
              const xidType = gmatterPtr.add(0x3C).readU32();
              const xidId = gmatterPtr.add(0x40).readU32();

              const itemInfo = {
                gmatterPtr: gmatterPtr,
                gmatterImpPtr: thisPtr,
                xid: { type: xidType, id: xidId },
                itemId: type,
                count: count,
                position: { x: posX, y: posY, z: posZ },
                timestamp: Date.now()
              };

              GPlayerImp.groundItemsFromHook.push(itemInfo);
              console.log(`[物品创建] ID=${type}, 数量=${count}, XID=${xidType}:${xidId}`);
            }
          } catch (error) {
            // 忽略错误
          }
        }
      });

      console.log('✓ Hook 物品创建 成功');

    } catch (error) {
      console.log('✗ Hook 物品创建 失败:', error);
    }
  }

  // 从CheckPickup触发的批量拾取
  static performBatchPickupFromCheck(playerXidPtr) {
    try {
      // 防止重复触发
      if (GPlayerImp.isProcessingBatchPickup) {
        console.log('[批量拾取] 正在处理中，跳过');
        return;
      }

      GPlayerImp.isProcessingBatchPickup = true;

      console.log(`[批量拾取] 开始执行，当前地面物品: ${GPlayerImp.groundItemsFromHook.length}个`);

      if (GPlayerImp.groundItemsFromHook.length === 0) {
        console.log(`[批量拾取] 没有地面物品，先扫描...`);
        GPlayerImp.scanGroundItems();

        if (GPlayerImp.groundItemsFromHook.length === 0) {
          console.log(`[批量拾取] 扫描后仍无物品`);
          GPlayerImp.isProcessingBatchPickup = false;
          return;
        }
      }

      // 获取玩家位置
      let gplayerInstance = null;
      let parent = null;
      let playerX = 0, playerY = 0, playerZ = 0;

      try {
        gplayerInstance = GPlayerImp.getCurrentPlayer();
        parent = gplayerInstance.pointer.add(8).readPointer();

        if (!parent || parent.isNull()) {
          console.log('[批量拾取] 无法获取玩家parent指针');
          GPlayerImp.isProcessingBatchPickup = false;
          return;
        }

        const playerPos = parent.add(0x30);
        playerX = playerPos.readFloat();
        playerY = playerPos.add(4).readFloat();
        playerZ = playerPos.add(8).readFloat();

      } catch (error) {
        console.log('[批量拾取] 获取玩家信息失败:', error);
        GPlayerImp.isProcessingBatchPickup = false;
        return;
      }

      console.log(`[批量拾取] 玩家位置: (${playerX.toFixed(1)}, ${playerY.toFixed(1)}, ${playerZ.toFixed(1)})`);

      // 筛选范围内的物品
      const nearbyItems = GPlayerImp.groundItemsFromHook.filter(item => {
        const dx = item.position.x - playerX;
        const dy = item.position.y - playerY;
        const dz = item.position.z - playerZ;
        const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
        return distance <= GPlayerImp.pickupRadius;
      });

      console.log(`[批量拾取] 范围内物品: ${nearbyItems.length}个`);

      if (nearbyItems.length === 0) {
        console.log(`[批量拾取] 范围内没有物品`);
        GPlayerImp.isProcessingBatchPickup = false;
        return;
      }

      // 准备OnPickup函数
      const onPickupFunc = new NativeFunction(
        ptr('0x08688D32'),
        'int',
        ['pointer', 'pointer', 'int', 'bool', 'int', 'bool']
      );

      let successCount = 0;
      let failCount = 0;

      // 批量拾取所有物品
      nearbyItems.forEach((item, index) => {
        try {
          console.log(`[批量拾取] ${index + 1}/${nearbyItems.length}: ID=${item.itemId}, 数量=${item.count}`);

          // 调用拾取函数
          const result = onPickupFunc(
            item.gmatterImpPtr,  // this
            playerXidPtr,        // 玩家XID
            item.count,          // 数量
            false,               // 参数3
            0,                   // 参数4
            false                // 参数5
          );

          if (result) {
            successCount++;
            console.log(`  ✓ 成功`);
          } else {
            failCount++;
            console.log(`  ✗ 失败`);
          }

          // 添加小延迟避免过快
          Thread.sleep(0.05);

        } catch (error) {
          failCount++;
          console.log(`  ✗ 错误: ${error}`);
        }
      });

      console.log(`[批量拾取] 完成: 成功${successCount}个, 失败${failCount}个`);

      // 清理已处理的物品
      GPlayerImp.groundItemsFromHook = GPlayerImp.groundItemsFromHook.filter(item => {
        return !nearbyItems.some(nearby => nearby.gmatterImpPtr.equals(item.gmatterImpPtr));
      });

      // 重置处理标志
      setTimeout(() => {
        GPlayerImp.isProcessingBatchPickup = false;
      }, 1000);

    } catch (error) {
      console.log(`[批量拾取] 错误: ${error}`);
      GPlayerImp.isProcessingBatchPickup = false;
    }
  }

  // 主动扫描地面物品
  static startPeriodicScan() {
    try {
      // 每2秒扫描一次地面物品
      setInterval(() => {
        GPlayerImp.scanGroundItems();
      }, 2000);
      console.log('✓ 启动定期扫描');
    } catch (error) {
      console.log('✗ 启动定期扫描失败:', error);
    }
  }

  // 使用QueryObject扫描地面物品
  static scanViaQueryObject() {
    try {
      console.log('[扫描] 方法: 使用QueryObject扫描');

      const gplayerInstance = GPlayerImp.getCurrentPlayer();

      // 安全地读取plane指针
      let planePtr = null;
      try {
        planePtr = gplayerInstance.pointer.add(0x4).readPointer(); // _plane
      } catch (error) {
        console.log('[扫描] 读取plane指针失败:', error);
        return;
      }

      if (!planePtr || planePtr.isNull()) {
        console.log('[扫描] plane指针为空');
        return;
      }

      console.log(`[扫描] Plane指针: ${planePtr}`);

      // 尝试不同的XID范围来查找物品
      let foundCount = 0;

      for (let id = 1; id < 10000; id++) {
        try {
          // 创建XID结构 (GM_TYPE_MATTER = 3)
          const xidStruct = Memory.alloc(8);
          xidStruct.writeU32(3);      // type = GM_TYPE_MATTER
          xidStruct.add(4).writeU32(id); // id

          // 创建object_info结构
          const objectInfo = Memory.alloc(0x40); // 假设object_info大小

          // 调用QueryObject - 使用正确的地址
          const queryObjectFunc = new NativeFunction(
            ptr('0x0859A7EE'), // world::QueryObject地址
            'bool',
            ['pointer', 'pointer', 'pointer', 'bool']
          );

          const result = queryObjectFunc(planePtr, xidStruct, objectInfo, false);

          if (result) {
            // 查询成功，读取物品信息
            const tid = objectInfo.add(0x2C).readU32(); // tid字段
            const itemType = tid & 0xFFFF;

            const posX = objectInfo.add(0x8).readFloat();  // pos.x
            const posY = objectInfo.add(0xC).readFloat();  // pos.y
            const posZ = objectInfo.add(0x10).readFloat(); // pos.z

            console.log(`[扫描发现] XID=3:${id}, 类型=${itemType}, 位置=(${posX.toFixed(1)}, ${posY.toFixed(1)}, ${posZ.toFixed(1)})`);

            // 记录到地面物品列表
            const itemInfo = {
              gmatterPtr: null, // 暂时没有
              gmatterImpPtr: null,
              xid: { type: 3, id: id },
              itemId: itemType,
              count: 1, // 默认数量
              position: { x: posX, y: posY, z: posZ },
              timestamp: Date.now()
            };

            GPlayerImp.groundItemsFromHook.push(itemInfo);
            foundCount++;

            if (foundCount >= 20) break; // 限制数量避免过多
          }

        } catch (error) {
          continue; // 忽略错误，继续下一个
        }
      }

      console.log(`[扫描] QueryObject方法发现 ${foundCount} 个物品`);

    } catch (error) {
      console.log('[扫描] QueryObject方法失败:', error);
    }
  }

  // 扫描地面物品 - 简化版本
  static scanGroundItems() {
    console.log('[扫描] 开始扫描地面物品...');

    // 暂时跳过扫描，避免错误
    console.log('[扫描] 暂时跳过扫描功能，避免指针错误');
    console.log('[扫描] 当前地面物品记录:', GPlayerImp.groundItemsFromHook.length, '个');

    // 如果没有记录的物品，创建一些测试数据
    if (GPlayerImp.groundItemsFromHook.length === 0) {
      console.log('[扫描] 没有Hook记录的物品，可能需要:');
      console.log('   1. 确保Hook系统正常工作');
      console.log('   2. 丢一些物品到地上');
      console.log('   3. 让挂机宠物拾取物品');
    }
  }

  // 方法1: GetMatterByIndex扫描
  static scanViaGetMatterByIndex() {
    try {
      console.log('[扫描] 方法1: GetMatterByIndex');

      const gplayerInstance = GPlayerImp.getCurrentPlayer();

      // 安全地读取world指针
      let worldPtr = null;
      try {
        worldPtr = gplayerInstance.pointer.add(0x4).readPointer();
      } catch (error) {
        console.log('[扫描] 读取world指针失败:', error);
        return;
      }

      if (!worldPtr || worldPtr.isNull()) {
        console.log('[扫描] World指针为空');
        return;
      }

      console.log(`[扫描] World指针: ${worldPtr}`);

      // 尝试不同的可能地址
      const possibleAddresses = [
        '0x085A073E', // 原地址
        '0x085A0740', // 可能的变体
        '0x085A0730', // 可能的变体
      ];

      for (const addr of possibleAddresses) {
        try {
          console.log(`[扫描] 尝试地址: ${addr}`);

          const getMatterByIndexFunc = new NativeFunction(
            ptr(addr), 'pointer', ['pointer', 'int']
          );

          let foundCount = 0;

          for (let i = 0; i < 100; i++) {
            try {
              const matterPtr = getMatterByIndexFunc(worldPtr, i);
              if (matterPtr && !matterPtr.isNull()) {
                console.log(`[扫描] 索引${i}: Matter指针 = ${matterPtr}`);

                const bZombie = matterPtr.add(0xD).readU8();
                const xidType = matterPtr.add(0x3C).readU32();
                const xidId = matterPtr.add(0x40).readU32();

                console.log(`[扫描] 索引${i}: zombie=${bZombie}, XID=${xidType}:${xidId}`);

                if (!bZombie && xidType === 3) {
                  // 检查是否已经记录过
                  const exists = GPlayerImp.groundItemsFromHook.some(item =>
                    item.xid.type === xidType && item.xid.id === xidId
                  );

                  if (!exists) {
                    // 尝试获取物品信息
                    const itemInfo = gplayerInstance.getGroundItemInfo(matterPtr);
                    if (itemInfo) {
                      const posX = matterPtr.add(0x30).readFloat();
                      const posY = matterPtr.add(0x34).readFloat();
                      const posZ = matterPtr.add(0x38).readFloat();

                      const newItem = {
                        gmatterPtr: matterPtr,
                        gmatterImpPtr: matterPtr.add(0x44).readPointer(),
                        xid: { type: xidType, id: xidId },
                        itemId: itemInfo.itemId,
                        count: itemInfo.count,
                        position: { x: posX, y: posY, z: posZ },
                        timestamp: Date.now()
                      };

                      GPlayerImp.groundItemsFromHook.push(newItem);
                      foundCount++;
                      console.log(`[扫描发现] 物品ID=${itemInfo.itemId}, 数量=${itemInfo.count}, XID=${xidType}:${xidId}`);
                    }
                  }
                }
              }
            } catch (error) {
              if (i < 5) { // 只显示前几个错误
                console.log(`[扫描] 索引${i}错误: ${error}`);
              }
              continue;
            }
          }

          console.log(`[扫描] 地址${addr}: 发现${foundCount}个物品`);
          if (foundCount > 0) break; // 如果找到物品就停止尝试其他地址

        } catch (error) {
          console.log(`[扫描] 地址${addr}失败: ${error}`);
        }
      }

    } catch (error) {
      console.log('[扫描] GetMatterByIndex方法失败:', error);
    }
  }

  // 方法2: 尝试其他扫描方法
  static scanViaAlternativeMethods() {
    try {
      console.log('[扫描] 方法2: 尝试其他扫描函数');

      let gplayerInstance = null;
      try {
        gplayerInstance = GPlayerImp.getCurrentPlayer();
      } catch (error) {
        console.log('[扫描] 创建GPlayerImp实例失败:', error);
        return;
      }

      // 尝试通过world_manager相关函数
      // 这些函数可能有内部的物品枚举逻辑

      // 方法2a: 尝试直接读取world的内部结构
      GPlayerImp.scanWorldStructure(gplayerInstance);

    } catch (error) {
      console.log('[扫描] 其他方法失败:', error);
    }
  }

  // 扫描World内部结构
  static scanWorldStructure(gplayerInstance) {
    try {
      console.log('[扫描] 方法2a: 扫描World结构');

      let worldPtr = null;
      try {
        worldPtr = gplayerInstance.pointer.add(0x4).readPointer();
      } catch (error) {
        console.log('[扫描] 读取world指针失败:', error);
        return;
      }

      if (!worldPtr || worldPtr.isNull()) {
        console.log('[扫描] World指针为空');
        return;
      }

      console.log(`[扫描] World指针: ${worldPtr}`);

      // 尝试读取world的前几个字段，寻找可能的对象列表
      for (let offset = 0; offset < 0x100; offset += 4) {
        try {
          const value = worldPtr.add(offset).readPointer();
          if (value && !value.isNull()) {
            // 检查这个指针是否指向有效的内存区域
            try {
              const testValue = value.readU32();
              console.log(`[扫描] World+0x${offset.toString(16)}: ${value} (首4字节: 0x${testValue.toString(16)})`);
            } catch (error) {
              // 忽略无效指针
            }
          }
        } catch (error) {
          continue;
        }
      }

    } catch (error) {
      console.log('[扫描] World结构扫描失败:', error);
    }
  }

  // 执行批量拾取
  static performBatchPickup(playerXidPtr) {
    try {
      console.log(`[批量拾取] 开始执行，当前地面物品: ${GPlayerImp.groundItemsFromHook.length}个`);

      if (GPlayerImp.groundItemsFromHook.length === 0) {
        console.log(`[批量拾取] 没有地面物品`);
        return;
      }

      // 获取玩家位置
      const gplayerInstance = new GPlayerImp();
      const parent = gplayerInstance.pointer.add(8).readPointer();
      const playerPos = parent.add(0x30);
      const playerX = playerPos.readFloat();
      const playerY = playerPos.add(4).readFloat();
      const playerZ = playerPos.add(8).readFloat();

      console.log(`[批量拾取] 玩家位置: (${playerX.toFixed(1)}, ${playerY.toFixed(1)}, ${playerZ.toFixed(1)})`);

      // 筛选范围内的物品
      const nearbyItems = GPlayerImp.groundItemsFromHook.filter(item => {
        const dx = item.position.x - playerX;
        const dy = item.position.y - playerY;
        const dz = item.position.z - playerZ;
        const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
        return distance <= GPlayerImp.pickupRadius;
      });

      console.log(`[批量拾取] 范围内物品: ${nearbyItems.length}个`);

      if (nearbyItems.length === 0) {
        console.log(`[批量拾取] 范围内没有物品`);
        return;
      }

      // 准备OnPickup函数
      const onPickupFunc = new NativeFunction(
        ptr('0x08688D32'),
        'int',
        ['pointer', 'pointer', 'int', 'bool', 'int', 'bool']
      );

      let successCount = 0;
      let failCount = 0;

      // 批量拾取所有物品
      nearbyItems.forEach((item, index) => {
        try {
          console.log(`[批量拾取] ${index + 1}/${nearbyItems.length}: ID=${item.itemId}, 数量=${item.count}`);

          // 调用拾取函数
          const result = onPickupFunc(
            item.gmatterImpPtr,  // this
            playerXidPtr,        // 玩家XID
            item.count,          // 数量
            false,               // 参数3
            0,                   // 参数4
            false                // 参数5
          );

          if (result) {
            successCount++;
            console.log(`  ✓ 成功`);
          } else {
            failCount++;
            console.log(`  ✗ 失败`);
          }

          // 添加小延迟避免过快
          Thread.sleep(0.05);

        } catch (error) {
          failCount++;
          console.log(`  ✗ 错误: ${error}`);
        }
      });

      console.log(`[批量拾取] 完成: 成功${successCount}个, 失败${failCount}个`);

      // 清理已处理的物品
      GPlayerImp.groundItemsFromHook = GPlayerImp.groundItemsFromHook.filter(item => {
        return !nearbyItems.some(nearby => nearby.gmatterImpPtr.equals(item.gmatterImpPtr));
      });

    } catch (error) {
      console.log(`[批量拾取] 错误: ${error}`);
    }
  }

  // 从Hook记录中获取地面物品
  getGroundItemsFromHook(radius = 50) {
    try {
      this.ensurePointer();

      // 获取玩家位置
      const parent = this.pointer.add(8).readPointer();
      const playerPos = parent.add(0x30);
      const playerX = playerPos.readFloat();
      const playerY = playerPos.add(4).readFloat();
      const playerZ = playerPos.add(8).readFloat();

      console.log(`=== 从Hook记录获取地面物品 ===`);
      console.log(`玩家位置: (${playerX.toFixed(1)}, ${playerY.toFixed(1)}, ${playerZ.toFixed(1)})`);
      console.log(`Hook记录中有 ${GPlayerImp.groundItemsFromHook.length} 个物品`);

      const nearbyItems = [];

      // 清理过期记录（超过5分钟的）
      const now = Date.now();
      GPlayerImp.groundItemsFromHook = GPlayerImp.groundItemsFromHook.filter(
        item => (now - item.timestamp) < 300000
      );

      // 筛选范围内的物品
      GPlayerImp.groundItemsFromHook.forEach((item, index) => {
        try {
          const dx = item.position.x - playerX;
          const dy = item.position.y - playerY;
          const dz = item.position.z - playerZ;
          const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);

          if (distance <= radius) {
            nearbyItems.push({
              ...item,
              distance: distance,
              index: index
            });
          }
        } catch (error) {
          // 忽略错误
        }
      });

      // 按距离排序
      nearbyItems.sort((a, b) => a.distance - b.distance);

      console.log(`找到 ${nearbyItems.length} 个范围内的物品`);

      return nearbyItems;

    } catch (error) {
      console.log('从Hook记录获取物品出错:', error);
      return [];
    }
  }

  // 测试GetMatterByIndex函数是否工作
  testGetMatterByIndex() {
    this.ensurePointer();

    try {
      const worldPtr = this.pointer.add(0x4).readPointer();
      if (!worldPtr || worldPtr.isNull()) {
        console.log('World指针为空');
        return;
      }

      console.log('=== 测试GetMatterByIndex函数 ===');
      console.log('World指针:', worldPtr);

      const getMatterByIndexFunc = new NativeFunction(
        ptr('0x085A073E'), 'pointer', ['pointer', 'int']
      );

      console.log('测试前100个索引:');
      let foundCount = 0;

      for (let i = 0; i < 100; i++) {
        try {
          const matterPtr = getMatterByIndexFunc(worldPtr, i);
          if (matterPtr && !matterPtr.isNull()) {
            console.log(`  索引${i}: Matter指针 = ${matterPtr}`);

            // 检查基本信息
            try {
              const bZombie = matterPtr.add(0xD).readU8();
              const xidType = matterPtr.add(0x3C).readU32();
              const xidId = matterPtr.add(0x40).readU32();
              console.log(`    状态: zombie=${bZombie}, XID=${xidType}:${xidId}`);

              if (!bZombie) {
                foundCount++;

                // 尝试获取物品信息
                const itemInfo = this.getGroundItemInfo(matterPtr);
                if (itemInfo) {
                  console.log(`    物品: ID=${itemInfo.itemId}, 数量=${itemInfo.count}`);
                }
              }
            } catch (error) {
              console.log(`    读取Matter信息出错: ${error}`);
            }
          }
        } catch (error) {
          console.log(`  索引${i}: 调用出错 - ${error}`);
        }
      }

      console.log(`总共找到 ${foundCount} 个有效的Matter对象`);

    } catch (error) {
      console.log('测试GetMatterByIndex出错:', error);
    }
  }

  // 通过Grid系统遍历地面物品（备用方法）
  getGroundItemsViaGrid() {
    this.ensurePointer();
    const items = [];

    try {
      const worldPtr = this.pointer.add(0x4).readPointer();
      if (!worldPtr || worldPtr.isNull()) return items;

      console.log('=== 通过Grid系统遍历地面物品 ===');

      // 获取grid信息
      const gridPtr = worldPtr; // grid在world开始位置
      const pTable = gridPtr.add(0x0).readPointer();
      const sliceCount = gridPtr.add(0x3C).readU32();

      console.log(`Grid: pTable=${pTable}, sliceCount=${sliceCount}`);

      if (!pTable || pTable.isNull()) {
        console.log('Grid表为空');
        return items;
      }

      // 获取玩家位置
      const parent = this.pointer.add(8).readPointer();
      const playerPos = parent.add(0x30);
      const playerX = playerPos.readFloat();
      const playerY = playerPos.add(4).readFloat();
      const playerZ = playerPos.add(8).readFloat();

      console.log(`玩家位置: (${playerX.toFixed(1)}, ${playerY.toFixed(1)}, ${playerZ.toFixed(1)})`);

      // 遍历slice
      for (let i = 0; i < Math.min(sliceCount, 100); i++) {
        try {
          const slicePtr = pTable.add(i * 0x28); // 每个slice 0x28字节
          const matterListHead = slicePtr.add(0x10).readPointer(); // matter_list

          if (matterListHead && !matterListHead.isNull()) {
            console.log(`Slice[${i}] 有Matter列表: ${matterListHead}`);

            // 遍历matter链表
            let currentMatter = matterListHead;
            let count = 0;

            while (currentMatter && !currentMatter.isNull() && count < 10) {
              try {
                const bZombie = currentMatter.add(0xD).readU8();
                if (!bZombie) {
                  const itemInfo = this.getGroundItemInfo(currentMatter);
                  if (itemInfo) {
                    const posX = currentMatter.add(0x30).readFloat();
                    const posY = currentMatter.add(0x34).readFloat();
                    const posZ = currentMatter.add(0x38).readFloat();

                    const dx = posX - playerX;
                    const dy = posY - playerY;
                    const dz = posZ - playerZ;
                    const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);

                    items.push({
                      sliceIndex: i,
                      itemId: itemInfo.itemId,
                      count: itemInfo.count,
                      position: { x: posX, y: posY, z: posZ },
                      distance: distance
                    });

                    console.log(`    找到物品: ID=${itemInfo.itemId}, 距离=${distance.toFixed(1)}m`);
                  }
                }

                // 移动到下一个matter
                currentMatter = currentMatter.add(0x18).readPointer(); // pNext
                count++;
              } catch (error) {
                break;
              }
            }
          }
        } catch (error) {
          continue;
        }
      }

      console.log(`通过Grid找到 ${items.length} 个物品`);
      return items;

    } catch (error) {
      console.log('Grid遍历出错:', error);
      return items;
    }
  }

  // 获取附近地面物品的简洁方法
  getNearbyGroundItems(radius = 50) {
    this.ensurePointer();

    // 优先使用Hook记录的信息
    console.log('=== 方法1: 从Hook记录获取 ===');
    const hookItems = this.getGroundItemsFromHook(radius);

    if (hookItems.length > 0) {
      console.log(`Hook方法找到 ${hookItems.length} 个物品`);
      return hookItems;
    }

    // 备用方法1: 测试GetMatterByIndex
    console.log('=== 方法2: 测试GetMatterByIndex ===');
    this.testGetMatterByIndex();

    // 备用方法2: Grid遍历
    console.log('=== 方法3: Grid遍历 ===');
    const gridItems = this.getGroundItemsViaGrid();

    if (gridItems.length > 0) {
      return gridItems.filter(item => item.distance <= radius);
    }

    console.log('所有方法都没有找到地面物品');
    return [];
  }

  // 发送拾取命令
  sendPickupCommand(matterXid) {
    try {
      this.ensurePointer();

      // 构造拾取命令数据 (基于反汇编分析)
      const cmdData = Memory.alloc(16);
      cmdData.writeU32(matterXid.type);     // XID type
      cmdData.add(4).writeU32(matterXid.id); // XID id
      cmdData.add(8).writeU32(0);           // 其他参数
      cmdData.add(12).writeU32(0);

      // 获取controller指针
      const controllerPtr = this.pointer.add(0xC).readPointer(); // _commander
      if (!controllerPtr || controllerPtr.isNull()) {
        console.log('无法获取controller指针');
        return false;
      }

      // 调用CommandHandler (命令6是拾取命令)
      const commandHandlerFunc = new NativeFunction(
        controllerPtr.readPointer().add(0x20).readPointer(), // 虚函数表中的CommandHandler
        'int',
        ['pointer', 'int', 'pointer', 'uint']
      );

      const result = commandHandlerFunc(controllerPtr, 6, cmdData, 16);
      return result !== 0;

    } catch (error) {
      console.log('发送拾取命令出错:', error);
      return false;
    }
  }

  // 检查背包是否有空位
  hasInventorySpace() {
    try {
      this.ensurePointer();

      // 获取背包指针 (this + 0x758)
      const inventoryPtr = this.pointer.add(0x758);

      // 调用item_list::HasSlot检查是否有空位
      const hasSlotFunc = new NativeFunction(
        ptr('0x08634000'), // 需要找到item_list::HasSlot的地址
        'bool',
        ['pointer', 'int']
      );

      // 检查是否有空位 (-1表示任意空位)
      return hasSlotFunc(inventoryPtr, -1);

    } catch (error) {
      console.log('检查背包空位出错:', error);
      return false;
    }
  }

  // 一键拾取附近所有物品
  pickupAllNearbyItems(radius = 30) {
    this.ensurePointer();

    try {
      console.log('=== 开始一键拾取 ===');

      // 获取附近物品
      const items = this.getNearbyGroundItems(radius);

      if (items.length === 0) {
        console.log('附近没有物品可拾取');
        return { success: false, message: '没有找到物品' };
      }

      console.log(`找到 ${items.length} 个物品，开始拾取...`);

      let pickupCount = 0;
      let failCount = 0;

      // 遍历拾取每个物品
      for (let i = 0; i < items.length; i++) {
        const item = items[i];

        try {
          console.log(`拾取第${i + 1}个物品: ID=${item.itemId}, 数量=${item.count}, 距离=${item.distance.toFixed(1)}m`);

          // 检查背包空间
          if (!this.hasInventorySpace()) {
            console.log('背包已满，停止拾取');
            break;
          }

          // 发送拾取命令
          const success = this.sendPickupCommand(item.xid);

          if (success) {
            pickupCount++;
            console.log(`  ✓ 拾取成功`);

            // 添加小延迟避免过快操作
            Thread.sleep(0.1);
          } else {
            failCount++;
            console.log(`  ✗ 拾取失败`);
          }

        } catch (error) {
          failCount++;
          console.log(`拾取物品${i + 1}失败: ${error}`);
        }
      }

      console.log(`拾取完成: 成功${pickupCount}个, 失败${failCount}个`);

      return {
        success: pickupCount > 0,
        message: `成功拾取 ${pickupCount} 个物品，失败 ${failCount} 个`,
        pickupCount: pickupCount,
        failCount: failCount
      };

    } catch (error) {
      console.log('一键拾取出错:', error);
      return { success: false, message: error.toString() };
    }
  }

  // 简化版拾取：直接调用gplayer_controller的CommandHandler
  simplePickupItem(matterXid) {
    try {
      this.ensurePointer();

      // 获取gplayer_controller指针
      const controllerPtr = this.pointer.add(0xC).readPointer();
      if (!controllerPtr || controllerPtr.isNull()) {
        return false;
      }

      // 构造拾取命令数据
      const pickupData = Memory.alloc(16);
      pickupData.writeU32(0);                    // 可能是命令参数
      pickupData.add(4).writeU32(0);
      pickupData.add(8).writeU32(matterXid.type); // XID type
      pickupData.add(12).writeU32(matterXid.id);  // XID id

      // 直接调用CommandHandler函数 (地址从反汇编中获取)
      const commandHandlerAddr = ptr('0x086ADA10'); // gplayer_controller::CommandHandler的地址
      const commandHandlerFunc = new NativeFunction(
        commandHandlerAddr,
        'int',
        ['pointer', 'int', 'pointer', 'uint']
      );

      // 调用命令6 (拾取命令)
      const result = commandHandlerFunc(controllerPtr, 6, pickupData, 16);

      console.log(`拾取命令结果: ${result}`);
      return result !== 0;

    } catch (error) {
      console.log('简化拾取出错:', error);
      return false;
    }
  }

  // 真正的一键拾取功能
  realPickupAllItems(radius = 30) {
    try {
      console.log('=== 真正的一键拾取 ===');

      // 获取附近物品
      const items = this.getNearbyGroundItems(radius);

      if (items.length === 0) {
        console.log('没有找到可拾取的物品');
        return { success: false, message: '没有找到物品' };
      }

      console.log(`找到 ${items.length} 个物品，开始拾取...`);

      let pickupCount = 0;
      let failCount = 0;

      // 遍历拾取每个物品
      for (let i = 0; i < items.length; i++) {
        const item = items[i];

        try {
          console.log(`拾取第${i + 1}个物品: ID=${item.itemId}, 数量=${item.count}, 距离=${item.distance.toFixed(1)}m`);

          // 直接调用游戏的拾取逻辑
          // 模拟玩家点击拾取
          const success = this.triggerPickup(item);

          if (success) {
            pickupCount++;
            console.log(`  ✓ 拾取成功`);

            // 添加延迟避免过快操作
            Thread.sleep(0.2);
          } else {
            failCount++;
            console.log(`  ✗ 拾取失败`);
          }

        } catch (error) {
          failCount++;
          console.log(`拾取物品${i + 1}失败: ${error}`);
        }
      }

      console.log(`拾取完成: 成功${pickupCount}个, 失败${failCount}个`);

      return {
        success: pickupCount > 0,
        message: `成功拾取 ${pickupCount} 个物品，失败 ${failCount} 个`,
        pickupCount: pickupCount,
        failCount: failCount
      };

    } catch (error) {
      console.log('一键拾取出错:', error);
      return { success: false, message: error.toString() };
    }
  }

  // 触发拾取操作
  triggerPickup(item) {
    try {
      // 方法1: 直接调用gmatter_item_imp的OnPickup
      if (item.gmatterImpPtr) {
        const onPickupFunc = new NativeFunction(
          ptr('0x08688D32'), // gmatter_item_imp::OnPickup地址
          'int',
          ['pointer', 'pointer', 'int', 'bool', 'int', 'bool']
        );

        // 获取玩家XID
        const parent = this.pointer.add(8).readPointer();
        const playerXidStruct = Memory.alloc(8);
        playerXidStruct.writeU32(parent.add(0x3C).readU32()); // type
        playerXidStruct.add(4).writeU32(parent.add(0x40).readU32()); // id

        const result = onPickupFunc(item.gmatterImpPtr, playerXidStruct, item.count, false, 0, false);
        return result !== 0;
      }

      return false;

    } catch (error) {
      console.log('触发拾取出错:', error);
      return false;
    }
  }

  // 测试拾取单个物品
  testPickupSingleItem() {
    try {
      console.log('=== 测试拾取单个物品 ===');

      // 获取附近物品
      const items = this.getNearbyGroundItems(50);

      if (items.length === 0) {
        console.log('没有找到可拾取的物品');
        return false;
      }

      const firstItem = items[0];
      console.log(`尝试拾取物品: ID=${firstItem.itemId}, XID=${firstItem.xid.type}:${firstItem.xid.id}`);

      // 尝试拾取
      const success = this.triggerPickup(firstItem);

      if (success) {
        console.log('拾取成功');
      } else {
        console.log('拾取失败');
      }

      return success;

    } catch (error) {
      console.log('测试拾取出错:', error);
      return false;
    }
  }

  // 物品收集管理功能

  // 开始收集模式
  static startItemCollection() {
    GPlayerImp.itemCollectionEnabled = true;
    GPlayerImp.collectedItems = [];
    GPlayerImp.collectionStartTime = Date.now();
    console.log('🎯 物品收集模式已启动！');
    console.log('现在所有掉落的物品都会被自动记录，使用 stopAndPickupAll() 来批量拾取');
  }

  // 停止收集并显示统计
  static stopItemCollection() {
    GPlayerImp.itemCollectionEnabled = false;
    const duration = (Date.now() - GPlayerImp.collectionStartTime) / 1000;

    console.log('📊 物品收集统计:');
    console.log(`收集时长: ${duration.toFixed(1)}秒`);
    console.log(`收集物品: ${GPlayerImp.collectedItems.length}个`);

    // 按物品ID分组统计
    const itemStats = {};
    GPlayerImp.collectedItems.forEach(item => {
      if (!itemStats[item.itemId]) {
        itemStats[item.itemId] = { count: 0, totalAmount: 0 };
      }
      itemStats[item.itemId].count++;
      itemStats[item.itemId].totalAmount += item.count;
    });

    console.log('物品详情:');
    Object.entries(itemStats).forEach(([itemId, stats]) => {
      console.log(`  物品ID ${itemId}: ${stats.count}堆, 总数量 ${stats.totalAmount}`);
    });

    return {
      duration: duration,
      totalItems: GPlayerImp.collectedItems.length,
      itemStats: itemStats
    };
  }

  // 获取收集状态
  static getCollectionStatus() {
    if (!GPlayerImp.itemCollectionEnabled) {
      return { enabled: false, message: '收集模式未启用' };
    }

    const duration = (Date.now() - GPlayerImp.collectionStartTime) / 1000;
    return {
      enabled: true,
      duration: duration,
      itemCount: GPlayerImp.collectedItems.length,
      message: `收集中... 已收集${GPlayerImp.collectedItems.length}个物品 (${duration.toFixed(1)}秒)`
    };
  }

  // 清空收集列表
  static clearCollection() {
    const count = GPlayerImp.collectedItems.length;
    GPlayerImp.collectedItems = [];
    console.log(`已清空收集列表 (${count}个物品)`);
  }

  // 停止收集并批量拾取所有物品
  static stopAndPickupAll(gplayerInstance) {
    try {
      console.log('🚀 停止收集并开始批量拾取...');

      // 停止收集模式
      const stats = GPlayerImp.stopItemCollection();

      if (GPlayerImp.collectedItems.length === 0) {
        console.log('没有收集到任何物品');
        return { success: false, message: '没有物品可拾取' };
      }

      // 过滤掉已经过期的物品 (超过5分钟)
      const now = Date.now();
      const validItems = GPlayerImp.collectedItems.filter(item =>
        (now - item.timestamp) < 300000
      );

      console.log(`有效物品: ${validItems.length}/${GPlayerImp.collectedItems.length}`);

      if (validItems.length === 0) {
        console.log('所有收集的物品都已过期');
        return { success: false, message: '所有物品都已过期' };
      }

      // 获取玩家位置
      const parent = gplayerInstance.pointer.add(8).readPointer();
      const playerPos = parent.add(0x30);
      const playerX = playerPos.readFloat();
      const playerY = playerPos.add(4).readFloat();
      const playerZ = playerPos.add(8).readFloat();

      // 计算距离并排序
      const itemsWithDistance = validItems.map(item => {
        const dx = item.position.x - playerX;
        const dy = item.position.y - playerY;
        const dz = item.position.z - playerZ;
        const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
        return { ...item, distance };
      });

      // 按距离排序，先拾取近的
      itemsWithDistance.sort((a, b) => a.distance - b.distance);

      console.log(`开始批量拾取 ${itemsWithDistance.length} 个物品...`);

      let pickupCount = 0;
      let failCount = 0;

      // 批量拾取
      for (let i = 0; i < itemsWithDistance.length; i++) {
        const item = itemsWithDistance[i];

        try {
          console.log(`拾取 ${i + 1}/${itemsWithDistance.length}: ID=${item.itemId}, 距离=${item.distance.toFixed(1)}m`);

          // 调用拾取函数
          const success = gplayerInstance.triggerPickup(item);

          if (success) {
            pickupCount++;
            console.log(`  ✓ 成功`);
          } else {
            failCount++;
            console.log(`  ✗ 失败`);
          }

          // 添加延迟避免过快操作
          Thread.sleep(0.15);

        } catch (error) {
          failCount++;
          console.log(`  ✗ 错误: ${error}`);
        }
      }

      // 清空收集列表
      GPlayerImp.clearCollection();

      const result = {
        success: pickupCount > 0,
        message: `批量拾取完成: 成功${pickupCount}个, 失败${failCount}个`,
        pickupCount: pickupCount,
        failCount: failCount,
        stats: stats
      };

      console.log('🎉 ' + result.message);
      return result;

    } catch (error) {
      console.log('批量拾取出错:', error);
      return { success: false, message: error.toString() };
    }
  }

  // 一键拾取控制方法

  // 启用一键拾取
  static startOneClickPickup(radius = 30) {
    if (!global.groundItemHookEnabled) {
      GPlayerImp.enableOneClickPickupHook();
      global.groundItemHookEnabled = true;
    }

    GPlayerImp.oneClickPickupEnabled = true;
    GPlayerImp.pickupRadius = radius;

    console.log(`🎯 一键拾取已启用！`);
    console.log(`   拾取范围: ${radius}米`);
    console.log(`   现在拾取任意物品时会自动拾取附近所有物品`);
  }

  // 停止一键拾取
  static stopOneClickPickup() {
    GPlayerImp.oneClickPickupEnabled = false;
    console.log('🛑 一键拾取已停止');
  }

  // 获取一键拾取状态
  static getOneClickPickupStatus() {
    return {
      enabled: GPlayerImp.oneClickPickupEnabled,
      radius: GPlayerImp.pickupRadius,
      hookEnabled: global.groundItemHookEnabled || false
    };
  }

  // 设置一键拾取范围
  static setPickupRadius(radius) {
    GPlayerImp.pickupRadius = radius || 30;
    console.log(`一键拾取范围已更新: ${GPlayerImp.pickupRadius}米`);
  }



  // 测试所有可能的扫描方法
  static testAllScanMethods() {
    console.log('=== 测试所有扫描方法 ===');

    try {
      const gplayerInstance = new GPlayerImp();

      // 测试1: 不同的GetMatterByIndex地址
      console.log('\n1. 测试GetMatterByIndex不同地址:');
      const addresses = [
        '0x085A073E', '0x085A0740', '0x085A0730', '0x085A0750',
        '0x085A0720', '0x085A0760', '0x085A0710', '0x085A0770'
      ];

      const worldPtr = gplayerInstance.pointer.add(0x4).readPointer();
      console.log(`World指针: ${worldPtr}`);

      for (const addr of addresses) {
        try {
          const func = new NativeFunction(ptr(addr), 'pointer', ['pointer', 'int']);
          const result = func(worldPtr, 0);
          console.log(`  ${addr}: ${result ? '✓ 有效' : '✗ 无效'} (返回: ${result})`);

          if (result && !result.isNull()) {
            // 尝试读取返回的指针
            try {
              const xidType = result.add(0x3C).readU32();
              const xidId = result.add(0x40).readU32();
              console.log(`    -> XID: ${xidType}:${xidId}`);
            } catch (error) {
              console.log(`    -> 读取XID失败: ${error}`);
            }
          }
        } catch (error) {
          console.log(`  ${addr}: ✗ 调用失败 (${error})`);
        }
      }

      // 测试2: 查找可能的枚举函数
      console.log('\n2. 测试可能的枚举函数:');
      const enumFunctions = [
        { name: 'EnumNearbyObjects', addr: '0x08500000' },
        { name: 'GetObjectsInRange', addr: '0x08600000' },
        { name: 'FindNearbyMatters', addr: '0x08700000' },
      ];

      // 这些是猜测的地址，实际需要从IDA中找到
      console.log('  (需要从IDA中找到实际的枚举函数地址)');

      // 测试3: 分析World结构寻找对象列表
      console.log('\n3. 分析World结构:');
      if (worldPtr && !worldPtr.isNull()) {
        for (let offset = 0; offset < 0x200; offset += 0x10) {
          try {
            const ptr1 = worldPtr.add(offset).readPointer();
            const ptr2 = worldPtr.add(offset + 4).readPointer();
            const val1 = worldPtr.add(offset + 8).readU32();
            const val2 = worldPtr.add(offset + 12).readU32();

            if (ptr1 && !ptr1.isNull()) {
              console.log(`  +0x${offset.toString(16).padStart(3, '0')}: ${ptr1} ${ptr2} 0x${val1.toString(16)} 0x${val2.toString(16)}`);
            }
          } catch (error) {
            continue;
          }
        }
      }

    } catch (error) {
      console.log('测试扫描方法出错:', error);
    }
  }

  // 构建拾取消息
  static buildPickupMessage(item, playerXid) {
    try {
      // 分配消息结构 (假设MSG结构大小为72字节)
      const msgStruct = Memory.alloc(72);

      // 调用BuildMessage函数
      // BuildMessage(MSG *msg, int type, const XID *picker, const XID *item, const A3DVECTOR *pos, int param, void *data, uint size)
      const buildMessageFunc = new NativeFunction(
        ptr('0x08500000'), // 需要找到BuildMessage的实际地址
        'void',
        ['pointer', 'int', 'pointer', 'pointer', 'pointer', 'int', 'pointer', 'uint']
      );

      // 创建拾取者XID
      const pickerXidStruct = Memory.alloc(8);
      pickerXidStruct.writeU32(playerXid.type);
      pickerXidStruct.add(4).writeU32(playerXid.id);

      // 创建物品XID
      const itemXidStruct = Memory.alloc(8);
      itemXidStruct.writeU32(item.xid.type);
      itemXidStruct.add(4).writeU32(item.xid.id);

      // 创建位置向量
      const posStruct = Memory.alloc(12);
      posStruct.writeFloat(item.position.x);
      posStruct.add(4).writeFloat(item.position.y);
      posStruct.add(8).writeFloat(item.position.z);

      // 构建消息
      buildMessageFunc(
        msgStruct,        // MSG *msg
        16,               // int type (GM_MSG_PICKUP)
        pickerXidStruct,  // const XID *picker
        itemXidStruct,    // const XID *item
        posStruct,        // const A3DVECTOR *pos
        item.count,       // int param (数量)
        ptr(0),           // void *data
        0                 // uint size
      );

      return msgStruct;

    } catch (error) {
      console.log('构建拾取消息出错:', error);
      return null;
    }
  }

  // 批量发送拾取消息
  static sendBatchPickupMessages(items, playerXid) {
    try {
      console.log(`[批量消息] 为${items.length}个物品构建拾取消息...`);

      // 获取SendMessage函数
      const sendMessageFunc = new NativeFunction(
        ptr('0x08600000'), // 需要找到gmatrix::SendMessage的实际地址
        'int',
        ['pointer', 'pointer']
      );

      let successCount = 0;

      items.forEach((item, index) => {
        try {
          console.log(`[批量消息] 构建第${index + 1}个消息: ID=${item.itemId}`);

          // 构建单个物品的拾取消息
          const msg = GPlayerImp.buildPickupMessage(item, playerXid);

          if (msg) {
            // 发送消息
            const result = sendMessageFunc(msg, ptr(0));

            if (result) {
              successCount++;
              console.log(`  ✓ 消息发送成功`);
            } else {
              console.log(`  ✗ 消息发送失败`);
            }
          } else {
            console.log(`  ✗ 消息构建失败`);
          }

          // 添加延迟避免过快
          Thread.sleep(0.05);

        } catch (error) {
          console.log(`  ✗ 处理第${index + 1}个消息出错: ${error}`);
        }
      });

      console.log(`[批量消息] 完成: 成功发送${successCount}/${items.length}个消息`);
      return successCount;

    } catch (error) {
      console.log('[批量消息] 发送出错:', error);
      return 0;
    }
  }

  // 构建包含多个物品的批量拾取消息
  static buildBatchPickupMessage(items, playerXid) {
    try {
      console.log(`[批量消息] 构建包含${items.length}个物品的批量消息...`);

      // 计算消息大小: 基础消息 + 物品数量 + 每个物品的数据
      const itemDataSize = 20; // 假设每个物品数据20字节 (XID + 位置 + 数量)
      const totalDataSize = 4 + (items.length * itemDataSize); // 4字节物品数量 + 物品数据

      // 分配数据缓冲区
      const dataBuffer = Memory.alloc(totalDataSize);
      let offset = 0;

      // 写入物品数量
      dataBuffer.add(offset).writeU32(items.length);
      offset += 4;

      // 写入每个物品的数据
      items.forEach(item => {
        // XID (8字节)
        dataBuffer.add(offset).writeU32(item.xid.type);
        dataBuffer.add(offset + 4).writeU32(item.xid.id);
        offset += 8;

        // 位置 (12字节)
        dataBuffer.add(offset).writeFloat(item.position.x);
        dataBuffer.add(offset + 4).writeFloat(item.position.y);
        dataBuffer.add(offset + 8).writeFloat(item.position.z);
        offset += 12;

        // 数量 (4字节) - 这里可能需要调整
        // dataBuffer.add(offset).writeU32(item.count);
        // offset += 4;
      });

      // 分配消息结构
      const msgStruct = Memory.alloc(72);

      // 构建批量拾取消息
      const buildMessageFunc = new NativeFunction(
        ptr('0x08500000'), // BuildMessage地址
        'void',
        ['pointer', 'int', 'pointer', 'pointer', 'pointer', 'int', 'pointer', 'uint']
      );

      // 创建拾取者XID
      const pickerXidStruct = Memory.alloc(8);
      pickerXidStruct.writeU32(playerXid.type);
      pickerXidStruct.add(4).writeU32(playerXid.id);

      // 使用第一个物品的信息作为基础
      const firstItem = items[0];
      const itemXidStruct = Memory.alloc(8);
      itemXidStruct.writeU32(firstItem.xid.type);
      itemXidStruct.add(4).writeU32(firstItem.xid.id);

      const posStruct = Memory.alloc(12);
      posStruct.writeFloat(firstItem.position.x);
      posStruct.add(4).writeFloat(firstItem.position.y);
      posStruct.add(8).writeFloat(firstItem.position.z);

      // 构建消息 (可能需要使用不同的消息类型来表示批量拾取)
      buildMessageFunc(
        msgStruct,        // MSG *msg
        17,               // int type (可能的批量拾取消息类型)
        pickerXidStruct,  // const XID *picker
        itemXidStruct,    // const XID *item (第一个物品)
        posStruct,        // const A3DVECTOR *pos
        items.length,     // int param (物品数量)
        dataBuffer,       // void *data (所有物品数据)
        totalDataSize     // uint size
      );

      return msgStruct;

    } catch (error) {
      console.log('构建批量拾取消息出错:', error);
      return null;
    }
  }

  // 触发挂机拾取来获取地面物品数据
  static triggerGuajiPickup() {
    try {
      console.log('🔍 尝试触发挂机拾取来获取地面物品数据...');

      const gplayerInstance = new GPlayerImp();

      // 尝试调用挂机相关函数来获取数据
      // 这可能需要特定的条件或参数

      console.log('💡 提示: 如果你有挂机宠物，让它拾取物品可能会触发数据解析');

    } catch (error) {
      console.log('触发挂机拾取出错:', error);
    }
  }

  // 测试基本功能
  static testBasicFunctions() {
    console.log('🧪 测试基本功能...');

    try {
      // 测试1: 创建GPlayerImp实例
      console.log('1. 测试GPlayerImp实例创建:');
      const gplayerInstance = GPlayerImp.getCurrentPlayer();
      console.log(`   ✓ 实例创建成功: ${gplayerInstance.pointer}`);

      // 测试2: 读取基本指针
      console.log('2. 测试指针读取:');
      try {
        const parent = gplayerInstance.pointer.add(8).readPointer();
        if (parent && !parent.isNull()) {
          console.log(`   ✓ parent指针: ${parent}`);

          // 测试位置读取
          const playerPos = parent.add(0x30);
          const x = playerPos.readFloat();
          const y = playerPos.add(4).readFloat();
          const z = playerPos.add(8).readFloat();
          console.log(`   ✓ 玩家位置: (${x.toFixed(1)}, ${y.toFixed(1)}, ${z.toFixed(1)})`);
        } else {
          console.log(`   ✗ parent指针为空`);
        }
      } catch (error) {
        console.log(`   ✗ 指针读取失败: ${error}`);
      }

      // 测试3: 检查Hook状态
      console.log('3. 检查Hook状态:');
      console.log(`   一键拾取启用: ${GPlayerImp.oneClickPickupEnabled}`);
      console.log(`   拾取范围: ${GPlayerImp.pickupRadius}米`);
      console.log(`   地面物品记录: ${GPlayerImp.groundItemsFromHook ? GPlayerImp.groundItemsFromHook.length : 0}个`);

      console.log('✅ 基本功能测试完成');

    } catch (error) {
      console.log('❌ 基本功能测试失败:', error);
    }
  }

  // 简化的物品ID获取方法
  getTempIdFromXID(xidType, xidId) {
    if (xidType === 3) {
      // 对于Matter类型，我们需要先找到Matter对象，然后获取物品ID
      // 这里暂时返回XID的ID部分，可能需要进一步处理
      return xidId;
    } else {
      // 对于NPC/玩家，使用TemplateID
      return this.GetTargetTemplateId(xidType, xidId);
    }
  }

  // Hook地面物品相关函数来获取物品数据
  static hookGroundItemFunctions() {
    try {
      // Hook SetData函数
      const setDataAddr = ptr('0x08688AFC');
      Interceptor.attach(setDataAddr, {
        onEnter: function(args) {
          const thisPtr = args[0];      // gmatter_item_imp *this
          const itemDataPtr = args[1];  // item_data const&

          try {
            console.log(`[SetData] gmatter_item_imp: ${thisPtr}, item_data: ${itemDataPtr}`);
            GPlayerImp.analyzeItemData(itemDataPtr);
          } catch (error) {
            console.log('SetData Hook出错:', error);
          }
        }
      });

      // Hook AttachData函数
      const attachDataAddr = ptr('0x08688B46');
      Interceptor.attach(attachDataAddr, {
        onEnter: function(args) {
          const thisPtr = args[0];      // gmatter_item_imp *this
          const itemDataPtr = args[1];  // item_data *

          try {
            console.log(`[AttachData] gmatter_item_imp: ${thisPtr}, item_data: ${itemDataPtr}`);
            GPlayerImp.analyzeItemData(itemDataPtr);
          } catch (error) {
            console.log('AttachData Hook出错:', error);
          }
        }
      });

      // Hook OnPickup函数（正确版本）
      const onPickupAddr = ptr('0x08688D32');
      Interceptor.attach(onPickupAddr, {
        onEnter: function(args) {
          const thisPtr = args[0];  // gmatter_item_imp *this
          const xidPtr = args[1];   // const XID * (拾取者的XID)

          try {
            const pickerXidType = xidPtr.readU32();
            const pickerXidId = xidPtr.add(4).readU32();

            console.log(`[OnPickup] 拾取者XID: ${pickerXidType}:${pickerXidId}`);

            // 读取正确的item_data信息
            try {
              const itemDataPtr = thisPtr.add(0x38).readPointer();
              if (itemDataPtr && !itemDataPtr.isNull()) {
                const type = itemDataPtr.add(0x00).readU32();           // 物品ID
                const count = itemDataPtr.add(0x04).readU32();          // 数量
                const pileLimit = itemDataPtr.add(0x08).readU32();      // 堆叠上限
                const procType = itemDataPtr.add(0x0C).readU32();       // 处理类型
                const price = itemDataPtr.add(0x18).readU32();          // 价格
                const expireDate = itemDataPtr.add(0x1C).readU32();     // 过期时间

                console.log(`  拾取物品: ID=${type}, 数量=${count}/${pileLimit}, 价格=${price}`);
                if (expireDate > 0) {
                  console.log(`  过期时间: ${expireDate}, 处理类型: ${procType}`);
                }
              } else {
                console.log('  无法获取物品数据');
              }
            } catch (error) {
              console.log('  读取物品数据出错:', error);
            }
          } catch (error) {
            console.log('OnPickup Hook出错:', error);
          }
        }
      });

      console.log('已Hook地面物品相关函数: SetData, AttachData, OnPickup');

    } catch (error) {
      console.log('Hook地面物品函数失败:', error);
    }
  }

  // 保持原有的Hook函数作为备用
  static hookGroundItemPickup() {
    return GPlayerImp.hookGroundItemFunctions();
  }

  // 测试TempID转换的各种方法
  testTempIdMethods() {
    this.ensurePointer();

    try {
      console.log('=== 测试TempID转换方法 ===');

      // 测试一些已知的XID
      const testXids = [
        { type: 1, id: 2720 },  // 从Hook输出中看到的玩家XID
        { type: 3, id: 5678 },  // 假设的Matter XID
        { type: 2, id: 1234 },  // 假设的NPC XID
      ];

      testXids.forEach(xid => {
        console.log(`测试XID ${xid.type}:${xid.id}:`);

        // 方法1: ID2IDX
        const method1 = GPlayerImp.simpleId2Idx(xid.id);
        console.log(`  ID2IDX结果: ${method1}`);

        // 方法2: 直接位运算
        const method2 = xid.id & 0xFFFF;
        console.log(`  低16位: ${method2}`);

        // 方法3: 高16位
        const method3 = (xid.id >> 16) & 0xFFFF;
        console.log(`  高16位: ${method3}`);

        // 方法4: 其他可能的变换
        const method4 = xid.id % 10000;
        console.log(`  模10000: ${method4}`);
      });

    } catch (error) {
      console.log('测试TempID方法出错:', error);
    }
  }

  // 简单测试：尝试手动丢一个物品然后找到它
  testDropAndFind() {
    this.ensurePointer();

    try {
      console.log('=== 测试丢物品和查找 ===');

      // 先测试TempID方法
      this.testTempIdMethods();

      // 启用Hook
      GPlayerImp.hookGroundItemPickup();

      console.log('请手动丢一个物品到地上，然后尝试拾取它，观察Hook输出');

    } catch (error) {
      console.log('测试时出错:', error);
    }
  }

  // 尝试通过不同的方法查找地面物品
  findGroundItemsAlternative() {
    this.ensurePointer();
    const items = [];

    try {
      console.log('=== 尝试替代方法查找地面物品 ===');

      // 方法1: 通过GetMatterByIndex遍历
      const worldPtr = this.pointer.add(0x4).readPointer();
      if (!worldPtr || worldPtr.isNull()) {
        console.log('无法获取world指针');
        return items;
      }

      const getMatterByIndexFunc = new NativeFunction(
        ptr('0x085A073E'),
        'pointer',
        ['pointer', 'int']
      );

      console.log('尝试通过GetMatterByIndex查找物品...');

      for (let i = 0; i < 100; i++) {
        try {
          const matterPtr = getMatterByIndexFunc(worldPtr, i);
          if (matterPtr && !matterPtr.isNull()) {
            // 检查是否为有效的matter (gmatter继承自gobject)
            const bZombie = matterPtr.add(0xD).readU8();        // gobject.b_zombie
            const bDisconnect = matterPtr.add(0xE).readU8();    // gobject.b_disconnect

            if (!bZombie && !bDisconnect) {
              // gobject基础信息
              const xidType = matterPtr.add(0x3C).readU32();
              const xidId = matterPtr.add(0x40).readU32();
              const tag = matterPtr.add(0x24).readU32();
              const posX = matterPtr.add(0x30).readFloat();
              const posY = matterPtr.add(0x34).readFloat();
              const posZ = matterPtr.add(0x38).readFloat();

              // gmatter特有信息
              const matterState = matterPtr.add(0x4E).readU8();    // matter_state
              const battleFlag = matterPtr.add(0x4F).readU8();     // battle_flag
              const matterType = matterPtr.add(0x5C).readU32();    // matter_type
              const spawnIndex = matterPtr.add(0x60).readU32();    // spawn_index
              const nameId = matterPtr.add(0x64).readU32();        // name_id

              items.push({
                index: i,
                xid: { type: xidType, id: xidId },
                tag: tag,
                position: { x: posX, y: posY, z: posZ },
                matterType: matterType,
                matterState: matterState,
                nameId: nameId,
                spawnIndex: spawnIndex
              });

              const tempId = this.getTempIdFromXID(xidType, xidId);
              console.log(`找到Matter[${i}]: XID ${xidType}:${xidId} (TempID: ${tempId}), 类型: ${matterType}, 名称ID: ${nameId}`);
              console.log(`  位置: (${posX.toFixed(2)}, ${posY.toFixed(2)}, ${posZ.toFixed(2)}), 状态: ${matterState}, Tag: ${tag}`);
            }
          }
        } catch (error) {
          // 忽略错误，继续下一个
          continue;
        }
      }

      console.log(`通过GetMatterByIndex找到${items.length}个物品`);
      return items;

    } catch (error) {
      console.log('替代方法查找出错:', error);
      return items;
    }
  }

  // 获取当前地图的所有地面物品
  getAllGroundItems() {
    this.ensurePointer();
    const items = [];

    try {
      console.log('=== 获取当前地图所有地面物品 ===');

      const worldPtr = this.pointer.add(0x4).readPointer();
      if (!worldPtr || worldPtr.isNull()) {
        console.log('无法获取world指针');
        return items;
      }

      // 获取玩家位置用于计算距离
      const parent = this.pointer.add(8).readPointer();
      const playerPos = parent.add(0x30);
      const playerX = playerPos.readFloat();
      const playerY = playerPos.add(4).readFloat();
      const playerZ = playerPos.add(8).readFloat();

      const getMatterByIndexFunc = new NativeFunction(
        ptr('0x085A073E'),
        'pointer',
        ['pointer', 'int']
      );

      // 扩大搜索范围
      for (let i = 0; i < 1000; i++) {
        try {
          const matterPtr = getMatterByIndexFunc(worldPtr, i);
          if (matterPtr && !matterPtr.isNull()) {
            const bZombie = matterPtr.add(0xD).readU8();

            if (!bZombie) {
              const posX = matterPtr.add(0x30).readFloat();
              const posY = matterPtr.add(0x34).readFloat();
              const posZ = matterPtr.add(0x38).readFloat();

              // 计算距离
              const dx = posX - playerX;
              const dy = posY - playerY;
              const dz = posZ - playerZ;
              const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);

              const matterType = matterPtr.add(0x5C).readU32();
              const nameId = matterPtr.add(0x64).readU32();
              const xidType = matterPtr.add(0x3C).readU32();
              const xidId = matterPtr.add(0x40).readU32();

              items.push({
                index: i,
                xid: { type: xidType, id: xidId },
                position: { x: posX, y: posY, z: posZ },
                distance: distance,
                matterType: matterType,
                nameId: nameId
              });
            }
          }
        } catch (error) {
          continue;
        }
      }

      // 按距离排序
      items.sort((a, b) => a.distance - b.distance);

      console.log(`找到${items.length}个地面物品，最近的5个:`);

      items.slice(0, 5).forEach(item => {
        console.log(`  XID: ${item.xid.type}:${item.xid.id}, Matter类型: ${item.matterType}, 名称ID: ${item.nameId}, 距离: ${item.distance.toFixed(2)}`);

        // 获取真实的物品ID
        try {
          const matterPtr = getMatterByIndexFunc(worldPtr, item.index);
          if (matterPtr && !matterPtr.isNull()) {
            const itemInfo = this.getGroundItemId(matterPtr);
            if (itemInfo) {
              console.log(`    真实物品ID: ${itemInfo.itemId}, 数量: ${itemInfo.count}`);
            } else {
              console.log(`    无法获取物品ID`);
            }
          }
        } catch (error) {
          console.log(`    获取物品ID出错: ${error}`);
        }

        // 尝试通过QueryObject获取更多信息
        const result = this.QueryObject(item.xid.type, item.xid.id, 0x38);
        if (result.success) {
          const objInfo = result.objectInfo;
          const state = objInfo.readU32();           // state
          const tag = objInfo.add(4).readU32();      // tag
          const tid = objInfo.add(0x2C).readU32();   // tid
          console.log(`    QueryObject结果: state=${state}, tag=${tag}, tid=${tid}`);
        } else {
          console.log(`    QueryObject失败`);
        }
      });

      return items;

    } catch (error) {
      console.log('获取所有地面物品出错:', error);
      return items;
    }
  }

  // 通过grid系统遍历地面物品
  getGroundItemsFromGrid() {
    this.ensurePointer();
    const groundItems = [];

    try {
      // 获取world指针
      const worldPtr = this.pointer.add(0x4).readPointer();
      if (!worldPtr || worldPtr.isNull()) {
        console.log('无法获取world指针');
        return groundItems;
      }

      // 获取grid指针 (grid在world的偏移0x0)
      const gridPtr = worldPtr;

      // 读取grid信息
      const pTable = gridPtr.add(0x0).readPointer();     // slice *pTable
      const sliceCount = gridPtr.add(0x3C).readU32();    // int slice_count
      const regRow = gridPtr.add(0x40).readU32();        // int reg_row
      const regColumn = gridPtr.add(0x44).readU32();     // int reg_column

      console.log(`Grid信息 - Slice数量: ${sliceCount}, 行: ${regRow}, 列: ${regColumn}`);

      if (!pTable || pTable.isNull()) {
        console.log('Grid表为空');
        return groundItems;
      }

      // 获取玩家当前位置
      const parent = this.pointer.add(8).readPointer();
      const playerPos = parent.add(0x30);
      const playerX = playerPos.readFloat();
      const playerY = playerPos.add(4).readFloat();
      const playerZ = playerPos.add(8).readFloat();

      // 遍历所有slice
      for (let i = 0; i < Math.min(sliceCount, 1000); i++) {
        try {
          // 每个slice的大小是0x28字节
          const slicePtr = pTable.add(i * 0x28);

          // 读取slice信息
          const flag = slicePtr.add(0x4).readU32();           // int flag
          const matterListHead = slicePtr.add(0x10).readPointer(); // gobject *matter_list

          // 如果这个slice没有matter，跳过
          if (!matterListHead || matterListHead.isNull()) {
            continue;
          }

          // 遍历matter链表
          let currentMatter = matterListHead;
          let matterCount = 0;

          while (currentMatter && !currentMatter.isNull() && matterCount < 100) {
            try {
              // 检查是否为zombie状态
              const bZombie = currentMatter.add(0xD).readU8();
              if (!bZombie) {
                // 获取matter位置
                const matterX = currentMatter.add(0x30).readFloat();
                const matterY = currentMatter.add(0x34).readFloat();
                const matterZ = currentMatter.add(0x38).readFloat();

                // 计算距离
                const dx = matterX - playerX;
                const dy = matterY - playerY;
                const dz = matterZ - playerZ;
                const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);

                // 获取XID信息
                const xidType = currentMatter.add(0x3C).readU32();
                const xidId = currentMatter.add(0x40).readU32();
                const tag = currentMatter.add(0x24).readU32();

                groundItems.push({
                  sliceIndex: i,
                  xid: { type: xidType, id: xidId },
                  tag: tag,
                  position: { x: matterX, y: matterY, z: matterZ },
                  distance: distance
                });
              }

              // 移动到下一个matter (通过pNext指针，偏移0x18)
              currentMatter = currentMatter.add(0x18).readPointer();
              matterCount++;

            } catch (error) {
              break; // 如果读取出错，跳出链表遍历
            }
          }

        } catch (error) {
          continue; // 跳过这个slice
        }
      }

      console.log(`通过Grid系统找到${groundItems.length}个地面物品`);

      // 按距离排序
      groundItems.sort((a, b) => a.distance - b.distance);

      return groundItems;

    } catch (error) {
      console.log('Grid遍历地面物品时出错:', error);
      return groundItems;
    }
  }

  // 获取附近物品的另一种方法：通过玩家的视野范围
  getItemsInVision() {
    this.ensurePointer();
    const items = [];

    try {
      // 获取world指针
      const worldPtr = this.pointer.add(0x4).readPointer();
      if (!worldPtr || worldPtr.isNull()) {
        console.log('无法获取world指针');
        return items;
      }

      // 读取视野相关信息
      const nearVision = worldPtr.add(0xA84).readU32();  // w_near_vision
      const farVision = worldPtr.add(0xA88).readU32();   // w_far_vision
      const trueVision = worldPtr.add(0xA8C).readU32();  // w_true_vision
      const vision = worldPtr.add(0xA98).readFloat();    // w_vision

      console.log(`视野信息 - Near: ${nearVision}, Far: ${farVision}, True: ${trueVision}, Vision: ${vision}`);

      return items;

    } catch (error) {
      console.log('获取视野物品时出错:', error);
      return items;
    }
  }

  transportTo(worldId, x, y, z) {
    this.ensurePointer();
    // 创建位置向量
    const pos = Memory.alloc(12);  // A3DVECTOR结构
    pos.writeFloat(x);             // X坐标
    pos.add(4).writeFloat(y);      // Y坐标
    pos.add(8).writeFloat(z);      // Z坐标

    // 获取PlayerTaskInterface指针
    const taskInterface = this.pointer.add(0x10).readPointer();  // _commander偏移

    // 调用PlayerTaskInterface::TransportTo函数
    const transportToFunc = new NativeFunction(
      ptr('0x08997E34'), // 更新后的函数地址
      'void',
      ['pointer', 'uint32', 'pointer', 'uint32']
    );

    // 调用传送函数
    transportToFunc(
      taskInterface,  // PlayerTaskInterface指针
      worldId,        // 目标世界ID
      pos,            // 位置向量
      0               // 额外参数
    );

    console.log(`[Transport] 传送到世界${worldId}, 坐标(${x}, ${y}, ${z})`);
    return true;
  }



  //测试接口 出售版本应该删除


  InsertSkill(skillId, level = 1) {
    const oi = Memory.alloc(8);
    oi.writePointer(this.pointer);
    oi.add(4).writeInt(0);
    // 获取 SkillWrapper
    const skillWrapper = this.pointer.add(0xA4).readPointer();
    // 检查技能插件是否已注册
    const checkAddonFunc = new NativeFunction(ptr("0x08798AC6"), "pointer", []);  //addon_manager::GetInstance
    const addonManager = checkAddonFunc();
    // 创建技能学习插件数据
    const addonData = Memory.alloc(12); // addon_data 结构
    addonData.writeU32(0); // type
    addonData.add(4).writeU32(skillId); // skill_id
    addonData.add(8).writeU32(level); // skill_level
    // 创建技能学习插件
    const createAddonFunc = new NativeFunction(ptr("0x089B8ADA"), "int32", [
      "pointer",
      "pointer",
      "pointer",
      "pointer",
      "pointer",
    ]);
    // 调用插件的Activate函数
    const result = createAddonFunc(
      addonManager, // this
      addonData, // addon_data
      ptr(0), // item_body (null)
      this.pointer, // gactive_imp
      ptr(0) // parent item (null)
    );
    // 同步到客户端
    const runner = this.pointer.add(0x10).readPointer();
    const vtable = runner.readPointer();
    // 同步技能
    const syncSkillFunc = new NativeFunction(
      vtable.add(0x424).readPointer(),
      "void",
      ["pointer", "int32", "int32"]
    );
    syncSkillFunc(runner, skillId, level);
    // console.log(`[InsertSkill] 添加技能成功 ID:${skillId}, 等级:${level}`);
    return result;
  }

  InsertSkillPermament(skillId, level) {
    this.ensurePointer();
    // 创建 object_interface 实例
    const oi = Memory.alloc(8);
    oi.writePointer(this.pointer);
    oi.add(4).writeInt(0);
    // 获取 SkillWrapper 实例
    const skillWrapperPtr = this.pointer.add(0xA4);
    const skillWrapper = new SkillWrapper(skillWrapperPtr);
    // 调用 InsertSkillPermament
    const result = skillWrapper.InsertSkillPermament(skillId, level, oi);
    // 同步到客户端
    const runner = this.pointer.add(0x10).readPointer();
    if (!runner.isNull()) {
      const vtable = runner.readPointer();
      const syncSkillFunc = new NativeFunction(
        vtable.add(0x424).readPointer(),
        "void",
        ["pointer", "int32", "int32"]
      );
      syncSkillFunc(runner, skillId, level);
    }
    console.log(
      `[InsertSkillPermament] 永久添加技能${result === 0 ? "成功" : "失败"
      } ID:${skillId}, 等级:${level}`
    );
    return result === 0;
  }

  // 通过门派名直接加满该门派所有技能
  learnSkillByClass(schoolName, isPermanent = false) {
    // 先获取门派技能ID
    let skillIds = [];
    if (schoolName === '师徒技能') {
      skillIds = SkillStubBase.prototype.getSkillsByOccupation.call(SkillStubBase.prototype, 147);
    } else if (schoolName === '夫妻技能') {
      skillIds = SkillStubBase.prototype.getSkillsByOccupation.call(SkillStubBase.prototype, 152);
    } else if (schoolName === '通用技能') {
      skillIds = SkillStubBase.prototype.getSkillsByOccupation.call(SkillStubBase.prototype, 166);
    } else if (schoolName === '家族技能') {
      skillIds = SkillStubBase.prototype.getSkillsByOccupation.call(SkillStubBase.prototype, 131);
    } else {
      // 门派技能
      skillIds = SkillStubBase.prototype.printSchoolAllSkills.call(SkillStubBase.prototype, schoolName) || [];
      // 追加通用技能
      const masterSkills = SkillStubBase.prototype.getSkillsByOccupation.call(SkillStubBase.prototype, 147) || [];
      const coupleSkills = SkillStubBase.prototype.getSkillsByOccupation.call(SkillStubBase.prototype, 152) || [];
      const commonSkills = SkillStubBase.prototype.getSkillsByOccupation.call(SkillStubBase.prototype, 166) || [];
      const familySkills = SkillStubBase.prototype.getSkillsByOccupation.call(SkillStubBase.prototype, 131) || [];
      skillIds = [...new Set([...skillIds, ...masterSkills, ...coupleSkills, ...commonSkills, ...familySkills])];
    }
    if (!skillIds || skillIds.length === 0) {
      console.log(`[learnSkillByClass] 未找到门派/通用技能: ${schoolName}`);
      return false;
    }
    let count = 0;
    for (const skillId of skillIds) {
      const stub = new SkillStubBase(skillId);
      const maxLevel = stub.getMaxLearn();
      if (maxLevel > 0) {
        if (isPermanent) {
          this.InsertSkillPermament(skillId, maxLevel);
        } else {
          this.InsertSkill(skillId, maxLevel);
        }
        count++;
      }
    }
    console.log(`[learnSkillByClass] ${schoolName} 加满技能完成，共处理 ${count} 个技能！`);
    return true;
  }

  learnSchoolSkills() {
    let stub = new SkillStubBase();
    let skillIds = stub.getSkillsByClass(15);
    for (const skillId of skillIds) {
      const skillStub = new SkillStubBase(skillId);
      const maxLevel = skillStub.getMaxLearn();
      this.InsertSkill(skillId, maxLevel);
    }
  }


  // 修改各项声望  1青云贡献 2天音贡献   9是仙 10是魔 11是佛
  ModifyRegionReputation(idx, offset) {
    const modifyRegionReputationFunc = new NativeFunction(
      ptr('0x0863B15A'),
      'void', [
      'pointer', 'int32', 'int32']);
    modifyRegionReputationFunc(
      this.pointer,
      idx,
      offset);
  }

  SetCultivation(value) {   //1是仙 2是魔 3是仙魔 4是佛 5是仙佛 6是魔佛 7是仙魔佛
    // 调用原始的SetCultivation函数
    const setCultivationFunc = new NativeFunction(ptr('0x087824F8'), 'void', ['pointer', 'int32']);
    setCultivationFunc(this.pointer, value);
    const runner = this.pointer.add(0x10).readPointer();
    const vtable = runner.readPointer();
    const notifyFunc = new NativeFunction(vtable.add(0x518).readPointer(), 'void', ['pointer', 'int32']);
    notifyFunc(runner, value & 0xFF);
  }




  GetCurTargetID() {
    const oi = Memory.alloc(8);
    oi.writePointer(this.pointer);
    oi.add(4).writeInt(0);
    const getCurTargetIDFunc = new NativeFunction(
      ptr("0x087605BA"), // object_interface::GetCurTargetID
      "pointer",
      ["pointer"]
    );
    const xidPtr = getCurTargetIDFunc(oi);
    return xidPtr.add(4).readU32();
  }

  GetTargetTemplateId() {
    const result = this.QueryObject(2, null, 0x48);
    if (!result.success) return null;
    const templateId = result.objectInfo.add(44).readInt();
    return templateId;
  }


  QueryObject(type, targetId, requiredSize = 0x48) {
    targetId = this.GetCurTargetID();
    const oi = Memory.alloc(8);
    oi.writePointer(this.pointer);
    oi.add(4).writeInt(0);
    const xid = Memory.alloc(8);
    xid.writeInt(type); // type: 1=玩家, 2=NPC
    xid.add(4).writeInt(targetId);
    const objectInfo = Memory.alloc(requiredSize);
    const worldPtr = this.pointer.add(4).readPointer();
    const queryObjectFunc = new NativeFunction(
      ptr("0x0859A7EE"), //world::QueryObject
      "int32",
      ["pointer", "pointer", "pointer", "uint8"]
    );
    const result = queryObjectFunc(worldPtr, xid, objectInfo, 0);
    if (result > 0) {
      return {
        success: true,
        xid,
        objectInfo,
        id: xid.add(4).readInt(),
        type: xid.readInt(),
      };
    }
  }

  GetTargetPos() {
    const targetId = this.GetCurTargetID();
    const type = this.GetTargetType();
    let result;
    if (type === 1) {
      result = this.QueryObject(1, targetId, 0x48);
    } else {
      result = this.QueryObject(2, targetId, 0x48);
    }
    if (result && result.success) {
      const pos = {
        x: result.objectInfo.add(8).readFloat(),
        y: result.objectInfo.add(12).readFloat(),
        z: result.objectInfo.add(16).readFloat(),
      };

      return pos;
    }
    return null;
  }


  summonNPCFormation(
    baseNpcId,
    duration,
    count,
    formation = "circle",
    npcIds = null
  ) {
    this.ensurePointer();
    // 创建object_interface实例
    const oi = Memory.alloc(8);
    oi.writePointer(this.pointer);
    oi.add(4).writeInt(0);
    const parent = this.pointer.add(8).readPointer();
    const playerPos = parent.add(0x30);
    const targetPos = this.GetTargetPos();

    let spawnX, spawnY, spawnZ;
    if (targetPos && targetPos.z >= 0) {
      spawnX = targetPos.x;
      spawnY = targetPos.y;
      spawnZ = targetPos.z;
    } else {
      spawnX = playerPos.readFloat();
      spawnY = playerPos.add(4).readFloat();
      spawnZ = playerPos.add(8).readFloat();
    }
    const positions = this.calculateFormationPositions(
      formation,
      spawnX,
      spawnY,
      spawnZ,
      count
    );
    const createNPCFunc = new NativeFunction(
      ptr("0x087644AA"), // object_interface::CreateNPC
      "void",
      ["pointer", "pointer", "pointer"]
    );

    for (let i = 0; i < positions.length; i++) {
      const pos = Memory.alloc(12);
      pos.writeFloat(positions[i].x);
      pos.add(4).writeFloat(positions[i].y);
      pos.add(8).writeFloat(positions[i].z);

      const param = Memory.alloc(12);
      const npcId = npcIds ? npcIds[i % npcIds.length] : baseNpcId;
      param.writeInt(npcId);
      param.add(4).writeInt(0);
      param.add(8).writeInt(duration);

      const dx = positions[i].x - spawnX;
      const dz = positions[i].z - spawnZ;
      let angle = Math.atan2(dz, dx);

      let direction = Math.floor((angle / (2 * Math.PI)) * 256);
      direction = (direction + 128) % 256;
      if (direction < 0) direction += 256;
      param.add(4).writeU8(direction);
      createNPCFunc(oi, pos, param);
      Thread.sleep(0.001);
    }
    return true;
  }

  // 计算阵型位置
  calculateFormationPositions(formation, playerX, playerY, playerZ, npcCount) {
    const positions = [];
    const baseRadius = 15.0;
    switch (formation.toLowerCase()) {
      case "circle":
        // 圆形阵
        for (let i = 0; i < npcCount; i++) {
          const angle = (2 * Math.PI * i) / npcCount;
          const pos = {
            x: playerX + baseRadius * Math.cos(angle),
            y: playerY,
            z: playerZ + baseRadius * Math.sin(angle),
          };
          positions.push(pos);
        }
        break;

      case "callnpc":
        // 圆形阵参数调整
        const targetNpcCount = 20; // 目标NPC数量（原54）
        const compactRadius = baseRadius * 0.6; // 半径缩小为原来的60%（可调整）

        for (let i = 0; i < targetNpcCount; i++) {
          const angle = (2 * Math.PI * i) / targetNpcCount; // 角度间隔自动适应数量
          const pos = {
            x: playerX + compactRadius * Math.cos(angle),
            y: playerY,
            z: playerZ + compactRadius * Math.sin(angle),
          };
          positions.push(pos);
        }
        break;

      case "square":
        // 方形阵
        const side = Math.ceil(Math.sqrt(npcCount));
        const spacing = (baseRadius * 2) / side;
        for (let i = 0; i < npcCount; i++) {
          const row = Math.floor(i / side);
          const col = i % side;
          positions.push({
            x: playerX + spacing * (col - side / 2),
            y: playerY,
            z: playerZ + spacing * (row - side / 2),
          });
        }
        break;
      case "helix":
        // 螺旋上升阵型
        const heightRange = 50.0; // 上升高度
        for (let i = 0; i < npcCount; i++) {
          const angle = (6 * Math.PI * i) / npcCount;
          const height = (heightRange * i) / npcCount;
          positions.push({
            x: playerX + baseRadius * Math.cos(angle),
            y: playerY + height,
            z: playerZ + baseRadius * Math.sin(angle),
          });
        }
        break;
      case "staircase":
        // 阶梯上升阵型（更高版）
        const stepHeight = 1.0; // 每级台阶的高度（增加到5.0）
        const stepWidth = 5.0; // 每级台阶的水平间距
        const totalSteps = npcCount; // 台阶数等于NPC数量
        for (let i = 0; i < npcCount; i++) {
          const stepLevel = i; // 当前台阶级别
          positions.push({
            x: playerX + stepWidth * stepLevel,
            y: playerY + stepHeight * stepLevel,
            z: playerZ, // z轴保持不变，模拟直线楼梯
          });
        }
        break;
      case "sphere":
        // 球形阵型 - 位于玩家头顶
        const phi = Math.PI * (3 - Math.sqrt(5)); // 黄金角
        const heightOffset = 5.0; // 在玩家头顶上方的基础高度
        for (let i = 0; i < npcCount; i++) {
          const y = 1 - (i / (npcCount - 1)) * 2;
          const radius = Math.sqrt(1 - y * y);
          const theta = phi * i;
          positions.push({
            x: playerX + baseRadius * radius * Math.cos(theta),
            y: playerY + heightOffset + baseRadius * y, // 添加基础高度偏移
            z: playerZ + baseRadius * radius * Math.sin(theta),
          });
        }
        break;
      case "phoenix":
        // 凤凰阵型（展翅飞翔形状）
        const wingSpan = 80.0; // 翅膀跨度
        const bodyHeight = 50.0; // 身体高度
        const featherDensity = 0.2; // 羽毛密度
        const tailLength = 30.0; // 尾部长度
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const wingIdx = i % 2 === 0 ? 1 : -1; // 左右翅膀
          const angle = Math.PI * t * featherDensity; // 羽毛角度
          const isBody = t < 0.3; // 前 30% 为身体
          const x = isBody ? 0 : wingSpan * Math.sin(angle) * wingIdx * (1 - t);
          const y =
            bodyHeight * t +
            (isBody ? 0 : tailLength * Math.cos(angle) * (1 - t));
          positions.push({
            x: playerX + x,
            y: playerY + y,
            z:
              playerZ +
              (isBody ? 0 : tailLength * (1 - t) * Math.sin(t * Math.PI)),
          });
        }
        break;
      case "nebula":
        // 星云阵型（不规则扩散云团）
        const cloudRadius = 60.0; // 云团半径
        const densityFactor = 0.5; // 密度因子
        const chaosFactor = 0.4; // 混沌扰动
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const angle =
            ((4 * Math.PI * i) / npcCount) * (1 + Math.random() * chaosFactor); // 随机角度
          const radius =
            cloudRadius *
            Math.pow(t, densityFactor) *
            (1 + Math.random() * chaosFactor); // 指数分布半径
          const height = cloudRadius * (Math.random() - 0.5) * 2 * (1 - t); // 高度随机分布
          positions.push({
            x: playerX + radius * Math.cos(angle),
            y: playerY + height,
            z: playerZ + radius * Math.sin(angle),
          });
        }
        break;
      case "wave":
        // 波浪阵型
        const wave1Length = 2 * Math.PI;
        const amplitude = 5.0;
        for (let i = 0; i < npcCount; i++) {
          const x = (baseRadius * 2 * i) / npcCount - baseRadius;
          const phase = (i * wave1Length) / npcCount;
          positions.push({
            x: playerX + x,
            y: playerY + amplitude * Math.sin(phase),
            z: playerZ + amplitude * Math.cos(phase),
          });
        }
        break;
      case "dna":
        // DNA双螺旋阵型
        const dnaHeight = 50.0;
        const dnaRadius = baseRadius * 0.5;
        for (let i = 0; i < npcCount; i++) {
          const height = (dnaHeight * i) / npcCount;
          const angle = (6 * Math.PI * i) / npcCount;
          // 第一条螺旋
          if (i % 2 === 0) {
            positions.push({
              x: playerX + dnaRadius * Math.cos(angle),
              y: playerY + height,
              z: playerZ + dnaRadius * Math.sin(angle),
            });
          }
          // 第二条螺旋（相位差π）
          else {
            positions.push({
              x: playerX + dnaRadius * Math.cos(angle + Math.PI),
              y: playerY + height,
              z: playerZ + dnaRadius * Math.sin(angle + Math.PI),
            });
          }
        }
        break;
      case "heart":
        // 心形阵
        for (let i = 0; i < npcCount; i++) {
          const t = (2 * Math.PI * i) / npcCount;
          const x = 16 * Math.pow(Math.sin(t), 3);
          const z =
            13 * Math.cos(t) -
            5 * Math.cos(2 * t) -
            2 * Math.cos(3 * t) -
            Math.cos(4 * t);
          positions.push({
            x: playerX + baseRadius * 0.3 * x,
            y: playerY,
            z: playerZ + baseRadius * 0.3 * z,
          });
        }
        break;
      case "butterfly":
        // 蝴蝶形阵
        for (let i = 0; i < npcCount; i++) {
          const t = (2 * Math.PI * i) / npcCount;
          const x =
            Math.sin(t) *
            (Math.exp(Math.cos(t)) -
              2 * Math.cos(4 * t) -
              Math.pow(Math.sin(t / 12), 5));
          const z =
            Math.cos(t) *
            (Math.exp(Math.cos(t)) -
              2 * Math.cos(4 * t) -
              Math.pow(Math.sin(t / 12), 5));
          positions.push({
            x: playerX + baseRadius * 0.3 * x,
            y: playerY,
            z: playerZ + baseRadius * 0.3 * z,
          });
        }
        break;
      case "yin-yang":
        // 阴阳形阵
        for (let i = 0; i < npcCount; i++) {
          const t = (2 * Math.PI * i) / npcCount;
          let x, z;
          if (i < npcCount / 2) {
            // 阳鱼
            x = Math.cos(t) * (1 + Math.sin(t));
            z = Math.sin(t) * (1 + Math.sin(t));
          } else {
            // 阴鱼
            x = Math.cos(t) * (1 - Math.sin(t));
            z = Math.sin(t) * (1 - Math.sin(t));
          }
          positions.push({
            x: playerX + baseRadius * x,
            y: playerY,
            z: playerZ + baseRadius * z,
          });
        }
        break;
      case "galaxy":
        // 银河系阵型（多层旋转臂）
        const armCount = 4; // 螺旋臂数量
        const galaxyRadius = 50.0; // 最大半径
        const armThickness = 10.0; // 臂厚度
        const twistFactor = 0.3; // 扭曲因子
        const heightSpread = 20.0; // 高度分布范围
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const armIdx = i % armCount; // 螺旋臂索引
          const angle =
            2 * Math.PI * t +
            (armIdx * 2 * Math.PI) / armCount +
            twistFactor * Math.log(1 + t * galaxyRadius);
          const radius = galaxyRadius * t * (1 + Math.random() * 0.2); // 随机扰动半径
          positions.push({
            x: playerX + radius * Math.cos(angle),
            y:
              playerY +
              heightSpread * (Math.sin(angle) + (Math.random() - 0.5) * 0.5), // 高度波动
            z: playerZ + radius * Math.sin(angle),
          });
        }
        break;
      case "galaxy-spiral":
        // 星系螺旋阵
        const galaxyarms = 4; // 螺旋臂数量
        for (let i = 0; i < npcCount; i++) {
          const galaxyarm = i % arms;
          const r = baseRadius * (i / npcCount);
          const angle =
            (2 * Math.PI * i) / (npcCount / arms) +
            (arm * 2 * Math.PI) / arms +
            (r / baseRadius) * Math.PI;
          const spread = 0.3 * r; // 臂的展开程度
          positions.push({
            x: playerX + (r + Math.random() * spread) * Math.cos(angle),
            y: playerY + (Math.random() - 0.5) * spread,
            z: playerZ + (r + Math.random() * spread) * Math.sin(angle),
          });
        }
        break;
      case "spiral":
        // 原有的螺旋形阵保持不变
        const spiralturns = 2;
        for (let i = 0; i < npcCount; i++) {
          const angle = (spiralturns * 2 * Math.PI * i) / npcCount;
          const r = baseRadius * (i / npcCount);
          positions.push({
            x: playerX + r * Math.cos(angle),
            y: playerY,
            z: playerZ + r * Math.sin(angle),
          });
        }
        break;
      case "crystal":
        // 水晶阵型（多面棱柱结构）
        const crystalHeight = 70.0; // 总高度
        const facetCount = 6; // 棱面数量
        const facetRadius = 30.0; // 基础半径
        const jaggedFactor = 0.3; // 锯齿状扰动
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const facetIdx = i % facetCount; // 棱面索引
          const angle =
            (2 * Math.PI * facetIdx) / facetCount + t * Math.PI * jaggedFactor; // 棱面角度
          const radius =
            facetRadius *
            (1 - t * 0.6) *
            (1 + Math.cos(t * Math.PI * facetCount) * jaggedFactor); // 半径变化
          positions.push({
            x: playerX + radius * Math.cos(angle),
            y: playerY + crystalHeight * t,
            z: playerZ + radius * Math.sin(angle),
          });
        }
        break;
      case "cathedral":
        // 大教堂阵型（尖顶塔楼结构）
        const baseHeight = 80.0; // 基础高度
        const towerRadius = 30.0; // 塔底半径
        const spireCount = 8; // 尖顶数量
        const spireHeight = 40.0; // 尖顶高度
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const layer = Math.floor(t * spireCount); // 分层
          const angle =
            (2 * Math.PI * (i % Math.ceil(npcCount / spireCount))) /
            Math.ceil(npcCount / spireCount);
          const radius = towerRadius * (1 - t * 0.7); // 半径随高度缩小
          const height =
            baseHeight * t +
            (layer < spireCount ? spireHeight * Math.sin(angle) : 0); // 尖顶波动
          positions.push({
            x: playerX + radius * Math.cos(angle),
            y: playerY + height,
            z: playerZ + radius * Math.sin(angle),
          });
        }
        break;
      case "vortex":
        // 涡流阵型（旋转漏斗状）
        const vortexHeight = 100.0; // 总高度
        const funnelRadius = 40.0; // 底部半径
        const swirlFactor = 5.0; // 旋转圈数
        const pinchFactor = 0.2; // 顶部收缩因子
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const angle = swirlFactor * 2 * Math.PI * t; // 多圈旋转
          const radius =
            funnelRadius *
            (1 - t * (1 - pinchFactor)) *
            (1 + Math.sin(t * Math.PI) * 0.2); // 半径波动
          positions.push({
            x: playerX + radius * Math.cos(angle),
            y: playerY + vortexHeight * t,
            z: playerZ + radius * Math.sin(angle),
          });
        }
        break;
      case "tree":
        // 巨树阵型（分叉树状结构）
        const trunkHeight = 60.0; // 主干高度
        const branchRadius = 25.0; // 分支半径
        const branchLevels = 4; // 分支层数
        const branchAngle = Math.PI / 6; // 分支角度
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const level = Math.floor(t * branchLevels); // 分支层
          const branchIdx = i % Math.ceil(npcCount / branchLevels); // 分支索引
          const height = trunkHeight * t;
          const angle = branchIdx * branchAngle + (level * Math.PI) / 4; // 分支旋转角度
          const radius =
            level === 0
              ? 0
              : branchRadius * (1 - t / branchLevels) * Math.sin(t * Math.PI); // 分支半径
          positions.push({
            x: playerX + radius * Math.cos(angle),
            y: playerY + height,
            z: playerZ + radius * Math.sin(angle),
          });
        }
        break;
      case "pyramid":
        // 金字塔阵型
        const pyramidLayers = 6;
        let currentNpc = 0;
        for (
          let layer = 0;
          layer < pyramidLayers && currentNpc < npcCount;
          layer++
        ) {
          const layerSize = (pyramidLayers - layer) * 2; // 每层的边长
          const spacing = baseRadius / pyramidLayers; // NPC间距
          const height = layer * 5; // 层高

          for (let x = 0; x < layerSize && currentNpc < npcCount; x++) {
            for (let z = 0; z < layerSize && currentNpc < npcCount; z++) {
              // 只在金字塔边缘放置NPC
              if (
                x === 0 ||
                x === layerSize - 1 ||
                z === 0 ||
                z === layerSize - 1
              ) {
                positions.push({
                  x: playerX + (x - layerSize / 2) * spacing,
                  y: playerY + height,
                  z: playerZ + (z - layerSize / 2) * spacing,
                });
                currentNpc++;
              }
            }
          }
        }
        break;
      case "castle":
        // 城堡阵型
        const towerHeight = 30; // 塔高
        const towerCount = 4; // 塔数量
        const wallLength = baseRadius * 1.5; // 城墙长度

        // 添加四个角塔
        for (let tower = 0; tower < towerCount; tower++) {
          const angle = (tower * Math.PI) / 2;
          for (let height = 0; height < 6; height++) {
            positions.push({
              x: playerX + wallLength * Math.cos(angle),
              y: playerY + height * 5,
              z: playerZ + wallLength * Math.sin(angle),
            });
          }
        }
        // 添加城墙
        for (let wall = 0; wall < 4; wall++) {
          const startAngle = (wall * Math.PI) / 2;
          const endAngle = ((wall + 1) * Math.PI) / 2;
          for (let i = 1; i < 5; i++) {
            const t = i / 5;
            const angle = startAngle + (endAngle - startAngle) * t;
            positions.push({
              x: playerX + wallLength * Math.cos(angle),
              y: playerY + 10, // 城墙高度
              z: playerZ + wallLength * Math.sin(angle),
            });
          }
        }
        break;
      case "text":
        // 文字阵型
        const letters = {
          V: [
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [0, 1, 0, 1, 0],
            [0, 0, 1, 0, 0],
          ],
          5: [
            [1, 1, 1, 1, 1],
            [1, 0, 0, 0, 0],
            [1, 1, 1, 1, 1],
            [0, 0, 0, 0, 1],
            [1, 1, 1, 1, 1],
          ],
          8: [
            [1, 1, 1, 1, 1],
            [1, 0, 0, 0, 1],
            [1, 1, 1, 1, 1],
            [1, 0, 0, 0, 1],
            [1, 1, 1, 1, 1],
          ],
          7: [
            [1, 1, 1, 1, 1],
            [0, 0, 0, 0, 1],
            [0, 0, 0, 1, 0],
            [0, 0, 1, 0, 0],
            [0, 1, 0, 0, 0],
          ],
        };
        const text = "V587"; // 要显示的文字
        const letterSpacing = 7; // 字母间距，从7增加到12
        const dotSpacing = 4; // 点阵间距，从4增加到8
        let currentX = 0;
        const textheightOffset = 1; // 增加整体高度偏移

        for (let char of text) {
          if (char in letters) {
            const pattern = letters[char];
            for (let row = 0; row < pattern.length; row++) {
              for (let col = 0; col < pattern[row].length; col++) {
                if (pattern[row][col]) {
                  positions.push({
                    x: playerX + currentX + col * dotSpacing,
                    y:
                      playerY +
                      textheightOffset +
                      (pattern.length - row) * dotSpacing,
                    z: playerZ,
                  });
                }
              }
            }
            currentX += letterSpacing * dotSpacing;
          }
        }
        break;
      case "love":
        // 竖立 "LOVE" 阵型
        const loveScale = 6.0;
        const loveDepth = 2.0;
        const loveLetters = {
          L: [
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 1, 1, 1, 1],
          ],
          O: [
            [0, 1, 1, 1, 0],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [0, 1, 1, 1, 0],
          ],
          V: [
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [0, 1, 0, 1, 0],
            [0, 0, 1, 0, 0],
            [0, 0, 1, 0, 0],
          ],
          E: [
            [1, 1, 1, 1, 1],
            [1, 0, 0, 0, 0],
            [1, 1, 1, 1, 0],
            [1, 0, 0, 0, 0],
            [1, 1, 1, 1, 1],
          ],
        };
        const loveSequence = "LOVE";
        const loveWidth = 5.5;
        let loveNpcIndex = 0;

        for (
          let charIdx = 0;
          charIdx < loveSequence.length && loveNpcIndex < npcCount;
          charIdx++
        ) {
          const letter = loveLetters[loveSequence[charIdx]];
          if (!letter) continue;

          for (let row = 0; row < 5 && loveNpcIndex < npcCount; row++) {
            for (let col = 0; col < 5 && loveNpcIndex < npcCount; col++) {
              if (letter[row][col] === 1) {
                positions.push({
                  x: playerX + (col + charIdx * loveWidth) * loveScale,
                  y: playerY + (4 - row) * loveScale,
                  z: playerZ + loveDepth,
                });
                loveNpcIndex++;
              }
            }
          }
        }
        break;
      case "torus":
        // 环面（甜甜圈形）阵型
        const torusRadius = 30.0; // 主环半径
        const tubeRadius = 10.0; // 管半径
        const torusSegments = Math.ceil(npcCount / 4); // 主环上的分段数
        for (let i = 0; i < npcCount; i++) {
          const theta = (2 * Math.PI * (i % torusSegments)) / torusSegments; // 主环角度
          const phi = (2 * Math.PI * Math.floor(i / torusSegments)) / 4; // 管角度
          positions.push({
            x:
              playerX +
              (torusRadius + tubeRadius * Math.cos(phi)) * Math.cos(theta),
            y: playerY + tubeRadius * Math.sin(phi),
            z:
              playerZ +
              (torusRadius + tubeRadius * Math.cos(phi)) * Math.sin(theta),
          });
        }
        break;
      case "waveplus":
        // 波动阵型（正弦波分布）
        const waveAmplitude = 15.0; // 波幅
        const waveFrequency = 0.5; // 频率
        const waveLength = 40.0; // 波长
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const xOffset = waveLength * t - waveLength / 2; // X 轴分布
          positions.push({
            x: playerX + xOffset,
            y: playerY + waveAmplitude * Math.sin(waveFrequency * xOffset),
            z: playerZ + waveAmplitude * Math.cos(waveFrequency * xOffset),
          });
        }
        break;
      case "starburst":
        // 星爆阵型（向外辐射的星形）
        const starRadius = 25.0; // 基础半径
        const spikeCount = 5; // 尖刺数量
        const heightVariation = 10.0; // 高度变化
        for (let i = 0; i < npcCount; i++) {
          const angle = (2 * Math.PI * i) / npcCount;
          const radiusMod =
            i % spikeCount < spikeCount / 2 ? starRadius : starRadius * 1.5; // 交替半径
          positions.push({
            x: playerX + radiusMod * Math.cos(angle),
            y: playerY + heightVariation * Math.sin(angle * spikeCount), // 高度随角度变化
            z: playerZ + radiusMod * Math.sin(angle),
          });
        }
        break;
      case "cone":
        // 圆锥阵型（底部宽，顶部窄）
        const coneHeight = 60.0; // 圆锥高度
        const baseWidth = 20.0; // 底部半径
        const taperFactor = 0.1; // 顶部收缩因子
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化高度参数
          const angle = (4 * Math.PI * i) / npcCount; // 多圈旋转
          const radius = baseWidth * (1 - t * (1 - taperFactor)); // 半径随高度缩小
          positions.push({
            x: playerX + radius * Math.cos(angle),
            y: playerY + coneHeight * t,
            z: playerZ + radius * Math.sin(angle),
          });
        }
        break;
      case "gridsphere":
        // 网格球形阵型（球面上的网格分布）
        const gridRadius = 35.0; // 球半径
        const latCount = Math.ceil(Math.sqrt(npcCount)); // 纬度分割数
        const lonCount = Math.ceil(npcCount / latCount); // 经度分割数
        for (let i = 0; i < npcCount; i++) {
          const latIdx = Math.floor(i / lonCount); // 纬度索引
          const lonIdx = i % lonCount; // 经度索引
          const phi = (Math.PI * latIdx) / latCount; // 纬度角度
          const theta = (2 * Math.PI * lonIdx) / lonCount; // 经度角度
          const radius = gridRadius * Math.sin(phi); // 水平半径
          positions.push({
            x: playerX + radius * Math.cos(theta),
            y: playerY + gridRadius * Math.cos(phi),
            z: playerZ + radius * Math.sin(theta),
          });
        }
        break;
      case "doublehelix":
        // 双螺旋阵型（两条交错的螺旋）
        const helixRadius = 20.0; // 螺旋半径
        const helixHeight = 80.0; // 总高度
        const turns = 3; // 螺旋圈数
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const angle = turns * 2 * Math.PI * t; // 旋转角度
          const strand = i % 2 === 0 ? 1 : -1; // 交错的双螺旋
          positions.push({
            x: playerX + helixRadius * Math.cos(angle) * strand,
            y: playerY + helixHeight * t,
            z: playerZ + helixRadius * Math.sin(angle),
          });
        }
        break;
      default:
        // 默认使用圆形阵
        return this.calculateFormationPositions(
          "circle",
          playerX,
          playerY,
          playerZ,
          npcCount
        );
    }
    return positions;
  }

}

module.exports = GPlayerImp;

}).call(this)}).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})

},{"../base/CPlusClass":5,"./PlayerWrapper":8,"./SkillStubBase":9}],8:[function(require,module,exports){
const CPlusClass = require('../base/CPlusClass');
const GPlayerImp = require('./GPlayerImp');

// 直接在 PlayerWrapper 中实现 getNativeFunction
const functionMap = new Map();

const getNativeFunction = (symbolOrAddress, ret, args) => {
    if (functionMap.has(symbolOrAddress)) {
        return functionMap.get(symbolOrAddress);
    }

    let address;
    if (symbolOrAddress.startsWith('0x')) {
        address = ptr(symbolOrAddress);
    } else {
        address = Module.getExportByName(null, symbolOrAddress);
    }

    const f = new NativeFunction(address, ret, args);
    functionMap.set(symbolOrAddress, f);
    return f;
};

class PlayerWrapper extends CPlusClass {
    constructor(pointer) {
        super(pointer);
    }

    // 基础属性设置方法
    setProbability(probability) {
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetProbabilityEf', 'void', ['pointer', 'float'])(this.pointer, probability);
    }

    setTime(time) {
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetTimeEf', 'void', ['pointer', 'float'])(this.pointer, time);
    }

    setBuffid(buffid) {
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetBuffidEi', 'void', ['pointer', 'int32'])(this.pointer, buffid);
    }

    setRatio(ratio) {
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetRatioEf', 'void', ['pointer', 'float'])(this.pointer, ratio);
    }

    setAmount(amount) {
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetAmountEf', 'void', ['pointer', 'float'])(this.pointer, amount);
    }

    setValue(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetValueEf', 'void', ['pointer', 'float'])(this.pointer, value);
    }
    SetPerform(inform) {
        const fc = getNativeFunction('_ZN4GNET13PlayerWrapper10SetPerformEi', 'int32', ['pointer', 'float']);
        fc(this.pointer, inform);
    }

    SetPray(inform) {
        const fc = getNativeFunction('_ZN4GNET13PlayerWrapper7SetPrayEi', 'int32', ['pointer', 'float']);
        fc(this.pointer, inform);
    }

    GetTmplid() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper9GetTmplidEv', 'int', ['pointer'])(this.pointer);
    }

    GetHide() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetHideEv', 'int', ['pointer'])(this.pointer);
    }

    GetSglevel() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper10GetSglevelEv', 'int', ['pointer'])(this.pointer);
    }

    GetMaxatk() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper9GetMaxatkEv', 'int', ['pointer'])(this.pointer);
    }

    GetSaint() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper8GetSaintEv', 'int', ['pointer'])(this.pointer);
    }

    GetSkilllevel(id) {
        return getNativeFunction('_ZN4GNET13PlayerWrapper13GetSkilllevelEi', 'int32', ['pointer', 'int32'])(this.pointer, id);
    }

    GetRage() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetRageEv', 'int32', ['pointer'])(this.pointer);
    }

    GetIscrit() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper9GetIscritEv', 'int32', ['pointer'])(this.pointer);
    }

    GetHasmultbuff(buffId) {
        return getNativeFunction('_ZN4GNET13PlayerWrapper14GetHasmultbuffEi', 'bool', ['pointer', 'int32'])(this.pointer, buffId);
    }

    GetHp() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper5GetHpEv', 'int32', ['pointer'])(this.pointer);
    }
    SetHp(hp) {
        getNativeFunction('_ZN4GNET13PlayerWrapper5SetHpEf', 'void', ['pointer', 'float'])(this.pointer, hp);
    }
    GetMp() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper5GetMpEv', 'int32', ['pointer'])(this.pointer);
    }
    GetMaxmp() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper8GetMaxmpEv', 'int32', ['pointer'])(this.pointer);
    }
    GetMaxhp() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper8GetMaxhpEv', 'int32', ['pointer'])(this.pointer);
    }
    SetMp(mp) {
        getNativeFunction('_ZN4GNET13PlayerWrapper5SetMpEf', 'void', ['pointer', 'float'])(this.pointer, mp);
    }
    GetOccupation() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper13GetOccupationEv', 'int32', ['pointer'])(this.pointer);
    }
    GetRes1() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetRes1Ev', 'int32', ['pointer'])(this.pointer);
    }
    GetRes2() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetRes2Ev', 'int32', ['pointer'])(this.pointer);
    }
    GetRes3() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetRes3Ev', 'int32', ['pointer'])(this.pointer);
    }
    GetRes4() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetRes4Ev', 'int32', ['pointer'])(this.pointer);
    }
    GetRes5() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetRes5Ev', 'int32', ['pointer'])(this.pointer);
    }
    GetRes6() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetRes6Ev', 'int32', ['pointer'])(this.pointer);
    }
    SetResisProf(idx, v) {
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetResisProfEii', 'void', ['pointer', 'int32', 'int32'])(this.pointer, idx, v);
    }
    GetVar1() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetVar1Ev', 'int32', ['pointer'])(this.pointer);
    }
    GetVar2() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetVar2Ev', 'int32', ['pointer'])(this.pointer);
    }
    GetVar3() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetVar3Ev', 'int32', ['pointer'])(this.pointer);
    }
    GetVar4() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetVar4Ev', 'int32', ['pointer'])(this.pointer);
    }
    GetVar5() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetVar5Ev', 'int32', ['pointer'])(this.pointer);
    }
    GetVar6() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetVar6Ev', 'int32', ['pointer'])(this.pointer);
    }
    GetVar7() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetVar7Ev', 'int32', ['pointer'])(this.pointer);
    }
    GetVar8() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetVar8Ev', 'int32', ['pointer'])(this.pointer);
    }
    GetVar9() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetVar9Ev', 'int32', ['pointer'])(this.pointer);
    }
    GetVar10() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetVar10Ev', 'int32', ['pointer'])(this.pointer);
    }
    GetVar11() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetVar11Ev', 'int32', ['pointer'])(this.pointer);
    }
    GetVar16() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetVar16Ev', 'int32', ['pointer'])(this.pointer);
    }
    GetDef() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper6GetDefEv', 'int', ['pointer'])(this.pointer);
    }
    GetLevel() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper8GetLevelEv', 'int', ['pointer'])(this.pointer);
    }
    GetBuffcnt() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper10GetBuffcntEv', 'int', ['pointer'])(this.pointer);
    }
    GetDebuffcnt() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper12GetDebuffcntEv', 'int', ['pointer'])(this.pointer);
    }
    GetBuffLevel(buffid) {
        return getNativeFunction('_ZN4GNET13PlayerWrapper11GetBuffLevelEi', 'int', ['pointer', 'int'])(this.pointer, buffid);
    }
    GetHasbuff(buffid) {
        return getNativeFunction('_ZN4GNET13PlayerWrapper10GetHasbuffEi', 'int', ['pointer', 'int'])(this.pointer, buffid);
    }
    GetCrithurt() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper11GetCrithurtEv', 'float', ['pointer'])(this.pointer);
    }
    GetCrit() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetCritEv', 'float', ['pointer'])(this.pointer);
    }
    SetBuffid(buffid) {
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetBuffidEi', 'void', ['pointer', 'int'])(this.pointer, buffid);
    }
    SetVar(value, index) {
        getNativeFunction('_ZN4GNET13PlayerWrapper6SetVarEij', 'void', ['pointer', 'int32', 'int32'])(this.pointer, value, index);
    }
    SetVar1(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetVar1Ei', 'void', ['pointer', 'int32'])(this.pointer, value);
    }
    SetVar2(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetVar2Ei', 'void', ['pointer', 'int32'])(this.pointer, value);
    }
    SetVar3(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetVar3Ei', 'void', ['pointer', 'int32'])(this.pointer, value);
    }
    SetVar4(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetVar4Ei', 'void', ['pointer', 'int32'])(this.pointer, value);
    }
    SetVar5(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetVar5Ei', 'void', ['pointer', 'int32'])(this.pointer, value);
    }
    SetVar6(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetVar6Ei', 'void', ['pointer', 'int32'])(this.pointer, value);
    }
    SetVar7(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetVar7Ei', 'void', ['pointer', 'int32'])(this.pointer, value);
    }
    SetVar8(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetVar8Ei', 'void', ['pointer', 'int32'])(this.pointer, value);
    }
    SetVar9(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetVar9Ei', 'void', ['pointer', 'int32'])(this.pointer, value);
    }
    SetVar10(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetVar10Ei', 'void', ['pointer', 'int32'])(this.pointer, value);
    }
    SetVar11(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetVar11Ei', 'void', ['pointer', 'int32'])(this.pointer, value);
    }
    SetVar16(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetVar16Ei', 'void', ['pointer', 'int32'])(this.pointer, value);
    }

    SetGuilin(probability, time, value, ratio, buffid) {
        this.setProbability(probability);
        this.setTime(time);
        this.setValue(value);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetGuilinEb', 'void', ['pointer', 'int32'])(this.pointer, 1);
    }

    SetHupo(probability, time, value, ratio, buffid) {
        this.setProbability(probability);
        this.setTime(time);
        this.setValue(value);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetHupoEb', 'void', ['pointer', 'int32'])(this.pointer, 1);
    }

    SetEquipeffect(probability, time, value, amount, buffid) {
        this.setProbability(probability);
        this.setTime(time);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetEquipeffectEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSwift(probability, time, value, ratio,) {
        this.setProbability(probability);
        this.setTime(time);
        this.setValue(value);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetSwiftEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetRandcurse(probability, time, value, ratio) {
        this.setProbability(probability);
        this.setTime(time);
        this.setValue(value);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetRandcurseEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetGuhuo(probability, time, amount, value) {
        this.setProbability(probability);
        this.setTime(time);
        this.setAmount(amount);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetGuhuoEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSalvation(ratio, value) {
        this.setRatio(ratio);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetSalvationEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAddattack(probability, time, value, buffid) {
        this.setProbability(probability);
        this.setTime(time);
        this.setValue(value);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetAddattackEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetRepel(probability, value) {
        this.setProbability(probability);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetRepelEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAoEImmune(probability, time) {
        this.setProbability(probability);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetAoEImmuneEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIncskilldodge(probability, time, ratio, buffid) {
        this.setProbability(probability);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetIncskilldodgeEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }
    // 致盲
    SetDarkness(probability, time) {
        this.setProbability(probability);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetDarknessEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetGuishen1(probability, time, ratio, amount, buffid) { // 添加默认值1给guishen1
        this.setProbability(probability);
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper17SetGuishen1debuffEb', 'void', ['pointer', 'float'])(this.pointer, 1);
    }

    // 神力爆发·仙
    SetMiracleburstxian(probability, time, ratio, buffid) {
        this.setBuffid(buffid);
        this.setProbability(probability);
        this.setTime(time);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper19SetMiracleburstxianEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 神力爆发·佛
    SetMiracleburstfo(probability, time, ratio, buffid) {
        this.setBuffid(buffid);
        this.setProbability(probability);
        this.setTime(time);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper17SetMiracleburstfoEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 神力爆发·魔
    SetMiracleburstmo(probability, time, ratio, buffid) {
        this.setBuffid(buffid);
        this.setProbability(probability);
        this.setTime(time);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper17SetMiracleburstmoEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetFamilyinchp(probability, time, value, ratio, buffid) {
        this.setProbability(probability);
        this.setTime(time);
        this.setValue(value);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetFamilyinchpEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetFamilyincmp(probability, time, value, ratio, buffid) {
        this.setProbability(probability);
        this.setTime(time);
        this.setValue(value);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetFamilyincmpEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetFamilyincattack(probability, time, value, ratio, buffid) {
        this.setProbability(probability);
        this.setTime(time);
        this.setValue(value);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper18SetFamilyincattackEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetClearbuff(probability, amount) {
        this.setProbability(probability);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetClearbuffEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetFiring2(probability, time, ratio, amount, buffid) {
        this.setProbability(probability);
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetFiring2Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetHpleak(probability, time, value, amount, buffid) {
        this.setProbability(probability);
        this.setTime(time);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetHpleakEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetBarehanded(probability, time) {
        this.setProbability(probability);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetBarehandedEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetTurnbuff(probability, time, buffid) {
        this.setProbability(probability);
        this.setTime(time);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetTurnbuffEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSummon(probability, time, value, amount, ratio) {
        this.setProbability(probability);
        this.setTime(time);
        this.setValue(value);
        this.setAmount(amount);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetSummonEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetPosSummon(time, value, amount) {
        this.setTime(time);
        this.setValue(value);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetPossummonEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSummontrap(time, ratio, value, amount) {
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetSummontrapEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSilent(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetSilentEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetWrap(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetWrapEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDecdamage(prob, time, ratio) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetDecdamageEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }
    SetNingjin(prob, time, ratio) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetNingjinEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetBlessed(value, time) {
        this.setValue(value);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetBlessedEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAvoidDeath(prob, time,amount,value,buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setAmount(amount);
        this.setValue(value);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetAvoidDeathEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSingleTargetImmune(prob, time, ratio) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper21SetSingleTargetImmuneEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSkillmirror(prob, time, ratio) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetSkillmirrorEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDecCultRes(prob, time, ratio, value, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetDecCultResEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    GetCultivation() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper14GetCultivationEv', 'int', ['pointer'])(this.pointer);
    }

    GetStandrange() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper13GetStandrangeEv', 'float', ['pointer'])(this.pointer);
    }

    GetDarklightform() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper16GetDarklightformEv', 'int', ['pointer'])(this.pointer);
    }

    GetSleepRes() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper11GetSleepResEv', 'int', ['pointer'])(this.pointer);
    }

    SetDarklight(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetDarklightEi', 'void', ['pointer', 'int'])(this.pointer, value);
    }

    SetRejectdebuff(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetRejectdebuffEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    GetPuppetid() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper11GetPuppetidEv', 'int', ['pointer'])(this.pointer);
    }

    SetTriggerskill(prob, time, amount, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setAmount(amount);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetTriggerskillEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetBloodthirsty(prob, time, ratio, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetBloodthirsty2Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetRandbuff(prob, time, ratio, value, amount, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetRandbuffEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetImmunesleep(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetImmunesleepEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetImmunedizzy(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetImmunedizzyEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 免疫定身
    SetImmunewrap(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetImmunewrapEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetImmuneslow(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetImmuneslowEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetImmuneWeak(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetImmuneweakEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }
    // 免疫魅惑
    SetImmunesilent(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetImmunesilentEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetImmuneturn(prob, time, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetImmuneturnEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetEagleEye(time, amount) {
        this.setTime(time);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetEagleEyeEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetClearcooldown(ratio, value, amount) {
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetClearcooldownEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSecondattack(value, ratio) {
        this.setValue(value);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetSecondattackEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetBepulled(prob, time, value, amount) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetBepulledEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    //增强嗑药效果
    SetIncdrugeffect(prob, time, ratio, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetIncdrugeffectEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSubantisleep(time, value) {
        this.setTime(time);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetSubantisleepEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSilenttimer(prob, time, value, ratio, amount, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetSilenttimerEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetCleardebuff(prob, amount, buffid) {
        this.setProbability(prob);
        this.setAmount(amount);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetCleardebuffEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetFrozenImmune(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetFrozenImmuneEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIncMobDmg(prob, time, ratio) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetIncMobDmgEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetMagicshield(time, ratio, amount) {
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetMagicshieldEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetMpgen(prob, time, value, amount, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetMpgenEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDecdefence(prob, time, ratio, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetDecdefenceEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetHpleak4(prob, time, value, amount, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetHpleak4Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDizzy(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetDizzyEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIncAttack(prob, time, ratio, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetIncattackEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetRecoverhp(time, value) {
        this.setTime(time);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetRecoverhpEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIncSkillAccu(prob, time, ratio, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetIncskillaccuEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIncMp(prob, time, ratio, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetIncmpEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIncHp(time, ratio, buffId) {
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetInchpEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    GetTargetType() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper13GetTargetTypeEv', 'int', ['pointer'])(this.pointer);
    }

    SetRage(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetRageEi', 'void', ['pointer', 'int32'])(this.pointer, value);
    }

    SetMakecrit(time) {
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetMakecritEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAddAnti(prob, time, value, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetAddantiEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSummonNpc(prob, time, value, amount) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetSummonnpcEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAddAttack(prob, time, value, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetAddattackEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }


    SetIncSkillDodge(prob, time, ratio, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetIncskilldodgeEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetRecoverHp(time, value) {
        this.setTime(time);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetRecoverhpEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetClearDeBuff(prob, amount) {
        this.setProbability(prob);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetCleardebuffEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetClearSlow(prob) {
        this.setProbability(prob);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetClearslowEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetClearWrap(prob) {
        this.setProbability(prob);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetClearwrapEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSingleAtkChange(prob, time, ratio, amount) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper18SetSingleatkchangeEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDeccritrate(prob, time, ratio, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetDeccritrateEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIncCritRate(time, ratio) {
        this.setTime(time);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetInccritrateEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIncCritHurt(prob, time, ratio) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetInccrithurtEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDiet(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetDietEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetNingJin(prob, time, ratio) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetNingjinEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetCrazy(time, ratio, amount, value) {
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetCrazyEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIncSkillDamage(prob, time, ratio, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper17SetIncskilldamageEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDietImmune(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetDietImmuneEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetImmunetaune(prob, time, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetImmunetauneEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetGoback(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetGobackEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetGFrosty(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetGFrostyEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDsleep(prob, time, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetDsleepEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetFlamecurse(prob, time, ratio, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetFlamecurseEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIncFatalRatio(prob, time, ratio, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetIncfatalratioEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetEvilaura(time, ratio, amount, value) {
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetEvilauraEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // attributes: thunk
    SetHolyaura(time, ratio, amount, value) {
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetHolyauraEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDecSkillDodge(prob, time, ratio, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetDecskilldodgeEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAddSpeed(prob, time, value, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetAddspeedEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIncCultRes(prob, time, ratio, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetIncCultResEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetImmuneSilent(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetImmunesilentEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetImmuneWrap(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetImmunewrapEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetImmuneSlow(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetImmuneslowEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetHeal(prob, value) {
        this.setProbability(prob);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetHealEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetCibei(prob, time, ratio, value, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetCibeiEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDecDamage(prob, time, ratio) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetDecdamageEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetTurnDeBuff(prob, time, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetTurndebuffEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDelayCast(prob, time, ratio, value, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetDelaycastEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetReduceSkillCD(prob, value, ratio, amount, buffId) {
        this.setProbability(prob);
        this.setValue(value);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetReduceskillcdEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIncHurt(prob, time, ratio) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetInchurtEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }
    SetInchurt2(prob, time, ratio) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetInchurt2Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetFlameBlessing(prob, time, ratio, amount, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetFlameblessingEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSetCooldown(prob, time, ratio, amount, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetSetcooldownEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDecHurt(prob, time, ratio, amount) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetDechurtEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetImitate(prob, time, ratio, amount) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetImitateEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetTargetAgedRewind(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper19SetTargetAgedRewindEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetShadowHide(prob, time, value, ratio, amount) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setRatio(ratio);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetShadowhideEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDecskilldodge(prob, time, ratio, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetDecskilldodgeEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIncfatalhurt(prob, time, ratio, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetIncfatalhurtEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDispel(prob, value, ratio, amount) {
        this.setProbability(prob);
        this.setValue(value);
        this.setRatio(ratio);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetDispelEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAoShi(prob, time, value, ratio, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetAoshiEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetChiHun(prob, time, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetChihunEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetBuffClearImmune(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper18SetBuffClearImmuneEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDarkuniform(prob, time, ratio, amount, value, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setValue(value);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetDarkuniformEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetLightuniform(prob, time, ratio, amount, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetLightuniformEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetImmunemanaburn(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper17SetImmunemanaburnEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDelaycast(prob, time, ratio, value, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetDelaycastEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSkillreflect2(prob, time, ratio, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetSkillreflect2Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetMirrorimage(prob, time, amount, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setAmount(amount);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetMirrorimageEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDirecthurt(prob, value) {
        this.setProbability(prob);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetDirecthurtEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetFiring(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetFiringEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetHpRegain(prob, time, value, ratio, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetHpregainEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetFrenzied(prob, time, value, ratio, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetFrenziedEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetRetort(prob, time, ratio, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetRetortEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }


    SetInchp2(prob, time, ratio, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetInchp2Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    //按百分比减免暴击率
    SetDecfatalratio(prob, time, ratio, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetDecfatalratioEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    //按百分比减免暴击伤害
    SetDecfatalhurt(prob, time, ratio, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetDecfatalhurtEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDecskilldamage(time, ratio, buffid) {
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper17SetDecskilldamageEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSubhp(prob, time, value, amount, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetSubhpEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAddHp(time, value, buffid) {
        this.setTime(time);
        this.setValue(value);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetAddhpEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAddMp(time, value, buffid) {
        this.setTime(time);
        this.setValue(value);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetAddmpEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSubmp(prob, time, value, amount, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetSubmpEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }


    SetCursed(prob, time, value, ratio, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetCursedEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAddAttack2(prob, time, value, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetAddattack2Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDrunk(prob, time, ratio, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetDrunkEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIncDefence(prob, time, ratio, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetIncdefenceEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetGloss(time, buffId) {
        this.setTime(time);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetGlossEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetHpLeak(prob, time, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetHpleakEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 设置创建物品    value 扣物品 ratio 给物品ID
    SetCreateItem(prob, value, ratio, amount) {
        this.setProbability(prob);
        this.setValue(value);
        this.setRatio(ratio);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetCreateitemEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // attributes: thunk
    SetIncskillaccu(prop, ratio, time, buffid) {
        this.setProbability(prop);
        this.setRatio(ratio);
        this.setTime(time);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetIncskillaccuEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }
    SetSubDefence(prob, time, value, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetSubdefenceEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAdddodge(prob, time, value, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetAdddodgeEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }
    SetAdddefence(prob, time, value, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetAdddefenceEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetHpgen(prob, time, buffId, amount, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setBuffid(buffId);
        this.setAmount(amount);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetHpgenEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSubattack(prob, time, value, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetSubattackEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetBloodShield(prob, time, ratio, amount, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetBloodshieldEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetInvincible(time) {
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetInvincibleEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIgnoreBlessed(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetIgnoreblessedEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIgnoreInvicible(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper18SetIgnoreinvicibleEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDirectHurt(prob, value) {
        this.setProbability(prob);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetDirecthurtEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }
    //网吧buff
    SetBaradddogeaccu(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper17SetBaradddogeaccuEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // attributes: thunk
    SetExorcism(prob, amount, value, buffid) {
        this.setProbability(prob);
        this.setAmount(amount);
        this.setValue(value);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetExorcismEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetUpdatecommondata(prob, amount, value) {
        this.setProbability(prob);
        this.setAmount(amount);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper19SetUpdatecommondataEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }
    SetSkillReplace(prob, time, skillIdOrin, skillIdReplace, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(skillIdOrin);
        this.setAmount(skillIdReplace);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetSkillreplaceEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    /**
     * 设置召唤分身效果
     * @param  probability - 触发概率(通常为120.0)
     * @param  time - 持续时间(毫秒)
     * @param  ratio - 分身属性比率(通常为1.0-2.0)
     * @param  amount - 分身数量(通常为5.0)
     * @param  value - 分身ID(例如：34666.0为初级影子)
     * @param  buffId - buff类型(通常为0)
     */
    SetSummonClone(probability, time, ratio, amount, value, buffId) {
        this.setProbability(probability);
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setValue(value);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetSummoncloneEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetTransform(prob, time, value, ratio = 1, amount = 1) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setRatio(ratio);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetTransformEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetRecordPos(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetRecordposEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetReturnPos(prob) {
        this.setProbability(prob);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetReturnposEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetParalysis(prob, time, ratio, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetParalysisEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetScopedamage(prob, ratio, value) {
        this.setProbability(prob);
        this.setRatio(ratio);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetScopedamageEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetActiveOnFilterAdd(prob, time, ratio, value, amount, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper20SetActiveonfilteraddEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetActiveCrit(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetActivecritEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetActiveBeCrit(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetActivebecritEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

 
    SetCrippled(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetCrippledEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetGuiShen1(time, ratio, amount, value, buffid) {
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setValue(value);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetGuishen1Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetGuiShen2(time, ratio, amount, value, buffid) {
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setValue(value);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetGuishen2Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }


    SetTuoLing1(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetTuoling1Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }


    SetTuoLing2(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetTuoling2Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetBePulled(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetBepulledEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetPulling(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetPullingEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetBloodThirsty(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetBloodthirstyEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetCycle(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetCycleEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIncSkillLevel(prob, time, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetIncskilllevelEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSetSkillLevel(prob, time, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetSetskilllevelEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAddMonsterExp(prob, value) {
        this.setProbability(prob);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetAddmonsterexpEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetHumanBomb2(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetHumanbomb2Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetTrad(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetTradEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetStruggle(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetStruggleEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetParry(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetParryEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDisguise(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetDisguiseEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSign(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetSignEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetMagicDance(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetMagicdanceEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAtkChangeTarget(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper17SetAtkchangetargetEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAtkChangeTarget2(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper18SetAtkchangetarget2Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetPoJun1(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetPojun1Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetPoJun2(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetPojun2Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSkillTransfer(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetSkilltransferEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetGroupAtkChange(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetGroupatkchangeEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetClearKillNum(prob) {
        this.setProbability(prob);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetClearkillnumEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSmog(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetSmogEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetClearImmune(prob) {
        this.setProbability(prob);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetClearimmuneEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetWhenKillAsSelf(prob) {
        this.setProbability(prob);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetWhenkillasselfEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetWhenKillAsTarget(prob) {
        this.setProbability(prob);
        getNativeFunction('_ZN4GNET13PlayerWrapper18SetWhenkillastargetEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetImmuneTaunt(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetImmunetauntEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetParry2(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetParry2Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetImmuneLakeMp(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetImmunelakempEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSuperTrigger(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetSupertriggerEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAddSeed(prob, value) {
        this.setProbability(prob);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetAddseedEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetHumanBomb(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetHumanbombEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDrainmagic(prob, ratio) {
        this.setProbability(prob);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetDrainmagicEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetWeak(prob, time, ratio, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetWeakEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSlow(prob, time, ratio, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper7SetSlowEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }


    SetTaunt(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetTauntEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetLakeMp(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetLakempEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetTrigger(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetTriggerEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetFilterAdd(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetFilteraddEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetFilterRemove(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetFilterremoveEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }
    RemoveMultiFilter(id) {
        getNativeFunction('_ZN4GNET13PlayerWrapper17RemoveMultiFilterEi', 'void', ['pointer', 'int'])(this.pointer, id);
    }
    SetActiveOnFilterRemove(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper22SetActiveonfilterremoveEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetActiveOnBeCrit(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper17SetActiveonbecritEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetActiveOnCrit(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetActiveoncritEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetActiveOnDodge(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper17SetActiveondodgeEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetActiveOnBeDodged(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper19SetActiveonbedodgedEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetActiveOnKill(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetActiveonkillEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetActiveOnDeath(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper17SetActiveondeathEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetActiveOnHit(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetActiveonhitEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetActiveOnBeHit(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper17SetActiveonbehitEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetActiveOnParry(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper17SetActiveonparryEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetActiveOnBeParried(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper19SetActiveonbeparriedEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetActiveOnCastSkill(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper20SetActiveoncastskillEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetActiveOnBeCastSkill(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper22SetActiveonbecastskillEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }


    SetMpleak(prob, time, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetMpleakEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIncDamage(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetIncdamageEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }
    // 增加格挡率2 - 提升格挡攻击的概率
    SetIncBlock2(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetIncblock2Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加格挡率3 - 提升格挡攻击的概率
    SetIncBlock3(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetIncblock3Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加格挡率4 - 提升格挡攻击的概率
    SetIncBlock4(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetIncblock4Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加格挡率5 - 提升格挡攻击的概率
    SetIncBlock5(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetIncblock5Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加反击率2 - 提升反击攻击的概率
    SetIncCounter2(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetInccounter2Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加反击率3 - 提升反击攻击的概率
    SetIncCounter3(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetInccounter3Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加反击率4 - 提升反击攻击的概率
    SetIncCounter4(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetInccounter4Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加反击率5 - 提升反击攻击的概率
    SetIncCounter5(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetInccounter5Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加生命恢复2 - 提升生命值恢复速度
    SetIncHpgen(time, ratio, buffId) {
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetInchpgenEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加生命恢复2 - 提升生命值恢复速度
    SetIncHpgen2(time, ratio, buffId) {
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetInchpgenEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加生命恢复3 - 提升生命值恢复速度
    SetIncHpgen3(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetInchpgenEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加生命恢复4 - 提升生命值恢复速度
    SetIncHpgen4(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetInchpgenEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加生命恢复5 - 提升生命值恢复速度
    SetIncHpgen5(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetInchpgenEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加魔法恢复 - 提升魔法值恢复速度
    SetIncMpRegen(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetIncmpregenEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加技能范围 - 提升技能的作用范围
    SetIncSkillRange(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetIncskillrangeEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加技能持续时间 - 延长技能效果持续时间
    SetIncSkillDuration(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper19SetIncskilldurationEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIncmountspeed(prob, time, value, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetIncmountspeedEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加经验获取 - 提升获得的经验值
    SetIncExpGain(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetIncexpgainEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加金钱获取 - 提升获得的金钱
    SetIncMoneyGain(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetIncmoneygainEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加掉落率 - 提升物品掉落概率
    SetIncDropRate(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetIncdroproteEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加装备掉落率 - 提升装备掉落概率
    SetIncEquipDropRate(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper19SetIncequipdroproteEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加魔法防御 - 提升魔法防御力
    SetIncMagicDefence(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper18SetIncmagicdefenceEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加物理防御 - 提升物理防御力
    SetIncPhysicalDefence(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper20SetIncphysicaldefenceEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加所有属性 - 提升所有基础属性
    SetIncAllStats(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetIncallstatsEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加力量属性 - 提升力量值
    SetIncStrength(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetIncstrengthEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加敏捷属性 - 提升敏捷值
    SetIncAgility(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetIncagilityEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加智力属性 - 提升智力值
    SetIncIntelligence(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper18SetIncintelligenceEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加体质属性 - 提升体质值
    SetIncConstitution(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper18SetIncconstitutionEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加精神 Increase Spirit
    SetIncSpirit(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetIncSpiritEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加幸运 Increase Luck
    SetIncLuck(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetIncLuckEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加最大生命值 Increase Max HP
    SetIncMaxHp(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetIncMaxHpEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加最大魔法值 Increase Max MP
    SetIncMaxMp(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetIncMaxMpEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加物理防御 Increase Physical Defense
    SetIncPhysicalDefense(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper20SetIncPhysicalDefenseEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加魔法防御 Increase Magic Defense
    SetIncMagicDefense(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper17SetIncMagicDefenseEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加物理攻击 Increase Physical Attack
    SetIncPhysicalAttack(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper19SetIncPhysicalAttackEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加魔法攻击 Increase Magic Attack
    SetIncMagicAttack(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetIncMagicAttackEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加命中率 Increase Hit Rate
    SetIncHitRate(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetIncHitRateEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加闪避率 Increase Dodge Rate
    SetIncDodgeRate(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetIncDodgeRateEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加暴击率 Increase Critical Rate
    SetIncCriticalRate(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper17SetIncCriticalRateEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加暴击伤害 Increase Critical Damage
    SetIncCriticalDamage(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper19SetIncCriticalDamageEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 增加格挡率 Increase Block Rate
    SetIncBlockRate(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetIncBlockRateEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 分散防御 Dispersed Defense
    SetDisperseanti(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetDisperseantiEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 获取暗光 Get Dark Light
    GetDarklight() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper12GetDarklightEv', 'int', ['pointer'])(this.pointer);
    }

    // 发现 Discovery
    SetDiscover(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetDiscoverEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }


    // 疯狂形态 Insanity Form
    SetInsanityform(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetInsanityformEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 眩晕计时器 Dizzy Timer
    SetDizzytimer(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetDizzytimerEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }



    // 伤害移动 Damage Move
    SetDamagemove(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetDamagemoveEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 被动增加攻击 Passive Attack Increase
    SetPasaddattack(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetPasaddattackEf', 'void', ['pointer', 'float'])(this.pointer, value);
    }

    // 获取攻击率 Get Attack Rate
    GetAtkrate() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper10GetAtkrateEv', 'float', ['pointer'])(this.pointer);
    }

    // 独特提示 Unique Prompt
    SetUniqprompt(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper13SetUniqpromptEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }


    // 同心 Synchronization
    SetTongxin(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetTongxinEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 被动增加睡眠 Passive Sleep Increase
    SetPasaddsleep(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetPasaddsleepEf', 'void', ['pointer', 'float'])(this.pointer, value);
    }

    // 与克隆体共享状态 Share State with Clone
    SetSharestatewithclone(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper22SetSharestatewithcloneEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 影舞 Shadow Dance
    SetShadowdance(prob, time, ratio, value, amount) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetShadowdanceEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetInccrithurt(time, ratio, buffId) {
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetInccrithurtEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }
    SetPasincattack(value) {
        return getNativeFunction('_ZN4GNET13PlayerWrapper15SetPasincattackEf', 'void', ['pointer', 'float'])(this.pointer, value);
    }
    SetPasdecfatalhurt(value) {
        return getNativeFunction('_ZN4GNET13PlayerWrapper18SetPasdecfatalhurtEf', 'void', ['pointer', 'float'])(this.pointer, value);
    }

    SetDeccrithurt(time, ratio, buffId) {
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetDeccrithurtEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }
    // 降低技能命中 Decrease Skill Accuracy
    SetDecskillaccu(prob, time, ratio, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetDecskillaccuEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetActivebecrit(prob, time, amount, value, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setAmount(amount);
        this.setValue(value);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetActivebecritEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSlant(prob, time, ratio) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetSlantEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetTorpescence(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetTorpescenceEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    AddCloudShift(prob, time, amount) {
        this.setProbability(prob);
        this.setTime(time);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper13AddCloudShiftEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetWraptimer(prob, time, ratio, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetWraptimerEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSubanti(prob, time, value, buffid) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setBuffid(buffid);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetSubantiEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 元灵状态 Yuanling State
    SetYuanling(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetYuanlingEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 冰盾 Ice Shield
    SetIceshield(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetIceshieldEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 睡眠状态 Sleep State
    SetSleep(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetSleepEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetEloquenceLord(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetEloquenceLordEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSkillreflect(prob, time, ratio, value, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetSkillreflectEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDecanti(time, ratio, buffId) {
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetDecantiEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAddanti(time, value, buffId) {
        this.setTime(time);
        this.setValue(value);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetAddantiEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSetcooldown(prob, time, ratio, amount, value, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        this.setValue(value);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetSetcooldownEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSpeedto(prob, time, value, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetSpeedtoEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAttack2hp(prob, time, ratio) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetAttack2hpEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAttack2mp(prob, time, ratio) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetAttack2mpEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetIgnoreblessed(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetIgnoreblessedEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetFenggong(prob, time, value, amount) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetFenggongEbt', 'void', ['pointer', 'bool', 'int'])(this.pointer, 1);
    }

    AddSpiritSeed(prob, time, amount) {
        this.setProbability(prob);
        this.setTime(time);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper13AddSpiritSeedEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetUltimateDamageReductionDebuff(prob, time, ratio, amount, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper32SetUltimateDamageReductionDebuffEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 循环减少攻击 Cycle Subtract Attack
    SetCycsubattack(prob, time, ratio, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper15SetCycsubattackEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAddSyliaOrb(prob, amount) {
        this.setProbability(prob);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetAddSyliaOrbEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetFiveElems(prob, time, amount, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setAmount(amount);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetFiveElemsEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetAssault2(prob, time, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetAssault2Eb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    GetType() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper7GetTypeEv', 'int', ['pointer'])(this.pointer);
    }

    GetSyliaOrb() {
        return getNativeFunction('_ZN4GNET13PlayerWrapper11GetSyliaOrbEv', 'int', ['pointer'])(this.pointer);
    }

    SetFrozen(prob, time, ratio, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetFrozenEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDpgen(time, value, amount, buffId) {
        this.setTime(time);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper8SetDpgenEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetFlameblessing(prob, time, ratio, amount, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setValue(value);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetFlameblessingEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetSkilltransfer(prob, time, ratio, amount) {
        this.setProbability(prob);
        this.setTime(time);
        this.setRatio(ratio);
        this.setAmount(amount);
        getNativeFunction('_ZN4GNET13PlayerWrapper16SetSkilltransferEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDpleak(prob, time, value, amount, buffId) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        this.setAmount(amount);
        this.setBuffid(buffId);
        getNativeFunction('_ZN4GNET13PlayerWrapper9SetDpleakEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 石化
    SetPetrify(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper10SetPetrifyEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    SetDisableCrit(prob, time, value) {
        this.setProbability(prob);
        this.setTime(time);
        this.setValue(value);
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetDisableCritEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    /**
     * 设置虚弱效果
     */
    SetPowerless(prob, time) {
        this.setProbability(prob);
        this.setTime(time);
        getNativeFunction('_ZN4GNET13PlayerWrapper12SetPowerlessEb', 'void', ['pointer', 'bool'])(this.pointer, 1);
    }

    // 被动专区
    SetPasincmp(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper11SetPasincmpEf', 'void', ['pointer', 'float'])(this.pointer, value);
    }

    SetPasaddsmite(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper14SetPasaddsmiteEf', 'void', ['pointer', 'float'])(this.pointer, value);
    }

    SetPasincskillaccu(value) {
        getNativeFunction('_ZN4GNET13PlayerWrapper18SetPasincskillaccuEf', 'void', ['pointer', 'float'])(this.pointer, value);
    }
}

module.exports = PlayerWrapper;

},{"../base/CPlusClass":5,"./GPlayerImp":7}],9:[function(require,module,exports){
"use strict";

class SkillStubBase {
  // 使用静态Map来存储所有已hook的技能的范围参数
  static hookedSkills = new Map();
  // 标记是否已经初始化了全局hook
  static isHookInitialized = false;

  static _getStubFunc = null;
  static _getStateSizeFunc = null;
  static _getIdFunc = null;
  static _getStateFunc = null;

  static getStubFunc() {
    if (!this._getStubFunc) {
      const getStubExport =
        Process.findModuleByName("libskill.so").findExportByName(
          "_ZN4GNET9SkillStub7GetStubEj"
        ) || ptr("0x0184FC9A");
      this._getStubFunc = new NativeFunction(getStubExport, "pointer", [
        "uint32",
      ]);
    }
    return this._getStubFunc;
  }

  static getStateSizeFunc() {
    if (!this._getStateSizeFunc) {
      const getStateSizeExport = Process.findModuleByName(
        "libskill.so"
      ).findExportByName("_ZNK4GNET9SkillStub12GetStateSizeEv");
      this._getStateSizeFunc = new NativeFunction(getStateSizeExport, "int", [
        "pointer",
      ]);
    }
    return this._getStateSizeFunc;
  }

  static getIdFunc() {
    if (!this._getIdFunc) {
      const getIdExport = Process.findModuleByName(
        "libskill.so"
      ).findExportByName("_ZNK4GNET5Skill5GetIdEv");
      if (getIdExport) {
        this._getIdFunc = new NativeFunction(getIdExport, "int", ["pointer"]);
      }
    }
    return this._getIdFunc;
  }

  static getStateFunc() {
    if (!this._getStateFunc) {
      const getStateExport = Process.findModuleByName(
        "libskill.so"
      ).findExportByName("_ZNK4GNET5Skill8GetStateEv");
      if (getStateExport) {
        this._getStateFunc = new NativeFunction(getStateExport, "int", ["pointer"]);
      }
    }
    return this._getStateFunc;
  }

  constructor(skillId) {
    this.skillId = skillId;
    this.stubIndex = undefined;
    this._stubPtr = null; // 缓存stub指针
  }

  // 优化的stub获取方法，带缓存
  _getStubPtr() {
    if (!this._stubPtr) {
      this._stubPtr = this.getStub();
    }
    return this._stubPtr;
  }

  // 通用的内存读取方法
  _readInt(offset) {
    return this._getStubPtr().add(offset).readInt();
  }

  _writeInt(offset, value) {
    this._getStubPtr().add(offset).writeInt(value);
  }

  _readU8(offset) {
    return this._getStubPtr().add(offset).readU8();
  }

  _writeU8(offset, value) {
    this._getStubPtr().add(offset).writeU8(value);
  }

  _readFloat(offset) {
    return this._getStubPtr().add(offset).readFloat();
  }

  _writeFloat(offset, value) {
    this._getStubPtr().add(offset).writeFloat(value);
  }

  getStub() {
    if (this.stubIndex) {
      return this.stubIndex;
    }
    const stub = SkillStubBase.getStubFunc()(this.skillId);
    this.stubIndex = stub;
    return stub;
  }

  getStateSize() {
    return SkillStubBase.getStateSizeFunc()(this.getStub());
  }

  // 基础属性读写 - 优化版本
  // 00000004 id              dd ?
  getID() {
    return this._readInt(4);
  }

  getId() {
    return this._readInt(4);
  }

  setId(value) {
    this._writeInt(4, value);
  }

  // 00000008 rank            dd ?
  getRank() {
    return this._readInt(8);
  }

  // 0000000C baseid          dd ?
  getBaseId() {
    return this._readInt(0x0C);
  }

  // 00000010 basesp          dd ?
  getBaseSp() {
    return this._readInt(0x10);
  }

  // 00000014 occupation      dd ?
  getOccupation() {
    return this._readInt(0x14);
  }

  setOccupation(occupation) {
    this._writeInt(0x14, occupation);
  }

  // 00000018 maxlevel        dd ?
  getMaxLevel() {
    return this._readInt(0x18);
  }

  setMaxLevel(level) {
    this._writeInt(0x18, level);
  }

  // 0000001C maxlearn        dd ?
  getMaxLearn() {
    return this._readInt(0x1C);
  }

  setMaxLearn(level) {
    this._writeInt(0x1C, level);
  }

  // 00000020 eventflag       dd ?
  getEventFlag() {
    return this._readInt(0x20);
  }

  // 00000024 spcost          dd ?
  getSpCost() {
    return this._readInt(0x24);
  }

  setSpCost(value) {
    this._writeInt(0x24, value);
  }

  // 00000028 reborncount     dd ?
  getRebornCount() {
    return this._readInt(0x28);
  }

  setRebornCount(value) {
    this._writeInt(0x28, value);
  }

  // 技能类别 1.主动攻击，2.主动祝福，3.主动诅咒，4.物品技能，5.被动，6.武器附加, 7.生产，8.瞬移
  // 0000002C type            db ?
  getType() {
    return this._readU8(0x2C);
  }

  setType(type) {
    this._writeU8(0x2C, type);
  }

  // 0000002D timetype        db ?
  getTimeType() {
    return this._readU8(0x2D);
  }

  setTimeType(value) {
    this._writeU8(0x2D, value);
  }

  // 0000002E targettype      db ?
  getTargetType() {
    return this._readU8(0x2E);
  }

  setTargetType(type) {
    this._writeU8(0x2E, type);
  }

  // 技能攻击范围：0点 1线 2自身球 3目标球 4圆锥形 5自身, 6, 地面无目标
  // 0000002F rangetype       db ?
  getRangeType() {
    return this._readU8(0x2F);
  }

  setRangeType(type) {
    this._writeU8(0x2F, type);
  }

  // 00000030 doenchant       db ?
  getDoEnchant() {
    return this.getStub().add(0x30).readU8();
  }

  setDoEnchant(enchant) {
    this.getStub().add(0x30).writeU8(enchant);
  }

  // 00000031 dobless         db ?
  getDoBless() {
    return this.getStub().add(0x31).readU8();
  }

  setDoBless(bless) {
    this.getStub().add(0x31).writeU8(bless);
  }

  // 00000032 autoattack      db ?
  getAutoAttack() {
    return this.getStub().add(0x32).readU8();
  }

  setAutoAttack(value) {
    this.getStub().add(0x32).writeU8(value);
  }

  // 00000033 allowform       dd ?
  getAllowForm() {
    return this.getStub().add(0x34).readInt();
  }

  setAllowForm(value) {
    this.getStub().add(0x34).writeInt(value);
  }

  // 00000037 credittype      dd ?
  getCreditType() {
    return this.getStub().add(0x38).readInt();
  }

  setCreditType(value) {
    this.getStub().add(0x38).writeInt(value);
  }

  // 0000003B clearmask       dd ?
  getClearMask() {
    return this.getStub().add(0x3C).readInt();
  }

  setClearMask(value) {
    this.getStub().add(0x3C).writeInt(value);
  }

  // 0000003F skill_limit     dd ?
  getSkillLimit() {
    return this.getStub().add(0x40).readInt();
  }

  setSkillLimit(value) {
    this.getStub().add(0x40).writeInt(value);
  }

  // 00000043 skillelemsnum   dd ?
  getSkillElemsNum() {
    return this.getStub().add(0x44).readInt();
  }

  setSkillElemsNum(value) {
    this.getStub().add(0x44).writeInt(value);
  }

  // 00000047 skillelemsfactor dd ?
  getSkillElemsFactor() {
    return this.getStub().add(0x48).readInt();
  }

  setSkillElemsFactor(value) {
    this.getStub().add(0x48).writeInt(value);
  }

  // 0000004B serialskill     dd ?
  getSerialSkill() {
    return this.getStub().add(0x4B).readInt();
  }

  setSerialSkill(skillId) {
    return this.getStub().add(0x4B).writeInt(skillId);
  }

  // 0000004F charge          db ?
  getCharge() {
    return this.getStub().add(0x4F).readU8();
  }

  setCharge(charge) {
    return this.getStub().add(0x4F).writeU8(charge);
  }

  // 00000050 chargedist      dd ?
  getChargeDist() {
    return this.getStub().add(0x50).readFloat();
  }

  setChargeDist(value) {
    this.getStub().add(0x50).writeFloat(value);
  }

  // 00000054 succeedskillcolor dd ?
  getSuccessSkillColor() {
    return this.getStub().add(0x54).readInt();
  }

  setSuccessSkillColor(color) {
    return this.getStub().add(0x54).writeInt(color);
  }

  // 00000058 succeedskillcolornum dd ?
  getSuccessSkillColorNum() {
    return this.getStub().add(0x58).readInt();
  }

  setSuccessSkillColorNum(num) {
    return this.getStub().add(0x58).writeInt(num);
  }

  // 0000005C comboskill      dd ?
  // 00000060 conbuttonskill  dd ?
  // 00000064 conbuttontype   dd ?
  // 00000068 need_item       db ?
  // 00000069 item1_id        dd ?
  // 0000006D item1_num       dd ?
  // 00000071 item2_id        dd ?
  // 00000075 item2_num       dd ?
  // 00000079 item_relation   dd ?
  // 0000007D weapon_limit    dd ?
  getWeaponLimit() {
    return this.getStub().add(0x7D).readInt();
  }

  setWeaponLimit(limit) {
    return this.getStub().add(0x7D).writeInt(limit);
  }

  // 00000081 action          dd 36 dup(?)            ; offset
  // 00000111 nativename      string ?
  // 00000115 icon            string ?
  // 00000119 name            basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > ?
  // 0000011D statestub       vector<GNET::SkillStub::State*,std::allocator<GNET::SkillStub::State*> > ?
  // 00000129 talent          dw 8 dup(?)
  // 00000139 talent_size     dw ?
  // 0000013B talent_type     db ?
  getSkillTalentType() {
    return this.getStub().add(0x13B).readU8();
  }

  // 0000013C use_proficiency db ?
  // 0000013D inc_proficiency dd ?
  // 00000141 preskillnum     dd ?
  getPreSkillNum() {
    return this.getStub().add(0x141).readInt();
  }

  setPreSkillNum(num) {
    return this.getStub().add(0x141).writeInt(num);
  }

  // 00000145 cycle           db ?
  // 00000146 cyclemode       dd ?
  // 0000014A cyclegfx        dd ?                    ; offset
  // 0000014E skill_class     dd ?
  getSkillClass() {
    return this.getStub().add(0x14E).readInt();
  }

  setSkillClass(skillClass) {
    return this.getStub().add(0x14E).writeInt(skillClass);
  }

  // 00000152 guide           db ?
  // 00000153 summon_id       dd ?
  // 00000157 trigger         db ?
  // 00000158 conbutton_sub   dd ?
  // 0000015C isBabySkill     db ?
  // 0000015D castInPreSkill  db ?
  // 0000015E preCastSkillId  dd ?
  // 00000162 darkLightType   db ?
  // 00000163 followskill     dd ?
  // 00000167 targetcnt       dd ?
  getTargetCnt() {
    return this.getStub().add(0x168).readInt();
  }

  setTargetCnt(cnt) {
    return this.getStub().add(0x168).writeInt(cnt);
  }

  // 0000016B excludemonstercnt dd ?
  // 0000016F troupe_skill    dd ?
  // 00000173 dospecial       db ?

  // Hook 技能范围
  hookRadius(newRadius) {
    const funcAddr = Module.findExportByName(
      null,
      `_ZNK4GNET${this.skillId}Stub9GetRadiusEPNS_5SkillE`
    );
    if (!funcAddr) {
      console.log(`[Skill] 未找到技能${this.skillId}的范围函数`);
      return;
    }

    Interceptor.attach(funcAddr, {
      onLeave(retval) {
        retval.replace(newRadius);
      },
    });
  }

  // 重构方法，为不同类型创建特定版本的函数，避免类型错误
  _commonStubReplaceHook(retFunc, funcName, type = 'int', originCall = 0) {
    let len = String(this.skillId).length + 9;
    funcName = `_ZNK4GNET${len}Skill${this.skillId}Stub${funcName.length}${funcName}EPNS_5SkillE`;

    const funcAddr = Module.findExportByName(null, funcName);
    if (!funcAddr) {
      console.log(`[Skill] Failed to find function ${funcName}`);
      return;
    }

    const originFunc = new NativeFunction(funcAddr, /** @type {*} */ (type), ["pointer", "pointer"]);

    Interceptor.replace(
      funcAddr,
      new NativeCallback(
        function (stub, skill) {
          if (originCall === 0) {
            return retFunc(stub, skill);
          } else if (originCall === 3) {
            let ret = originFunc(stub, skill);
            return retFunc(ret, stub, skill);
          }
          return type === 'float' ? 0.0 : 0;
        },
        /** @type {*} */ (type),
        ["pointer", "pointer"]
      )
    );
  }

  hookGetRadiusAfter(func) {
    this._commonStubReplaceHook(func, "GetRadius", 'float', 3);
  }

  hookGetRadiusReplace(func) {
    this._commonStubReplaceHook(func, "GetRadius", 'float', 0);
  }

  hookGetCoverageAfter(func) {
    this._commonStubReplaceHook(func, "GetCoverage", 'int', 3);
  }

  // 初始化全局hook
  static initializeGlobalHooks() {
    if (this.isHookInitialized) {
      return true;
    }

    // Hook Radius
    const GetRadiusExport = Process.findModuleByName(
      "libskill.so"
    ).findExportByName("_ZN4GNET5Skill9GetRadiusEv");
    if (GetRadiusExport) {
      const GetRadius = new NativeFunction(GetRadiusExport, "float", ["pointer"]);
      Interceptor.replace(
        GetRadiusExport,
        new NativeCallback(
          function (skillPtr) {
            try {
              let before = GetRadius(skillPtr);

              // 获取技能ID
              const getIdFunc = Process.findModuleByName(
                "libskill.so"
              ).findExportByName("_ZNK4GNET5Skill5GetIdEv");
              const getId = new NativeFunction(getIdFunc, "int", ["pointer"]);
              const skillId = getId(skillPtr);

              // 获取技能状态
              const getStateFunc = Process.findModuleByName(
                "libskill.so"
              ).findExportByName("_ZNK4GNET5Skill8GetStateEv");
              if (getStateFunc) {
                const getState = new NativeFunction(getStateFunc, "int", [
                  "pointer",
                ]);
                const state = getState(skillPtr);

                // 只在技能初始状态时修改范围
                if (state !== 0) {
                  return before;
                }
              }

              // 检查是否是已hook的技能
              const skillConfig = SkillStubBase.hookedSkills.get(skillId);
              if (skillConfig) {
                return skillConfig.radius;
              }

              return before;
            } catch (e) {
              console.log("[Skill] Error in hookGlobalRadius:", e);
              return GetRadius(skillPtr);
            }
          },
          "float",
          ["pointer"]
        )
      );
    }

    // Hook Coverage
    const GetCoverageExport = Process.findModuleByName(
      "libskill.so"
    ).findExportByName("_ZN4GNET5Skill11GetCoverageEv");
    if (GetCoverageExport) {
      const GetCoverage = new NativeFunction(GetCoverageExport, "int", [
        "pointer",
      ]);
      Interceptor.replace(
        GetCoverageExport,
        new NativeCallback(
          function (skillPtr) {
            try {
              let before = GetCoverage(skillPtr);

              // 获取技能ID
              const getIdFunc = Process.findModuleByName(
                "libskill.so"
              ).findExportByName("_ZNK4GNET5Skill5GetIdEv");
              const getId = new NativeFunction(getIdFunc, "int", ["pointer"]);
              const skillId = getId(skillPtr);

              // 获取技能状态
              const getStateFunc = Process.findModuleByName(
                "libskill.so"
              ).findExportByName("_ZNK4GNET5Skill8GetStateEv");
              if (getStateFunc) {
                const getState = new NativeFunction(getStateFunc, "int", [
                  "pointer",
                ]);
                const state = getState(skillPtr);

                // 只在技能初始状态时修改范围
                if (state !== 0) {
                  return before;
                }
              }

              // 检查是否是已hook的技能
              const skillConfig = SkillStubBase.hookedSkills.get(skillId);
              if (skillConfig) {
                return skillConfig.coverage;
              }

              return before;
            } catch (e) {
              console.log("[Skill] Error in hookGlobalCoverage:", e);
              return GetCoverage(skillPtr);
            }
          },
          "int",
          ["pointer"]
        )
      );
    }

    this.isHookInitialized = true;
    return true;
  }

  // 添加一个简单的方法来设置技能
  setSkill(rangeType = 3, radius = 26.0, coverage = 40) {
    // 确保全局hook已初始化
    SkillStubBase.initializeGlobalHooks();
    // 保存技能参数到静态Map
    SkillStubBase.hookedSkills.set(this.skillId, { radius, coverage });
    this.setRangeType(rangeType);
    return this;
  }

  // 设置为群攻技能的快捷方法
  setAOE(rangeType = 3, radius = 26.0, coverage = 40) {
    return this.setSkill(rangeType, radius, coverage);
  }

  // 设置为单体技能的快捷方法
  setSingle() {
    return this.setSkill(0, 0, 0);
  }

  getName() {
    try {
      const nativeNamePtr = this._getStubPtr().add(0x111);

      // 读取原始字节数据
      let stringData = [];
      let i = 0;
      while (i < 64) {  // 设置合理的最大长度
        const byte = nativeNamePtr.add(i).readU8();
        if (byte === 0x00) break;
        stringData.push(byte);
        i++;
      }

      if (stringData.length === 0) {
        return `技能${this.getId()}_未命名`;
      }

      // 简化的编码处理 - 只尝试最常用的GBK编码
      let result = '';
      for (let i = 0; i < stringData.length; i += 2) {
        if (i + 1 >= stringData.length) break;
        const b1 = stringData[i];
        const b2 = stringData[i + 1];

        // 跳过分隔符
        if (b1 === 0x4E || b1 === 0x0A) continue;

        // 尝试GBK编码
        const c1 = ((b1 & 0x7F) + 0x81) >>> 0;
        const c2 = ((b2 & 0x7F) + 0x40) >>> 0;
        result += String.fromCharCode((c1 << 8) | c2);
      }

      return result || `技能${this.getId()}_解码失败`;

    } catch(e) {
      return `技能${this.getId()}_读取失败`;
    }
  }

  //测试接口 出售版本应该删除
  getAllPossibleSkillIds(maxId = 10000) {
    const skillIds = [];
    const getStubFunc = SkillStubBase.getStubFunc();

    // 直接使用静态函数，避免修改实例状态
    for (let i = 1; i <= maxId; i++) {
      try {
        const stubPtr = getStubFunc(i);
        if (stubPtr && !stubPtr.isNull()) {
          skillIds.push(i);
        }
      } catch (e) {
        // 忽略获取无效stub时的错误
      }
    }

    return skillIds;
  }

  // 职业类别映射表
  static skillClassMap = {
    少侠: -1,
    合欢: 0,
    鬼王: 1,
    青云: 2,
    天音: 3,
    鬼道: 4,
    九黎: 5,
    烈山: 6,
    怀光: 7,
    天华: 8,
    焚香: 9,
    太昊: 10,
    辰皇: 11,
    牵机: 12,
    英招: 13,
    破军: 14,
    青罗: 15,
    归云: 16,
    画影: 17,
  };

  // 职业和转生等级映射表
  static occupationMap = {
    鬼王: { 一重: 1, 二重: 2, 三重: 3, 四重: 13, 五重: 14 },
    合欢: { 一重: 4, 二重: 5, 三重: 6, 四重: 16, 五重: 17 },
    青云: { 一重: 7, 二重: 8, 三重: 9, 四重: 19, 五重: 20 },
    天音: { 一重: 10, 二重: 11, 三重: 12, 四重: 22, 五重: 23 },
    鬼道: { 一重: 25, 二重: 26, 三重: 27, 四重: 28, 五重: 29 },
    焚香: { 一重: 64, 二重: 65, 三重: 66, 四重: 67, 五重: 68 },
    九黎: { 一重: 33, 二重: 34, 三重: 35, 四重: 36, 五重: 37 },
    烈山: { 一重: 39, 二重: 40, 三重: 41, 四重: 42, 五重: 43 },
    怀光: { 一重: 45, 二重: 46, 三重: 47, 四重: 48, 五重: 49 },
    天华: { 一重: 51, 二重: 52, 三重: 53, 四重: 54, 五重: 55 },
    辰皇: { 一重: 56, 二重: 57, 三重: 58, 四重: 59, 五重: 60 },
    太昊: { 一重: 96, 二重: 97, 三重: 98, 四重: 99, 五重: 100 },
    牵机: { 一重: 102, 二重: 103, 三重: 104, 四重: 105, 五重: 106 },
    英招: { 一重: 108, 二重: 109, 三重: 110, 四重: 111, 五重: 112 },
    破军: { 一重: 117, 二重: 118, 三重: 119, 四重: 120, 五重: 121 },
    青罗: { 一重: 71, 二重: 72, 三重: 73, 四重: 74, 五重: 75 },
    归云: { 一重: 77, 二重: 78, 三重: 79, 四重: 80, 五重: 81 },
    画影: { 一重: 83, 二重: 84, 三重: 85, 四重: 86, 五重: 87 },
  };

  getSkillsByOccupation(occupation, maxId = 10000) {
    //147师徒技能  152夫妻技能 166捕捉,生产技能
    let originalName = "";
    if (typeof occupation === "string") {
      const parts = occupation.match(/^(.+?)([一二三四五]重)?$/);
      if (parts) {
        const schoolName = parts[1];
        const rebornLevel = parts[2] || "一重";
        originalName = `${schoolName}${rebornLevel}`;

        if (
          SkillStubBase.occupationMap[schoolName] &&
          SkillStubBase.occupationMap[schoolName][rebornLevel]
        ) {
          occupation = SkillStubBase.occupationMap[schoolName][rebornLevel];
        }
      }
    } else {
      for (const [school, levels] of Object.entries(
        SkillStubBase.occupationMap
      )) {
        for (const [level, id] of Object.entries(levels)) {
          if (id === occupation) {
            originalName = `${school}${level}`;
            break;
          }
        }
        if (originalName) break;
      }
    }

    const skillIds = [];
    for (let i = 1; i <= maxId; i++) {
      const stub = new SkillStubBase(i);
      // const stubPtr = stub.getStub();
      if (stub.getStub() && !stub.getStub().isNull()) {
        const skillOccupation = stub.getOccupation();
        const skillType = stub.getType();
        if (skillOccupation === occupation && skillType !== 1) {
          skillIds.push(i);
        }
      }
    }
    const displayName = originalName || `职业ID ${occupation}`;
    console.log(
      `找到${skillIds.length}个${displayName}的技能：${skillIds.join(",")}`
    );
    return skillIds;
  }

  // 按技能类型过滤
  getSkillsByType(type, maxId = 10000) {
    const skillIds = [];
    const getStubFunc = SkillStubBase.getStubFunc();
    for (let i = 1; i <= maxId; i++) {
      const stubPtr = getStubFunc(i);
      if (stubPtr && !stubPtr.isNull()) {
        const skillType = stubPtr.add(0x2c).readU8();
        if (skillType === type) {
          skillIds.push(i);
        }
      }
    }
    return skillIds;
  }

  // 按天赋类型过滤
  getSkillsByTalentType(hasTalent, maxId = 10000) {
    const skillIds = [];
    for (let i = 1; i <= maxId; i++) {
      const stub = new SkillStubBase(i);
      // const stubPtr = stub.getStub();
      if (stub.getStub() && !stub.getStub().isNull()) {
        const talentType = stub.getSkillTalentType();
        if ((hasTalent && talentType > 0) || (!hasTalent && talentType === 0)) {
          skillIds.push(i);
        }
      }
    }
    console.log(`找到 ${skillIds.length} 个天书技能`);
    console.log(skillIds);
    return skillIds;
  }

  // 按职业类别过滤 (简化版)
  getSkillsByClass(targetClass, maxId = 10000) {
    let originalName = "";
    if (
      typeof targetClass === "string" &&
      SkillStubBase.skillClassMap[targetClass] !== undefined
    ) {
      originalName = targetClass;
      targetClass = SkillStubBase.skillClassMap[targetClass];
    }
    const skillIds = [];
    for (let i = 1; i <= maxId; i++) {
      const tempSkill = new SkillStubBase(i);
      const stubPtr = tempSkill.getStub();
      if (stubPtr && !stubPtr.isNull()) {
        if (tempSkill.getSkillClass() === targetClass) {
          skillIds.push(i);
        }
      }
    }
    const displayName = originalName || `职业ID ${targetClass}`;
    console.log(`找到 ${skillIds.length} 个 ${displayName} 的技能`);
    console.log(skillIds);
    return skillIds;
  }

  /*
        使用示例：
       const stub = new SkillStubBase();
        const skillids = stub.findSkillsByProperties({skillClass: '青云',talenttype: 1}, 10000);
        console.log(skillids);
    */
  findSkillsByProperties(filters = {}, maxId = 10000) {
    const skillIds = [];
    const filterDescriptions = [];
    if (filters.occupation !== undefined)
      filterDescriptions.push(`职业ID=${filters.occupation}`);
    if (filters.type !== undefined)
      filterDescriptions.push(`类型=${filters.type}`);
    if (filters.clearmask !== undefined)
      filterDescriptions.push(`ClearMask=${filters.clearmask}`);
    if (filters.skillClass !== undefined)
      filterDescriptions.push(`职业类别=${filters.skillClass}`);
    if (filters.talenttype !== undefined)
      filterDescriptions.push(`天赋类型=${filters.talenttype}`);
    // console.log(`开始查找技能（模板属性过滤: ${filterDescriptions.join(', ')}）...`);
    for (let i = 1; i <= maxId; i++) {
      const tempSkill = new SkillStubBase(i);
      const stubPtr = tempSkill.getStub();
      if (stubPtr && !stubPtr.isNull()) {
        let matchesFilter = true;
        if (filters.occupation !== undefined) {
          if (tempSkill.getOccupation() !== filters.occupation) {
            matchesFilter = false;
          }
        }
        if (matchesFilter && filters.type !== undefined) {
          if (tempSkill.getType() !== filters.type) {
            matchesFilter = false;
          }
        }
        if (matchesFilter && filters.talenttype !== undefined) {
          const talentType = tempSkill.getSkillTalentType();
          if (
            (filters.talenttype && talentType === 0) ||
            (!filters.talenttype && talentType !== 0)
          ) {
            matchesFilter = false;
          }
        }
        if (matchesFilter && filters.clearmask !== undefined) {
          const clearmask = tempSkill.getClearMask();
          // 假设 filters.clearmask=true 表示需要 clearmask 不为0
          if (
            (filters.clearmask && clearmask === 0) ||
            (!filters.clearmask && clearmask !== 0)
          ) {
            matchesFilter = false;
          }
        }
        if (matchesFilter && filters.skillClass !== undefined) {
          let targetClass = filters.skillClass;
          // 处理中文名称
          if (
            typeof targetClass === "string" &&
            SkillStubBase.skillClassMap[targetClass] !== undefined
          ) {
            targetClass = SkillStubBase.skillClassMap[targetClass];
          }
          if (tempSkill.getSkillClass() !== targetClass) {
            matchesFilter = false;
          }
        }
        if (matchesFilter) {
          skillIds.push(i);
        }
      }
    }
    // console.log(`根据模板属性找到 ${skillIds.length} 个技能`);
    return skillIds;
  }

  // 打印指定门派的所有技能（转生、天赋、造化、封神）
  printSchoolAllSkills(schoolName, maxId = 10000) {
    if (!schoolName || !SkillStubBase.skillClassMap[schoolName]) {
      console.log(`未找到门派: ${schoolName}`);
      return;
    }

    const schoolClassId = SkillStubBase.skillClassMap[schoolName];
    const schoolRebornLevels = SkillStubBase.occupationMap[schoolName];

    if (!schoolRebornLevels) {
      console.log(`未找到门派转生信息: ${schoolName}`);
      return;
    }

    // type含义对照表
    const typeNames = {
      1: "主动攻击",
      2: "主动祝福",
      3: "主动诅咒",
      4: "物品技能",
      5: "被动",
      6: "武器附加",
      7: "生产",
      8: "瞬移",
      11: "被动",
    };

    // rangetype含义对照表
    const rangeTypeNames = {
      0: "点",
      1: "线",
      2: "自身球",
      3: "目标球",
      4: "圆锥形",
      5: "自身",
      6: "地面无目标",
    };

    // 格式化技能信息的函数
    const formatSkillInfo = (skillId) => {
      const stub = new SkillStubBase(skillId);
      const maxLearn = stub.getMaxLearn();
      const type = stub.getType();
      const rangeType = stub.getRangeType();
      const stateSize = stub.getStateSize();
      const stage = stateSize - 1; // 技能段数 = stateSize - 1

      // 如果stage为-1或0（即非多段技能），则不显示stage字段
      if (stage <= 0) {
        return `[id：${skillId}，maxlearn：${maxLearn}，type：${type}，rangetype：${rangeType}]`;
      } else {
        return `[id：${skillId}，maxlearn：${maxLearn}，type：${type}，rangetype：${rangeType}，stage：${stage}]`;
      }
    };

    console.log(`\n===== ${schoolName}门派全部技能 =====`);

    // 先获取天赋技能，避免与门派技能重复
    console.log(`\n[天书技能]`);
    const filters = {
      skillClass: schoolName,
      talenttype: 1,
    };
    const talentSkills = this.findSkillsByProperties(filters, maxId);

    // 过滤掉废弃技能(skillClass = -2)
    const filteredTalentSkills = [];
    for (const skillId of talentSkills) {
      const stub = new SkillStubBase(skillId);
      if (stub.getSkillClass() !== -2) {
        filteredTalentSkills.push(skillId);
      }
    }

    console.log(filteredTalentSkills.map(formatSkillInfo).join(", "));

    // 存储天赋技能为Set，便于快速查找
    const talentSkillSet = new Set(filteredTalentSkills);

    // 1. 打印门派技能
    console.log(`\n[门派技能]`);

    const schoolSkillsMap = {};
    Object.entries(schoolRebornLevels).forEach(([level, occupationId]) => {
      // 使用门派映射表获取技能
      const schoolSkills = [];

      for (let i = 1; i <= maxId; i++) {
        const stub = new SkillStubBase(i);
        try {
          const stubPtr = stub.getStub();
          if (stubPtr && !stubPtr.isNull()) {
            const skillOccupation = stub.getOccupation();
            const skillClass = stub.getSkillClass();
            const talentType = stub.getSkillTalentType();

            // 匹配职业ID，过滤掉废弃技能和天赋技能
            if (
              skillOccupation === occupationId &&
              skillClass !== -2 &&
              talentType !== 1
            ) {
              schoolSkills.push(i);
            }
          }
        } catch (e) {
          // 忽略错误
        }
      }

      console.log(`${level}: ${schoolSkills.map(formatSkillInfo).join(", ")}`);
      schoolSkillsMap[level] = schoolSkills;
    });

    // 3. 造化技能
    const zhaohuaXianSkills = [];
    const zhaohuaFoSkills = [];
    const zhaohuaMoSkills = [];

    for (let i = 1; i <= maxId; i++) {
      const stub = new SkillStubBase(i);
      const stubPtr = stub.getStub();
      if (stubPtr && !stubPtr.isNull()) {
        const creditType = stub.getCreditType();
        const sClass = stub.getSkillClass();

        // 造化技能: CreditType为9、10、11且匹配门派class，且不是废弃技能
        if (sClass === schoolClassId && sClass !== -2) {
          if (creditType === 9) {
            zhaohuaXianSkills.push(i);
          } else if (creditType === 10) {
            zhaohuaMoSkills.push(i);
          } else if (creditType === 11) {
            zhaohuaFoSkills.push(i);
          }
        }
      }
    }

    // 输出各类造化技能
    if (zhaohuaXianSkills.length > 0) {
      console.log(`\n[造化仙]`);
      console.log(zhaohuaXianSkills.map(formatSkillInfo).join(", "));
    }

    if (zhaohuaFoSkills.length > 0) {
      console.log(`\n[造化佛]`);
      console.log(zhaohuaFoSkills.map(formatSkillInfo).join(", "));
    }

    if (zhaohuaMoSkills.length > 0) {
      console.log(`\n[造化魔]`);
      console.log(zhaohuaMoSkills.map(formatSkillInfo).join(", "));
    }

    // 合并所有造化技能用于最终统计
    const zhaohuaSkills = [
      ...zhaohuaXianSkills,
      ...zhaohuaFoSkills,
      ...zhaohuaMoSkills,
    ];

    // 4. 封神技能
    console.log(`\n[封神技能]`);
    const fengshenSkills = [];
    for (let i = 1; i <= maxId; i++) {
      const stub = new SkillStubBase(i);
      const stubPtr = stub.getStub();
      if (stubPtr && !stubPtr.isNull()) {
        const occupationId = stub.getOccupation();
        const sClass = stub.getSkillClass();

        // 画影门派特例：如果class为-2但weaponlimit为18，也视为画影的封神技能
        const isHuayingSpecial =
          schoolName === "画影" &&
          sClass === -2 &&
          stub.getWeaponLimit() === 18;

        // 封神技能: occupation为167、168、169且匹配门派class，且不是废弃技能
        // 或者是画影特例
        if (
          ((occupationId === 167 ||
            occupationId === 168 ||
            occupationId === 169) &&
            sClass === schoolClassId &&
            sClass !== -2) ||
          isHuayingSpecial
        ) {
          fengshenSkills.push(i);
        }
      }
    }
    console.log(fengshenSkills.map(formatSkillInfo).join(", "));

    // 5. 合计所有技能
    const allSkills = new Set([
      ...Object.values(schoolSkillsMap).flat(),
      ...filteredTalentSkills,
      ...zhaohuaSkills,
      ...fengshenSkills,
    ]);

    console.log(`\n[技能汇总] 共${allSkills.size}个`);

    return Array.from(allSkills);
  }
}

module.exports = SkillStubBase;

},{}],10:[function(require,module,exports){
// 升级概率表
const upgradeRates = {
    // level: [升级概率]
    0: 1.001,      // 1级升2级
    1: 1.001,        // 2级升3级
    2: 1.001,       // 3级升4级
    3: 1.001,       // 4级升5级
    4: 1.001,        // 5级升6级
    5: 1.001,       // 6级升7级
    6: 1.001,       // 7级升8级
    7: 1.001,
    8: 1.001,
    9: 1.001,
};

// 修改升级概率的函数
function modifyUpgradeRate(level, rate) {
    const upgrade_table = ptr('0x899EA60');
    const entry = upgrade_table.add(level * 4);  // 每个概率占4字节
    
    entry.writeFloat(rate);   // 写入升级概率
    
    console.log(`等级${level+1}升${level+2}的概率: ${(rate*100).toFixed(1)}%`);
}

// 应用升级概率修改
function applyUpgradeRates() {
    try {
        console.log("[升级概率修改] 开始修改升级概率...");
        Object.entries(upgradeRates).forEach(([level, rate]) => {
            modifyUpgradeRate(parseInt(level), rate);
        });
        console.log("[升级概率修改] 升级概率修改完成");
        return true;
    } catch(e) {
        console.error("[升级概率修改] 修改失败:", e);
        return false;
    }
}


module.exports = {
    applyUpgradeRates,
}; 
},{}],11:[function(require,module,exports){
// 进度条加速配置
const progressBarHooks = [
    // 基础物品操作
    { addr: "0x087522F4", name: "绑定物品执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE18bind_item_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08753418", name: "血量附魔执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE22blood_enchant_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08753224", name: "损坏物品恢复执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE28broken_item_restore_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08754220", name: "改变样式执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE21change_style_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x087544F4", name: "充能祭坛执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE27charge_talestation_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08754E1C", name: "自定义宝石槽执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE28customize_gem_slots_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08752542", name: "销毁绑定物品执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE26destroy_bind_item_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x087527E0", name: "销毁物品恢复执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE29destroy_item_restore_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x087550CE", name: "镶嵌宝石执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE19embed_gems_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },

    // 星座相关
    { addr: "0x08755F74", name: "星座镶嵌", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE36equipment_install_astrology_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08755EA0", name: "星座打孔", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE23equipment_slot_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x0875604E", name: "星座拆除", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE38equipment_uninstall_astrology_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x0875479C", name: "装备升级执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE26equipment_upgrade_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },

    // 宝石相关
    { addr: "0x08755874", name: "提取宝石执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE20extract_gem_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x087548A6", name: "鉴定宝石槽执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE27identify_gem_slots_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x0875210E", name: "炼器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE16install_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x0875304C", name: "锁定物品执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE18lock_item_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },

    // 魔法相关
    { addr: "0x08754306", name: "魔法精炼执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE21magic_refine_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08754406", name: "魔法恢复执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE22magic_restore_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08754070", name: "NPC生产执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE20npc_produce_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08754130", name: "宠物装备精炼执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE24petequip_refine_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x0875618C", name: "生产金法神执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE25produce_jinfashen_exector7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },

    // 宝石槽相关
    { addr: "0x08754B60", name: "重建宝石槽执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE26rebuild_gem_slots_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08755392", name: "移除宝石执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE20remove_gems_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x087545C8", name: "修复损坏物品执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE27repair_damage_item_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08755984", name: "熔炼宝石执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE18smelt_gem_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x0875564C", name: "宝石等级升级执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE25upgrade_gem_level_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08755758", name: "宝石质量升级执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE27upgrade_gem_quality_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },

    // 法宝相关
    { addr: "0x087566FA", name: "法宝合成执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE22amulet_combine_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08756632", name: "法宝镶嵌石头执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE27amulet_embed_stone_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08756458", name: "法宝生成石头执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE26amulet_gen_stone_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08756540", name: "法宝精炼执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE22amulet_refine_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x0875635E", name: "坐骑提速执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE28mount_item_up_speed_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },

    // 灵气相关
    { addr: "0x08753D14", name: "灵气分解执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE25spirit_decompose_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x0875366E", name: "灵气插入执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE22spirit_insert_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08753B4C", name: "灵气能量恢复执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE29spirit_power_restore_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08753906", name: "灵气移除执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE22spirit_remove_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },

    // 解锁、装备转换相关
    { addr: "0x08753138", name: "解锁物品执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE20unlock_item_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08752204", name: "卸载执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE19uninstall_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x087567C2", name: "装备属性转移执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE32transfer_equipment_attr_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x087568B2", name: "装备转换执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE26equipment_convert_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x087569A2", name: "装备星宿转移执行器", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE33equipment_astrum_transfer_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    
    // 玩家相关
    { addr: "0x08625C5C", name: "玩家分解符文", duration: 20, symbol: "_ZZN11gplayer_imp19PlayerDecomposeRuneEiEN2op11GetDurationEv" },
    { addr: "0x086261E0", name: "玩家销毁占星", duration: 20, symbol: "_ZZN11gplayer_imp22PlayerDestroyAstrologyEjiEN2op11GetDurationEv" },
    { addr: "0x08625EA8", name: "玩家擦除符文槽", duration: 20, symbol: "_ZZN11gplayer_imp19PlayerEraseRuneSlotEiiEN2op11GetDurationEv" },
    { addr: "0x08625664", name: "玩家飞行活动位置", duration: 20, symbol: "_ZZN11gplayer_imp20PlayerFlyActivityPosEiEN2op11GetDurationEv" },
    { addr: "0x08625442", name: "任务飞天", duration: 20, symbol: "_ZZN11gplayer_imp16PlayerFlyTaskPosEiiEN2op11GetDurationEv" },
    { addr: "0x08626044", name: "星座鉴定", duration: 20, symbol: "_ZZN11gplayer_imp23PlayerIdentifyAstrologyEjiEN2op11GetDurationEv" },
    { addr: "0x0862593A", name: "玩家鉴定符文", duration: 20, symbol: "_ZZN11gplayer_imp18PlayerIdentifyRuneEiiEN2op11GetDurationEv" },
    { addr: "0x08625F70", name: "玩家镶嵌符文槽", duration: 20, symbol: "_ZZN11gplayer_imp21PlayerInstallRuneSlotEiiiiEN2op11GetDurationEv" },
    { addr: "0x08625D1E", name: "玩家打开符文槽", duration: 20, symbol: "_ZZN11gplayer_imp18PlayerOpenRuneSlotEiEN2op11GetDurationEv" },
    { addr: "0x08625B92", name: "元婴归劫", duration: 20, symbol: "_ZZN11gplayer_imp15PlayerResetRuneEiiEN2op11GetDurationEv" },
    { addr: "0x0862610C", name: "星座升级", duration: 20, symbol: "_ZZN11gplayer_imp22PlayerUpgradeAstrologyEjijiEN2op11GetDurationEv" },

    // 法宝精炼相关
    { addr: "0x08752D4A", name: "法宝合成", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE22talisman_combine_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08755B9C", name: "法宝镶嵌技能", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE26talisman_embedskill_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08752E46", name: "法宝附魔", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE22talisman_enchant_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08755AAC", name: "法宝圣级提升", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE29talisman_holylevelup_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08752B76", name: "法宝等级提升", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE24talisman_lvlup_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08752A88", name: "法宝精炼", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE22talisman_refine_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08752C60", name: "法宝重置", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE22talisman_reset_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08755CAE", name: "法宝技能精炼", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE29talisman_skillrefine_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },
    { addr: "0x08755DAE", name: "法宝技能精炼结果", duration: 20, symbol: "_ZZN18NG_ELEMNET_SERVICE36talisman_skillrefine_result_executor7OnServeEP11gplayer_impRK3XIDRK9A3DVECTORPKvjEN2op11GetDurationEv" },

    // 物品使用相关
    { addr: "0x08893624", name: "战斗旗帜", duration: 20, symbol: "_ZZNK15item_battleflag5OnUseEN4item8LOCATIONEjP11gactive_impPS0_EN2op11GetDurationEv" },
    { addr: "0x088ACC3A", name: "坐骑", duration: 20, symbol: "_ZZNK10item_mount5OnUseEN4item8LOCATIONEjP11gactive_impPS0_EN2op11GetDurationEv" },
    { addr: "0x088CE6B8", name: "星盘", duration: 20, symbol: "_ZZNK16item_telestation15UseTelePositionEP11gplayer_impP4itemPKvjiEN2op11GetDurationEv" },
    { addr: "0x088CF414", name: "城镇卷轴", duration: 20, symbol: "_ZZNK15item_townscroll5OnUseEN4item8LOCATIONEjP11gactive_impPS0_EN2op11GetDurationEv" },
    { addr: "0x088CF6EC", name: "宝藏挖掘计数", duration: 20, symbol: "_ZNK22item_treasure_digcount16OnGetUseDurationEv" },
    { addr: "0x088CF9CE", name: "VIP卡", duration: 20, symbol: "_ZNK12item_vipcard16OnGetUseDurationEv" },
    { addr: "0x088CF556", name: "定点飞天", duration: 20, symbol: "_ZNK21item_townscroll_paper16OnGetUseDurationEv" },
    { addr: "0x08897BC4", name: "经验珠子", duration: 20, symbol: "_ZNK12item_dbl_exp16OnGetUseDurationEv" },
    { addr: "0x088976CE", name: "控制怪物", duration: 20, symbol: "_ZNK16item_control_mob16OnGetUseDurationEv" },
    { addr: "0x088979FE", name: "夫妻传送", duration: 20, symbol: "_ZNK16item_couple_jump16OnGetUseDurationEv" },
    { addr: "0x088CD1D4", name: "传送道具", duration: 20, symbol: "_ZNK13item_teleport16OnGetUseDurationEv" },
    { addr: "0x086262A8", name: "符文合成", duration: 20, symbol: "_ZZN11gplayer_imp18PlayerComposeFuwenEbiEN2op11GetDurationEv" },
    { addr: "0x088BFC88", name: "技能道具", duration: 20, symbol: "_ZNK10item_skill16OnGetUseDurationEv" },
    { addr: "0x088B9544", name: "配方道具", duration: 20, symbol: "_ZNK11item_recipe16OnGetUseDurationEv" },
    { addr: "0x088B763C", name: "PK重置", duration: 20, symbol: "_ZNK13item_pk_reset16OnGetUseDurationEv" },
    { addr: "0x088ADA46", name: "在线奖励礼盒", duration: 20, symbol: "_ZNK24item_onlineaward_giftbox16OnGetUseDurationEv" },
    { addr: "0x088A630A", name: "礼盒套装", duration: 20, symbol: "_ZNK16item_giftbox_set16OnGetUseDurationEv" },
    { addr: "0x088A5546", name: "礼盒配送", duration: 20, symbol: "_ZNK21item_giftbox_delivery16OnGetUseDurationEv" },
    { addr: "0x088A5F8A", name: "礼盒", duration: 20, symbol: "_ZNK12item_giftbox16OnGetUseDurationEv" },
    { addr: "0x088B8E42", name: "神圣药水", duration: 20, symbol: "_ZNK17item_deity_potion16OnGetUseDurationEv" },

    // 新增功能
    { addr: "0x086273F2", name: "轩辕策升星", duration: 20, symbol: "_ZZN11gplayer_imp23TalentScrollStarUpgradeEiEN2op11GetDurationEv" },
    { addr: "0x0862732A", name: "星宿星移除", duration: 20, symbol: "_ZZN11gplayer_imp22StarSystemStarRemoveEiiiEN2op11GetDurationEv" },
    { addr: "0x0862725A", name: "星宿星镶嵌", duration: 20, symbol: "_ZZN11gplayer_imp21StarSystemStarEmbedEiiiEN2op11GetDurationEv" },
    { addr: "0x08627196", name: "星宿槽位重置", duration: 20, symbol: "_ZZN11gplayer_imp22StarSystemSlotsRerollEiiEN2op11GetDurationEv" },
    { addr: "0x086270D8", name: "星宿鉴定", duration: 20, symbol: "_ZZN11gplayer_imp18StarSystemIdentifyEiiiEN2op11GetDurationEv" },
    { addr: "0x08626602", name: "升级轩辕策", duration: 20, symbol: "_ZZN11gplayer_imp26PlayerUpgradeTalentScrollEiEN2op11GetDurationEv" },
    { addr: "0x0862677E", name: "卸载轩辕策", duration: 20, symbol: "_ZZN11gplayer_imp26PlayerUninstallTalentScrollEiiEN2op11GetDurationEv" },
    { addr: "0x08626CCC", name: "转向法宝方向", duration: 20, symbol: "_ZZN11gplayer_imp27PlayerTalismanTurnDirectionEiiiiEN2op11GetDurationEv" },
    { addr: "0x08626BEC", name: "法宝快速精炼", duration: 20, symbol: "_ZZN11gplayer_imp25PlayerTalismanQuickRefineEiEN2op11GetDurationEv" },
    { addr: "0x08626842", name: "重置轩辕策", duration: 20, symbol: "_ZZN11gplayer_imp23PlayerResetTalentScrollEiiEN2op11GetDurationEv" },
    { addr: "0x086266C0", name: "镶嵌轩辕策", duration: 20, symbol: "_ZZN11gplayer_imp25PlayerInstallTalentScrollEiEN2op11GetDurationEv" },
    { addr: "0x08626544", name: "鉴定轩辕策", duration: 20, symbol: "_ZZN11gplayer_imp26PlayerIdentifyTalentScrollEiEN2op11GetDurationEv" },
    { addr: "0x08626F4A", name: "鉴定新装备", duration: 20, symbol: "_ZZN11gplayer_imp26PlayerIdentifyNewEquipmentEiEN2op11GetDurationEv" },
    { addr: "0x0862646C", name: "宠物重生", duration: 20, symbol: "_ZZN11gplayer_imp17PlayerRebornPetEjjEN2op11GetDurationEv" },
    { addr: "0x08627008", name: "修炼装备属性", duration: 20, symbol: "_ZZN11gplayer_imp27PlayerRefineEquipmentAttrEjijiEN2op11GetDurationEv" },
    { addr: "0x08625ACA", name: "符文精炼", duration: 20, symbol: "_ZZN11gplayer_imp17PlayerRefineRuneEiiEN2op11GetDurationEv" },
    { addr: "0x08625A02", name: "符文合成", duration: 20, symbol: "_ZZN11gplayer_imp19PlayerCombineRuneEiiEN2op11GetDurationEv" },
    { addr: "0x08625DE0", name: "改变符文槽", duration: 20, symbol: "_ZZN11gplayer_imp21PlayerChangeRuneSlotEiiEN2op11GetDurationEv" },
    { addr: "0x08625878", name: "检查进入基地", duration: 20, symbol: "_ZZN11gplayer_imp14CheckEnterBaseEvEN2op11GetDurationEv" },
    { addr: "0x08626E86", name: "装备外形重置", duration: 20, symbol: "_ZZN11gplayer_imp21PlayerEquipShapeResetEiiEN2op11GetDurationEv" },
    { addr: "0x08626DBE", name: "装备外形复制", duration: 20, symbol: "_ZZN11gplayer_imp20PlayerEquipShapeCopyEiiiEN2op11GetDurationEv" },
];
      
// 应用进度条加速
function applyProgressBarHooks() {
    progressBarHooks.forEach(hook => {
        try {
            const funcAddr = ptr(hook.addr);
            Interceptor.attach(funcAddr, {
                onEnter: function(args) {
                    // if (hook.name) {
                    //     console.log(`${hook.name}读条被调用`);
                    // }
                },
                onLeave: function(retval) {
                    const duration = hook.duration || 20;
                    retval.replace(ptr(duration));
                    // 为了保持控制台清洁，可以注释掉成功的日志
                    // if (hook.name) {
                    //     console.log(`${hook.name}读条修改后的返回值: ${duration}`);
                    // }
                }
            });
        } catch(e) {
            console.error(`[GS] Failed to hook ${hook.name} (${hook.addr}):`, e);
        }
    });
    console.log("[GS] 进度条加速已镶嵌");
}

module.exports = {
    applyProgressBarHooks
};
},{}],12:[function(require,module,exports){
const { applyKillMonitor, applyRuneScoreHook, applyCombatStateHook, applyOtherHooks, applyChatHook } = require('./utils');
const { applyProgressBarHooks } = require('./duration');
const { applyReinforceRates, applyPropertyRates } = require('./reinforce_simple');
const { mountDelayHook } = require('./mountDelay');
const { applyUpgradeRates } = require('./AstrologyUpgradeRate');
const skillNoLimit = require('./skillNoLimit');


// 初始化所有gs相关的hook
function initGSHooks() {
    try {
        // 初始化击杀监控
        // applyKillMonitor();

        // 初始化物品使用监控
        // initItemUsageMonitor();

        // 初始化物品获取监控
        // initItemGainMonitor();

        console.log('[System] GS hooks initialized');
    } catch (e) {
        console.error('[Init] Error:', e);
    }
}

function init() {
    try {
        // 初始化符文合成修改
        // initFuwen();

        // 初始化物品拾取监控
        // initItemPickupMonitor();

        // 初始化物品获取监控
        // initItemGainMonitor();

        // 初始化在线奖励系统
        // onlineRewards.init();
        // console.log('[GS] 在线奖励系统已启动');

        // 再次确保装备效果监控系统已启动
        // try {
        //     if (!equipmentEffectMonitor.isInitialized) {
        //         equipmentEffectMonitor.init();
        //         console.log('[GS] 装备效果监控系统已启动');
        //     }
        // } catch (e) {
        //     console.error('[GS] 装备效果监控系统启动失败:', e);
        // }

    } catch (e) {
        console.error('[GS] Init error:', e);
        return false;
    }
}

// 初始化所有监控器
function initAllMonitors() {
    // try {
    // ... existing code ...

    // 初始化物品获取监控
    // if (initItemGainMonitor()) {
    //     console.log('[Monitor] 物品获取监控初始化成功');
    // } else {
    //     console.error('[Monitor] 物品获取监控初始化失败');
    // }

    // ... existing code ...
    // } catch (e) {
    //     console.error('[Monitor] 初始化监控器失败:', e);
    // }
}

module.exports = {
    initGSHooks,
    init,
};

rpc.exports = {
    init(stage) {
        // try {
        //     console.log("[GS] 开始安装功能...");

        //     // 初始化击杀监控
        //     try {
        //         applyKillMonitor();
        //         console.log('[GS] 击杀监控系统已启动');
        //     } catch (e) {
        //         console.error('[GS] 击杀监控系统安装失败:', e);
        //     }

        //     // 初始化物品使用监控
        //     initItemUsageMonitor();

        //     // 初始化物品拾取监控
        //     initItemPickupMonitor();

        //     // 初始化物品获取监控
        //     try {
        //         initItemGainMonitor();
        //         console.log('[GS] 物品获取监控已启动');
        //     } catch (e) {
        //         console.error('[GS] 物品获取监控启动失败:', e);
        //     }

        //     // 初始化符文合成修改
        //     try {
        //         initFuwen();
        //         console.log('[GS] 符文合成限制移除成功');
        //     } catch (e) {
        //         console.error('[GS] 符文合成限制移除失败:', e);
        //     }

        //     // 初始化装备效果监控系统
        //     try {
        //         equipmentEffectMonitor.init();
        //         console.log('[GS] 装备效果监控系统已启动');
        //     } catch (e) {
        //         console.error('[GS] 装备效果监控系统启动失败:', e);
        //     }


        try {
            applyChatHook();
            console.log('[GS] 聊天监控系统已启动');
        } catch (e) {
            console.error('[GS] 聊天监控系统启动失败:', e);
        }
        applyProgressBarHooks();
        // applyRuneScoreHook();
        applyChatHook();
        applyCombatStateHook();
        applyOtherHooks();
        applyReinforceRates();
        applyPropertyRates();
        // applyUpgradeRates();
        // hookEquipmentRange();
        mountDelayHook.install();

        // 初始化在线奖励系统
        // try {
        //     onlineRewards.init();
        //     console.log('[GS] 在线奖励系统已启动');
        // } catch (e) {
        //     console.error('[GS] 在线奖励系统启动失败:', e);
        // }
        // 初始化技能无职业限制功能
        try {
            skillNoLimit.init();
            console.log('[GS] 技能职业限制已移除');
        } catch (e) {
            console.error('[GS] 技能职业限制移除失败:', e);
        }

        console.log('[GS] 所有功能已成功安装');
        return true;
        // } catch (e) {
        //     console.error('[GS] Init error:', e);
        //     return false;
        // }
    }
};

},{"./AstrologyUpgradeRate":10,"./duration":11,"./mountDelay":13,"./reinforce_simple":14,"./skillNoLimit":15,"./utils":16}],13:[function(require,module,exports){
const mountDelayHook = {
    install() {
            // 获取基址
            const baseAddr = Module.findBaseAddress('gs');
            const offset = 0x991A9A; 
            const delayAddress = baseAddr.add(offset);
            const originalValue = delayAddress.add(4).readU32();
            Memory.protect(delayAddress, 8, 'rwx');
            const mountDelay = 1000;  // 设置为1000ms (1秒)
            delayAddress.add(4).writeU32(mountDelay);
    }
};

module.exports = {
    mountDelayHook
}; 
},{}],14:[function(require,module,exports){
const GPlayerImp = require('../classes/GPlayerImp');

// ==================== 配置区域 ====================
// 功能开关配置
const CONFIG = {
    // 是否启用角色专属配置 (true = 优先使用角色专属配置)
    enablePlayerSpecific: true,

    // 调试模式 (显示详细日志)
    debugMode: false,
};

// 角色专属配置表 (根据角色ID)
const playerSpecificRates = {
    // 角色ID: { reinforce: 炼器概率, property: 灌注概率 }
    "2705": {
        reinforce: {
            // 炼器概率 - 超高成功率，概率总和=1.01
            0: [0.98, 0.0, 0.0, 0.03, 0.0],    // 98%成功 + 3%爆装 = 1.01
            1: [-0.050000001, 0.0, 0.0, 1.051, 0.0],
            2: [0.96, 0.0, 0.0, 0.05, 0.0],    // 96%成功 + 5%爆装 = 1.01
            3: [0.95, 0.0, 0.0, 0.06, 0.0],    // 95%成功 + 6%爆装 = 1.01
            4: [0.94, 0.0, 0.0, 0.07, 0.0],    // 94%成功 + 7%爆装 = 1.01
            5: [0.93, 0.0, 0.0, 0.08, 0.0],    // 93%成功 + 8%爆装 = 1.01
            6: [0.92, 0.0, 0.0, 0.09, 0.0],    // 92%成功 + 9%爆装 = 1.01
            7: [0.91, 0.0, 0.0, 0.10, 0.0],    // 91%成功 + 10%爆装 = 1.01
            8: [0.90, 0.0, 0.0, 0.11, 0.0],    // 90%成功 + 11%爆装 = 1.01
            9: [0.89, 0.0, 0.0, 0.12, 0.0],    // 89%成功 + 12%爆装 = 1.01
            10: [0.88, 0.0, 0.0, 0.13, 0.0],    // 88%成功 + 13%爆装 = 1.01
            11: [0.87, 0.0, 0.0, 0.14, 0.0],    // 87%成功 + 14%爆装 = 1.01
            12: [0.86, 0.0, 0.0, 0.15, 0.0],    // 86%成功 + 15%爆装 = 1.01
            13: [0.85, 0.0, 0.0, 0.16, 0.0],    // 85%成功 + 16%爆装 = 1.01
            14: [0.84, 0.0, 0.0, 0.17, 0.0],    // 84%成功 + 17%爆装 = 1.01
            15: [0.83, 0.0, 0.0, 0.18, 0.0],    // 83%成功 + 18%爆装 = 1.01
            16: [0.82, 0.0, 0.0, 0.19, 0.0],    // 82%成功 + 19%爆装 = 1.01
            17: [0.81, 0.0, 0.0, 0.20, 0.0],    // 81%成功 + 20%爆装 = 1.01
            18: [0.80, 0.0, 0.0, 0.21, 0.0],    // 80%成功 + 21%爆装 = 1.01
            19: [0.79, 0.0, 0.0, 0.22, 0.0],    // 79%成功 + 22%爆装 = 1.01
        },
        property: {
            // 灌注概率 - 超高成功率，概率总和=1.01
            1: [0.99, 0.0, 0.02, 0.0, 0.0],     // 99%成功 + 2%清除 = 1.01
            2: [0.98, 0.0, 0.03, 0.0, 0.0],     // 98%成功 + 3%清除 = 1.01
            3: [0.97, 0.0, 0.04, 0.0, 0.0],     // 97%成功 + 4%清除 = 1.01
            4: [0.96, 0.0, 0.05, 0.0, 0.0],     // 96%成功 + 5%清除 = 1.01
            5: [0.95, 0.0, 0.06, 0.0, 0.0],     // 95%成功 + 6%清除 = 1.01
            6: [0.94, 0.0, 0.07, 0.0, 0.0],     // 94%成功 + 7%清除 = 1.01
            7: [0.93, 0.0, 0.08, 0.0, 0.0],     // 93%成功 + 8%清除 = 1.01
            8: [0.92, 0.0, 0.09, 0.0, 0.0],     // 92%成功 + 9%清除 = 1.01
            9: [0.91, 0.0, 0.10, 0.0, 0.0],     // 91%成功 + 10%清除 = 1.01
            10: [0.90, 0.0, 0.11, 0.0, 0.0]     // 90%成功 + 11%清除 = 1.01
        }
    },

    // 为角色ID 2720 添加专属配置 (超高成功率)
    "2720": {
        reinforce: {
            // 炼器概率 - 超高成功率，概率总和=1.01
            0: [0.98, 0.0, 0.0, 0.03, 0.0],    // 98%成功 + 3%爆装 = 1.01
            1: [0.98, 0.0, 0.0, 0.03, 0.0],
            2: [0.96, 0.0, 0.0, 0.05, 0.0],    // 96%成功 + 5%爆装 = 1.01
            3: [-0.050000001, 0.0, 0.0, 1.051, 0.0],    // 95%成功 + 6%爆装 = 1.01
            4: [0.94, 0.0, 0.0, 0.07, 0.0],    // 94%成功 + 7%爆装 = 1.01
            5: [0.93, 0.0, 0.0, 0.08, 0.0],    // 93%成功 + 8%爆装 = 1.01
            6: [0.92, 0.0, 0.0, 0.09, 0.0],    // 92%成功 + 9%爆装 = 1.01
            7: [0.91, 0.0, 0.0, 0.10, 0.0],    // 91%成功 + 10%爆装 = 1.01
            8: [0.90, 0.0, 0.0, 0.11, 0.0],    // 90%成功 + 11%爆装 = 1.01
            9: [0.89, 0.0, 0.0, 0.12, 0.0],    // 89%成功 + 12%爆装 = 1.01
            10: [0.88, 0.0, 0.0, 0.13, 0.0],    // 88%成功 + 13%爆装 = 1.01
            11: [0.87, 0.0, 0.0, 0.14, 0.0],    // 87%成功 + 14%爆装 = 1.01
            12: [0.86, 0.0, 0.0, 0.15, 0.0],    // 86%成功 + 15%爆装 = 1.01
            13: [0.85, 0.0, 0.0, 0.16, 0.0],    // 85%成功 + 16%爆装 = 1.01
            14: [0.84, 0.0, 0.0, 0.17, 0.0],    // 84%成功 + 17%爆装 = 1.01
            15: [0.83, 0.0, 0.0, 0.18, 0.0],    // 83%成功 + 18%爆装 = 1.01
            16: [0.82, 0.0, 0.0, 0.19, 0.0],    // 82%成功 + 19%爆装 = 1.01
            17: [0.81, 0.0, 0.0, 0.20, 0.0],    // 81%成功 + 20%爆装 = 1.01
            18: [0.80, 0.0, 0.0, 0.21, 0.0],    // 80%成功 + 21%爆装 = 1.01
            19: [0.79, 0.0, 0.0, 0.22, 0.0],    // 79%成功 + 22%爆装 = 1.01
        },
        property: {
            // 灌注概率 - 超高成功率，概率总和=1.01
            1: [0.99, 0.0, 0.02, 0.0, 0.0],     // 99%成功 + 2%清除 = 1.01
            2: [0.98, 0.0, 0.03, 0.0, 0.0],     // 98%成功 + 3%清除 = 1.01
            3: [0.97, 0.0, 0.04, 0.0, 0.0],     // 97%成功 + 4%清除 = 1.01
            4: [0.96, 0.0, 0.05, 0.0, 0.0],     // 96%成功 + 5%清除 = 1.01
            5: [0.95, 0.0, 0.06, 0.0, 0.0],     // 95%成功 + 6%清除 = 1.01
            6: [0.94, 0.0, 0.07, 0.0, 0.0],     // 94%成功 + 7%清除 = 1.01
            7: [0.93, 0.0, 0.08, 0.0, 0.0],     // 93%成功 + 8%清除 = 1.01
            8: [0.92, 0.0, 0.09, 0.0, 0.0],     // 92%成功 + 9%清除 = 1.01
            9: [0.91, 0.0, 0.10, 0.0, 0.0],     // 91%成功 + 10%清除 = 1.01
            10: [0.90, 0.0, 0.11, 0.0, 0.0]      // 90%成功 + 11%清除 = 1.01
        }
    }
    // 可以添加更多角色ID...
};

// 默认炼器概率配置表
const defaultReinforceRates = {
    // level: [成功率, 降级率, 清除属性率, 爆装率, 不降级率] - 前4个总和=1.01
    1: [0.85, 0.0, 0.0, 0.16, 0.0],      // 85%成功 + 16%爆装 = 1.01
    2: [0.60, 0.0, 0.0, 0.41, 0.0],      // 60%成功 + 41%爆装 = 1.01  
    3: [0.55, 0.0, 0.0, 0.46, 0.0],      // 55%成功 + 46%爆装 = 1.01
    4: [0.50, 0.0, 0.0, 0.51, 0.0],      // 50%成功 + 51%爆装 = 1.01
    5: [0.50, 0.0, 0.0, 0.51, 0.0],      // 50%成功 + 51%爆装 = 1.01
    6: [0.40, 0.0, 0.0, 0.61, 0.0],      // 40%成功 + 61%爆装 = 1.01
    7: [0.35, 0.0, 0.0, 0.66, 0.0],      // 35%成功 + 66%爆装 = 1.01
    8: [0.25, 0.0, 0.0, 0.76, 0.0],      // 25%成功 + 76%爆装 = 1.01
    9: [0.20, 0.0, 0.0, 0.81, 0.0],      // 20%成功 + 81%爆装 = 1.01
    10: [0.15, 0.0, 0.0, 0.86, 0.0],      // 15%成功 + 86%爆装 = 1.01
    11: [0.12, 0.0, 0.0, 0.89, 0.0],     // 12%成功 + 89%爆装 = 1.01
    12: [0.10, 0.0, 0.0, 0.91, 0.0],     // 10%成功 + 91%爆装 = 1.01
    13: [0.10, 0.0, 0.0, 0.91, 0.0],     // 10%成功 + 91%爆装 = 1.01
    14: [0.05, 0.0, 0.0, 0.96, 0.0],     // 5%成功 + 96%爆装 = 1.01
    15: [0.05, 0.0, 0.0, 0.96, 0.0],     // 5%成功 + 96%爆装 = 1.01
    16: [0.00, 0.0, 0.0, 1.01, 0.0],     // 0%成功 + 101%爆装 = 1.01
    17: [0.00, 0.0, 0.0, 1.01, 0.0],     // 0%成功 + 101%爆装 = 1.01
    18: [0.00, 0.0, 0.0, 1.01, 0.0],     // 0%成功 + 101%爆装 = 1.01
    19: [0.00, 0.0, 0.0, 1.01, 0.0],     // 0%成功 + 101%爆装 = 1.01
    20: [0.00, 0.0, 0.0, 1.01, 0.0]      // 0%成功 + 101%爆装 = 1.01
};

// 默认灌注概率配置表
const defaultPropertyRates = {
    // level: [成功率, 降级率, 清除属性率, 爆装率, 不降级率] - 前4个总和=1.01
    1: [0.80, 0.0, 0.21, 0.0, 0.0],      // 80%成功 + 21%清除 = 1.01
    2: [0.70, 0.0, 0.31, 0.0, 0.0],      // 70%成功 + 31%清除 = 1.01
    3: [0.60, 0.0, 0.41, 0.0, 0.0],      // 60%成功 + 41%清除 = 1.01
    4: [0.50, 0.0, 0.51, 0.0, 0.0],      // 50%成功 + 51%清除 = 1.01
    5: [0.40, 0.0, 0.61, 0.0, 0.0],      // 40%成功 + 61%清除 = 1.01
    6: [0.35, 0.0, 0.66, 0.0, 0.0],      // 35%成功 + 66%清除 = 1.01
    7: [0.35, 0.0, 0.66, 0.0, 0.0],      // 35%成功 + 66%清除 = 1.01
    8: [0.30, 0.0, 0.71, 0.0, 0.0],      // 30%成功 + 71%清除 = 1.01
    9: [0.30, 0.0, 0.71, 0.0, 0.0],      // 30%成功 + 71%清除 = 1.01
    10: [0.25, 0.0, 0.76, 0.0, 0.0]       // 25%成功 + 76%清除 = 1.01
};
// ==================== 配置区域结束 ====================

// 从 gactive_imp 指针获取角色ID
function getPlayerIdFromGActiveImp(gactiveImpPtr) {
    try {
        if (!gactiveImpPtr || gactiveImpPtr.isNull()) {
            return null;
        }

        // gactive_imp + 8 -> _parent (gplayer_imp) + 40 -> 角色ID
        const parentPtr = gactiveImpPtr.add(0x8).readPointer();
        if (!parentPtr || parentPtr.isNull()) {
            return null;
        }

        const playerId = parentPtr.add(0x40).readInt();

        // 检查是否是合理的角色ID
        if (playerId && playerId > 0 && playerId < 1000000) {
            return playerId.toString();
        }

        return null;
    } catch (e) {
        return null;
    }
}

// 修改强化概率的函数
function modifyReinforceRate(level, rates) {
    const reinforce_table = ptr('0x091E8A00');
    const entry = reinforce_table.add((level - 1) * 24);

    entry.add(4).writeFloat(rates[0]);   // 成功率
    entry.add(8).writeFloat(rates[1]);   // 降级率
    entry.add(12).writeFloat(rates[2]);  // 清除属性率
    entry.add(16).writeFloat(rates[3]);  // 爆装率
    entry.add(20).writeFloat(rates[4]);  // 不降级率
}

// 修改灌注概率的函数
function modifyPropertyRate(level, rates) {
    const property_table = ptr('0x091E8EE0');
    const entry = property_table.add((level - 1) * 24);

    entry.add(4).writeFloat(rates[0]);   // 成功率
    entry.add(8).writeFloat(rates[1]);   // 降级率
    entry.add(12).writeFloat(rates[2]);  // 清除属性率
    entry.add(16).writeFloat(rates[3]);  // 爆装率
    entry.add(20).writeFloat(rates[4]);  // 不降级率
}

// Hook ReinforceEquipment 函数来实时获取角色ID并应用配置
function hookReinforceEquipment() {
    try {
        // ReinforceEquipment 函数地址 (从你提供的汇编代码)
        const reinforceEquipmentAddr = ptr('0x0889A33A');

        Interceptor.attach(reinforceEquipmentAddr, {
            onEnter: function (args) {
                try {
                    // 根据函数签名: equip_item *this, item *a2, gactive_imp *a3, ...
                    // args[0] = this (equip_item*)
                    // args[1] = a2 (item*)
                    // args[2] = a3 (gactive_imp*)
                    const gactiveImpPtr = args[2];

                    // 从 gactive_imp 获取角色ID
                    const playerId = getPlayerIdFromGActiveImp(gactiveImpPtr);

                    // 如果有专属配置，立即应用
                    if (playerId && playerSpecificRates[playerId]) {
                        console.log(`[强化] 为角色 ${playerId} 应用专属配置`);

                        // 应用炼器专属配置
                        const reinforceConfig = playerSpecificRates[playerId].reinforce;
                        Object.entries(reinforceConfig).forEach(([configLevel, rates]) => {
                            const level = parseInt(configLevel);
                            modifyReinforceRate(level + 1, rates);
                        });

                        // 应用灌注专属配置
                        const propertyConfig = playerSpecificRates[playerId].property;
                        Object.entries(propertyConfig).forEach(([configLevel, rates]) => {
                            const level = parseInt(configLevel);
                            modifyPropertyRate(level, rates);
                        });
                    } else {
                        // 应用默认配置
                        applyDefaultRates();
                    }

                } catch (e) {
                    console.error("[Hook] 错误:", e);
                }
            }
        });

    } catch (e) {
        console.error("[Hook] 安装Hook失败:", e);
        applyDefaultRates();
    }
}

// 应用默认配置
function applyDefaultRates() {
    try {
        // 应用默认炼器配置
        Object.entries(defaultReinforceRates).forEach(([level, rates]) => {
            modifyReinforceRate(parseInt(level), rates);
        });

        // 应用默认灌注配置
        Object.entries(defaultPropertyRates).forEach(([level, rates]) => {
            modifyPropertyRate(parseInt(level), rates);
        });
    } catch (e) {
        console.error("[错误] 应用默认配置失败:", e);
    }
}

// 主要入口函数
function applyReinforceRates() {
    if (CONFIG.enablePlayerSpecific) {
        // 如果启用角色专属配置，使用Hook方式
        hookReinforceEquipment();
    } else {
        // 否则使用默认方式
        applyDefaultRates();
    }
}

function applyPropertyRates() {
    // 灌注概率在 applyReinforceRates 中一起处理了
}

// 配置管理函数
function setConfig(key, value) {
    if (CONFIG.hasOwnProperty(key)) {
        CONFIG[key] = value;
        console.log(`[配置] ${key} 设置为: ${value}`);
        return true;
    } else {
        console.error(`[配置] 无效的配置项: ${key}`);
        return false;
    }
}

function getConfig() {
    return { ...CONFIG };
}

function addPlayerConfig(playerId, reinforceRates, propertyRates) {
    playerSpecificRates[playerId.toString()] = {
        reinforce: reinforceRates,
        property: propertyRates
    };
    console.log(`[配置] 已添加角色专属配置 (ID: ${playerId})`);
}

function removePlayerConfig(playerId) {
    const playerIdStr = playerId.toString();
    if (playerSpecificRates[playerIdStr]) {
        delete playerSpecificRates[playerIdStr];
        console.log(`[配置] 已删除角色专属配置 (ID: ${playerId})`);
        return true;
    } else {
        console.log(`[配置] 未找到角色配置: ID ${playerId}`);
        return false;
    }
}

function listPlayerConfigs() {
    console.log("\n=== 角色专属配置列表 ===");
    const configs = Object.entries(playerSpecificRates);
    if (configs.length === 0) {
        console.log("暂无角色专属配置");
    } else {
        configs.forEach(([playerId]) => {
            console.log(`角色ID: ${playerId}`);
        });
    }
    console.log("========================\n");
}

// 测试系统功能
function testSystem() {
    console.log("\n=== 强化概率系统测试 ===");

    // 测试配置获取
    console.log("当前配置:", getConfig());

    // 测试角色专属配置
    console.log("角色专属配置:");
    listPlayerConfigs();

    console.log("========================\n");
}

module.exports = {
    applyReinforceRates,
    applyPropertyRates,
    setConfig,
    getConfig,
    addPlayerConfig,
    removePlayerConfig,
    listPlayerConfigs,
    testSystem
};

},{"../classes/GPlayerImp":7}],15:[function(require,module,exports){
function removeSkillOccupationLimit() {
    const modules = Process.enumerateModules();
    const gameModule = modules.find(m => m.name.toLowerCase().includes('libskill.so'));
    if (!gameModule) {
        throw new Error('找不到游戏模块');
    }
    // console.log('[SkillNoLimit] 游戏模块基址:', gameModule.base);

    // GNET::IsDiffrentOccupClass 函数的调用点
    const checkFuncCall = gameModule.base.add(0x0135ED6C);  // call _ZN4GNET20IsDiffrentOccupClassEii
    Interceptor.attach(checkFuncCall, {
        onEnter: function (args) {
            // 记录参数用于调试
            this.playerOccup = args[0];
            this.skillOccup = args[1];
        },
        onLeave: function (retval) {
            // 强制返回0，表示职业匹配
            retval.replace(ptr('0x0'));
        }
    });



    // Hook LearnCondition函数
    const learnConditionAddr = gameModule.base.add(0x0135AC56);  // GNET::SkillStub::LearnCondition
    Interceptor.attach(learnConditionAddr, {
        onEnter: function (args) {
            this.thisPtr = args[0];  // SkillStub*
            this.skill = args[1];    // Skill*
        },
        onLeave: function (retval) {
            // 强制返回1，表示所有条件都满足
            retval.replace(ptr('0x1'));
        }
    });
}


// 初始化函数
function init() {
        removeSkillOccupationLimit();
        return true;
}

module.exports = {
    init
}; 
},{}],16:[function(require,module,exports){
(function (Buffer){(function (){
const dataman = require('../classes/Dataman');
const GPlayerImp = require('../classes/GPlayerImp');


// 创建一个Map用于存储每个killerName的击杀次数
let killCounts = new Map();

// 定义一个函数来重置killCounts
function resetKillCounts() {
    killCounts.clear();
    // console.log('[KillCount] 击杀计数已重置');
}

// 设置一个定时器，每60秒重置一次killCounts
setInterval(resetKillCounts, 60 * 1000);

// 保存当前玩家指针
let currentPlayer = null;

// 设置玩家监控
function setupPlayerMonitor() {
    try {
        Interceptor.attach(ptr('0x080DC742'), {
            onEnter(args) {
                if (args[0] && !args[0].isNull()) {
                    currentPlayer = new GPlayerImp(args[0]);
                }
            }
        });
        console.log('[PlayerMonitor] 玩家监控已安装');
    } catch(e) {
        console.error('[PlayerMonitor] 安装玩家监控失败:', e);
    }
}

// 应用击杀监控
function applyKillMonitor() {
    try {
        // 先安装玩家监控
        setupPlayerMonitor();

        Interceptor.attach(ptr('0x0809DE38'), {
            onEnter(args) {
                try {
                    const deadPlayerPtr = args[0];  // this指针，指向死亡玩家
                    const killerXID = args[1];      // attacker指针，指向击杀者的XID结构

                    console.log('[KillMonitor] 死亡事件触发');
                    console.log(`[KillMonitor] 死亡玩家指针: 0x${deadPlayerPtr.toString(16)}`);

                    try {
                        // 获取死亡玩家信息（从this指针）
                        const deadPlayer = new GPlayerImp(deadPlayerPtr);
                        const deadPlayerName = deadPlayer.GetPlayerName();
                        // 获取地图名称
                        const mapName = deadPlayerPtr.add(0x268).readUtf16String(0x100);
                        
                        // 读取XID结构获取击杀者信息
                        const killerType = killerXID.readU32();
                        const killerId = killerXID.add(4).readU32();
                        console.log(`[KillMonitor] 击杀者类型: ${killerType}, ID: ${killerId} (0x${killerId.toString(16)})`);

                        if (killerType === 1) {  // 如果击杀者是玩家
                            // 使用当前玩家指针来获取玩家基址
                            if (currentPlayer && !currentPlayer.pointer.isNull()) {
                                const playerBase = currentPlayer.pointer.sub(currentPlayer.GetPlayerId() * 0x642C);
                                const killerPtr = playerBase.add(killerId * 0x642C);
                                
                                console.log(`[KillMonitor] 击杀者指针: 0x${killerPtr.toString(16)}`);
                                
                                const killerPlayer = new GPlayerImp(killerPtr);
                                const killerName = killerPlayer.GetPlayerName();
                                
                                if (killerName) {
                                    // 更新击杀计数
                                    const currentCount = (killCounts.get(killerName) || 0) + 1;
                                    killCounts.set(killerName, currentCount);
                                    
                                    // 获取当前时间
                                    const now = new Date();
                                    const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
                                    
                                    // 构建击杀信息
                                    let killMsg = `${killerName} 吊打了 ${deadPlayerName}`;
                                    if (currentCount > 1) {
                                        killMsg += ` (开始疯狂连杀${currentCount}次)`;
                                    }
                                    if (mapName) {
                                        killMsg += ` [${mapName}]`;
                                    }
                                    killMsg += ` ${timeStr}`;
                                    
                                    console.log('[KillMonitor] 准备发送消息:', killMsg);
                                    
                                    // 使用死亡玩家的实例发送广播
                                    try {
                                        deadPlayer.broadChatMsg(killMsg, 27);
                                        console.log('[KillMonitor] 消息发送成功');
                                    } catch (e) {
                                        console.error('[KillMonitor] 发送消息失败:', e);
                                    }
                                }
                            }
                        }
                    } catch (e) {
                        console.error('[KillMonitor] 处理玩家信息失败:', e);
                        console.error(e.stack);
                    }
                    
                } catch (e) {
                    console.error('[KillMonitor] Error in onEnter:', e);
                    if (e.stack) {
                        console.error('[KillMonitor] Stack:', e.stack);
                    }
                }
            }
        });
        console.log('[KillMonitor] 击杀监控已启动');
        return true;
    } catch (e) {
        console.error('[KillMonitor] 安装失败:', e);
        return false;
    }
}

// 应用道胎修改
function applyRuneScoreHook() {
    try {
        Interceptor.attach(ptr('0x0875D2BE'), {
            onEnter: function(args) {
                console.log('[AddRuneScore] 原始增加分数:', args[1].toInt32());
                args[1] = ptr(50000);
                console.log('[AddRuneScore] 修改后增加分数: 50000');
            }
        });
        console.log("[GS] 道胎修改已安装");
    } catch(e) {
        console.error("[GS] 道胎修改安装失败:", e);
    }
}

// 应用战斗状态修改
function applyCombatStateHook() {
    try {
        Interceptor.attach(ptr('0x08759CE8'), {
            onEnter: function(args) {
                const thisPtr = args[0];
                args[1] = ptr(0);
                thisPtr.add(0x6E6).writeU8(5);
            }
        });
        console.log("[GS] 战斗状态修改已安装");
    } catch(e) {
        console.error("[GS] 战斗状态修改安装失败:", e);
    }
}

// 应用聊天监控
function applyChatHook() {
    try {
        Interceptor.attach(ptr('0x81E9EA8'), {
            onEnter: function(args) {
                
                    const pImp = args[1];
                    const messageType = args[2];
                    const targetXID = args[3];
                    const param = args[4];
                    const gplayerimp = new GPlayerImp(pImp);
                    
                    if (!pImp.isNull()) {
                       
                        const playerName = gplayerimp.GetPlayerName();
                        
                        if (targetXID && !targetXID.isNull()) {
                            const msgStr = targetXID.readCString();
                            if (msgStr) {
                                try {
                                    const decoded = Buffer.from(msgStr, 'base64');
                                    let message = '';
                                    for (let i = 0; i < decoded.length; i += 2) {
                                        const charCode = decoded[i];
                                        if (charCode !== 0) {
                                            message += String.fromCharCode(charCode);
                                        }
                                    }
                                    
                                    console.log('[PlayerChat]', playerName, '说:', message);
                                    console.log('消息类型:', messageType.toInt32());
                                    console.log('频道:', param.toInt32());
                                    console.log('------------------------');
                                } catch(e) {}
                            }
                        }
                    }
            }
        });
        console.log("[GS] 聊天监控已安装");
    } catch(e) {
        console.error("[GS] 聊天监控安装失败:", e);
    }
}

// 应用其他功能
function applyOtherHooks() {
    // let isModified = false;
    
    // try {
    //     // 星盘容量修改 - 使用NativeCallback正确实现
    //     const starCapacityFunc = new NativeCallback(function(thisPtr) {
    //         try {
    //             if (!isModified && thisPtr && !thisPtr.isNull()) {
    //                 // 修改星盘容量为999
    //                 thisPtr.add(0x8).writeInt(999);
    //                 thisPtr.add(0x1C).writeInt(999);
    //                 isModified = true;
    //                 console.log("[GS] 星盘容量已修改为999");
    //             }
                
    //             // 调用原始函数 (如果需要的话)
    //             // 这里省略原始函数调用，因为我们只需要修改内存
    //         } catch (e) {
    //             console.error("[GS] 星盘容量修改异常:", e);
    //         }
    //     }, 'void', ['pointer']);
        
    //     Interceptor.replace(ptr('0x82C4566'), starCapacityFunc);
    //     console.log("[GS] 星盘容量修改已安装");

        // 坐骑冷却修改 - 使用NativeCallback正确实现
        // const mountCooldownFunc = new NativeCallback(function(arg1, arg2, cooldown) {
        //     // 直接返回1000，表示新的冷却时间
        //     return 1000;
        // }, 'int', ['pointer', 'pointer', 'int']);
        
        // Interceptor.replace(ptr('0x089D98A4'), mountCooldownFunc);
        // console.log("[GS] 坐骑冷却修改已安装");

    //     // 移除捡物品限制 - 使用完整的函数替代
    //     const pickupLimit1Func = new NativeCallback(function(thisPtr) {
    //         // 直接跳过检查逻辑，不执行任何操作
    //         return;
    //     }, 'void', ['pointer']);
        
    //     Interceptor.replace(ptr('0x812E61C'), pickupLimit1Func);
    //     console.log("[GS] 物品拾取限制1已移除");

    //     const pickupLimit2Func = new NativeCallback(function(thisPtr) {
    //         // 直接跳过检查逻辑，不执行任何操作
    //         return;
    //     }, 'void', ['pointer']);
        
    //     Interceptor.replace(ptr('0x812E73F'), pickupLimit2Func);
    //     console.log("[GS] 物品拾取限制2已移除");

    //     console.log("[GS] 其他功能已安装");
    // } catch(e) {
    //     console.error("[GS] 其他功能安装失败:", e);
    // }
}


// 导出所有函数
module.exports = {
    applyKillMonitor,
    applyRuneScoreHook,
    applyCombatStateHook,
    applyChatHook,
    applyOtherHooks,
}; 
}).call(this)}).call(this,require("buffer").Buffer)

},{"../classes/Dataman":6,"../classes/GPlayerImp":7,"buffer":2}]},{},[12])
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
