const { activateAllCommonSkills} = require('./skills/通用/index');
const { activateAllQingYunSkills } = require('./skills/人族/青云/index'); 
const { activateAllTianYinSkills} = require('./skills/人族/天音/index');
const { activateAllGuiWangSkills} = require('./skills/人族/鬼王/index');
const { activateAllHeHuanSkills} = require('./skills/人族/合欢/index');
const { activateAllGuiDaoSkills} = require('./skills/人族/鬼道/index');
const { activateAllFenxiangSkills} = require('./skills/人族/焚香/index');

const { activateAllJiuLiSkills} = require('./skills/神族/九黎/index');
const { activateAllLieShanSkills} = require('./skills/神族/烈山/index');
const { activateAllHuaiGuangSkills} = require('./skills/神族/怀光/index');
const { activateAllTianHuaSkills} = require('./skills/神族/天华/index');
const { activateAllTaiHuoSkills} = require('./skills/神族/太昊/index');
const { activateAllChenHuangSkills} = require('./skills/神族/辰皇/index');

const { activateAllYingZhaoSkills} = require('./skills/天脉/英招/index');
const {activateAllQianJiSkills} = require('./skills/天脉/牵机/index');
const { activateAllPoJunSkills} = require('./skills/天脉/破军/index');
const { activateAllQingLuoSkills} = require('./skills/天脉/青罗/index');
const { activateAllGuiYunSkills} = require('./skills/天脉/归云/index');


//1715炼器表地址: 0x091E8A04 灌注表地址: 0x091E8EE0

// 导入一键拾取系统 (备用)
// const GPlayerImp = require('./classes/GPlayerImp');

// 初始化所有技能

activateAllCommonSkills();  // 通用
activateAllQingYunSkills();  // 青云
activateAllTianYinSkills();  // 天音
activateAllGuiWangSkills();  // 鬼王
activateAllHeHuanSkills();  // 合欢
activateAllGuiDaoSkills();  // 鬼道
activateAllFenxiangSkills(); //焚香

activateAllJiuLiSkills(); //九黎
activateAllLieShanSkills(); //烈山
activateAllHuaiGuangSkills();  // 怀光
activateAllTianHuaSkills(); //天华
activateAllTaiHuoSkills(); //太昊
activateAllChenHuangSkills(); //辰皇

activateAllQianJiSkills();//牵机
activateAllYingZhaoSkills();//英招
activateAllPoJunSkills();//破军
activateAllQingLuoSkills();//青罗
activateAllGuiYunSkills();//归云

// 激活基于world::ForEachSlice的一键拾取系统
console.log('🎯 正在激活基于world::ForEachSlice的一键拾取系统...');
try {
    // 导入并激活基于world::ForEachSlice的拾取系统
    const worldForEachPickup = require('./simple_pickup.js');

    console.log('✅ 基于world::ForEachSlice的一键拾取系统已激活！');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('🔧 使用游戏内部的ForEachSlice函数');
    console.log('📏 sphere_collector: 0x0859E5FA');
    console.log('📏 object_collector: 0x0859DA8C');
    console.log('🎯 学习游戏的对象收集机制');
    console.log('⚡ 最接近游戏原生的实现方式');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('');
    console.log('🎮 一键拾取工作原理:');
    console.log('1. Hook游戏的ForEachSlice函数');
    console.log('2. 学习游戏如何收集对象');
    console.log('3. 玩家拾取时触发对象收集');
    console.log('4. 使用学到的方法收集并拾取所有物品');
    console.log('');
    console.log('🔧 控制命令:');
    console.log('• 查看状态: require("./world_foreach_pickup.js").status()');
    console.log('• 停用系统: require("./world_foreach_pickup.js").deactivate()');

    // 将worldForEachPickup导出到全局，方便控制
    global.worldForEachPickup = worldForEachPickup;
    console.log('• 全局访问: worldForEachPickup.status()');

} catch (error) {
    console.log('❌ 基于world::ForEachSlice的一键拾取系统激活出错:', error);
    console.log('💡 回退到安全调试版本...');

    // 回退到安全调试版本
    try {
        const safePickup = require('./safe_pickup.js');
        console.log('✅ 安全调试版本已激活！');
        console.log('🔧 使用逐步测试方法定位问题');
        global.safePickup = safePickup;
    } catch (fallbackError) {
        console.log('❌ 安全版本也失败:', fallbackError);
        console.log('💡 请手动运行: require("./safe_pickup.js")');
    }
}






